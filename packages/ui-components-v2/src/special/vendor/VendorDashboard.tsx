import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  <PERSON>ton,
  ButtonText,
  Card,
  CardHeader,
  CardBody,
  Badge,
  BadgeText,
  Grid,
  GridItem,
  Progress,
  ProgressFilledTrack,
  Icon,
  Divider,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Image,
  Pressable,
} from "@/ui";
import { 
  TrendingUpIcon, 
  ShoppingBagIcon, 
  DollarSignIcon, 
  StarIcon,
  PackageIcon,
  UsersIcon
} from "@/ui";

export interface VendorStats {
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  activeProducts: number;
  pendingOrders: number;
  monthlyGrowth: number;
}

export interface RecentOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  amount: number;
  status: "pending" | "confirmed" | "shipped" | "delivered";
  date: string;
}

export interface TopProduct {
  id: string;
  name: string;
  image: string;
  sales: number;
  revenue: number;
  rating: number;
}

export interface VendorDashboardProps {
  /** Vendor information */
  vendor?: {
    id: string;
    name: string;
    avatar?: string;
    joinDate: string;
    status: "active" | "pending" | "suspended";
  };
  /** Dashboard statistics */
  stats?: VendorStats;
  /** Recent orders */
  recentOrders?: RecentOrder[];
  /** Top performing products */
  topProducts?: TopProduct[];
  /** Loading state */
  loading?: boolean;
  /** Action handlers */
  onAddProduct?: () => void;
  onViewAllOrders?: () => void;
  onViewAllProducts?: () => void;
  onViewAnalytics?: () => void;
  onOrderClick?: (orderId: string) => void;
  onProductClick?: (productId: string) => void;
}

const mockStats: VendorStats = {
  totalProducts: 45,
  totalOrders: 128,
  totalRevenue: 15420.50,
  averageRating: 4.6,
  totalReviews: 89,
  activeProducts: 42,
  pendingOrders: 7,
  monthlyGrowth: 12.5
};

const mockRecentOrders: RecentOrder[] = [
  {
    id: "1",
    orderNumber: "ORD-2024-001",
    customerName: "John Doe",
    amount: 299.99,
    status: "pending",
    date: "2024-03-15"
  },
  {
    id: "2", 
    orderNumber: "ORD-2024-002",
    customerName: "Jane Smith",
    amount: 149.50,
    status: "shipped",
    date: "2024-03-14"
  }
];

const mockTopProducts: TopProduct[] = [
  {
    id: "1",
    name: "Wireless Headphones Pro",
    image: "https://via.placeholder.com/60",
    sales: 45,
    revenue: 2250.00,
    rating: 4.8
  },
  {
    id: "2",
    name: "Smart Watch Series X",
    image: "https://via.placeholder.com/60", 
    sales: 32,
    revenue: 1920.00,
    rating: 4.5
  }
];

const VendorDashboard = React.memo<VendorDashboardProps>(({
  vendor = {
    id: "1",
    name: "TechStore Pro",
    joinDate: "2024-01-15",
    status: "active"
  },
  stats = mockStats,
  recentOrders = mockRecentOrders,
  topProducts = mockTopProducts,
  loading = false,
  onAddProduct,
  onViewAllOrders,
  onViewAllProducts,
  onViewAnalytics,
  onOrderClick,
  onProductClick
}) => {
  const getStatusBadge = (status: string) => {
    const variants = {
      active: { color: "bg-green-500", text: "Active" },
      pending: { color: "bg-yellow-500", text: "Pending Approval" },
      suspended: { color: "bg-red-500", text: "Suspended" }
    };

    const config = variants[status as keyof typeof variants];
    return (
      <Badge className={`${config.color} text-white`}>
        <BadgeText className="text-white">{config.text}</BadgeText>
      </Badge>
    );
  };

  const getOrderStatusBadge = (status: RecentOrder["status"]) => {
    const variants = {
      pending: { color: "bg-yellow-500" },
      confirmed: { color: "bg-blue-500" },
      shipped: { color: "bg-indigo-500" },
      delivered: { color: "bg-green-500" }
    };

    const config = variants[status];
    return (
      <Badge className={`${config.color} text-white`}>
        <BadgeText className="text-white capitalize">{status}</BadgeText>
      </Badge>
    );
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Icon
        key={index}
        as={StarIcon}
        size="2xs"
        className={index < Math.floor(rating) ? "fill-current" : "fill-gray-300"}
        color={index < Math.floor(rating) ? "#FCD34D" : "#D1D5DB"}
      />
    ));
  };

  return (
    <VStack space="lg" className="p-6">
      {/* Header */}
      <VStack space="md">
        <HStack className="justify-between items-start">
          <HStack space="md" className="items-center">
            <Avatar size="lg">
              <AvatarFallbackText>{vendor.name}</AvatarFallbackText>
              {vendor.avatar && <AvatarImage source={{ uri: vendor.avatar }} />}
            </Avatar>
            <VStack>
              <Heading size="xl">{vendor.name}</Heading>
              <Text className="text-typography-600">
                Member since {new Date(vendor.joinDate).toLocaleDateString()}
              </Text>
              {getStatusBadge(vendor.status)}
            </VStack>
          </HStack>
          
          <Button onPress={onAddProduct}>
            <ButtonText>Add New Product</ButtonText>
          </Button>
        </HStack>
      </VStack>

      {/* Stats Overview */}
      <Grid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <GridItem>
          <Card>
            <CardBody>
              <HStack space="md" className="items-center">
                <VStack className="bg-blue-100 p-3 rounded-full">
                  <Icon as={PackageIcon} className="text-blue-600" />
                </VStack>
                <VStack>
                  <Text className="text-2xl font-bold">{stats.totalProducts}</Text>
                  <Text className="text-sm text-typography-600">Total Products</Text>
                  <Text className="text-xs text-green-600">
                    {stats.activeProducts} active
                  </Text>
                </VStack>
              </HStack>
            </CardBody>
          </Card>
        </GridItem>

        <GridItem>
          <Card>
            <CardBody>
              <HStack space="md" className="items-center">
                <VStack className="bg-green-100 p-3 rounded-full">
                  <Icon as={ShoppingBagIcon} className="text-green-600" />
                </VStack>
                <VStack>
                  <Text className="text-2xl font-bold">{stats.totalOrders}</Text>
                  <Text className="text-sm text-typography-600">Total Orders</Text>
                  <Text className="text-xs text-yellow-600">
                    {stats.pendingOrders} pending
                  </Text>
                </VStack>
              </HStack>
            </CardBody>
          </Card>
        </GridItem>

        <GridItem>
          <Card>
            <CardBody>
              <HStack space="md" className="items-center">
                <VStack className="bg-purple-100 p-3 rounded-full">
                  <Icon as={DollarSignIcon} className="text-purple-600" />
                </VStack>
                <VStack>
                  <Text className="text-2xl font-bold">
                    ${stats.totalRevenue.toLocaleString()}
                  </Text>
                  <Text className="text-sm text-typography-600">Total Revenue</Text>
                  <HStack space="xs" className="items-center">
                    <Icon as={TrendingUpIcon} size="xs" className="text-green-600" />
                    <Text className="text-xs text-green-600">
                      +{stats.monthlyGrowth}% this month
                    </Text>
                  </HStack>
                </VStack>
              </HStack>
            </CardBody>
          </Card>
        </GridItem>

        <GridItem>
          <Card>
            <CardBody>
              <HStack space="md" className="items-center">
                <VStack className="bg-yellow-100 p-3 rounded-full">
                  <Icon as={StarIcon} className="text-yellow-600" />
                </VStack>
                <VStack>
                  <Text className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</Text>
                  <Text className="text-sm text-typography-600">Average Rating</Text>
                  <Text className="text-xs text-typography-500">
                    {stats.totalReviews} reviews
                  </Text>
                </VStack>
              </HStack>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>

      <Grid className="grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <GridItem>
          <Card>
            <CardHeader>
              <HStack className="justify-between items-center">
                <Heading size="md">Recent Orders</Heading>
                <Button size="sm" variant="outline" onPress={onViewAllOrders}>
                  <ButtonText>View All</ButtonText>
                </Button>
              </HStack>
            </CardHeader>
            <CardBody>
              <VStack space="sm">
                {recentOrders.map((order, index) => (
                  <VStack key={order.id}>
                    <Pressable onPress={() => onOrderClick?.(order.id)} className="w-full">
                      <HStack className="justify-between items-center w-full cursor-pointer hover:bg-background-50 p-2 rounded">
                      <VStack>
                        <Text className="font-medium">{order.orderNumber}</Text>
                        <Text className="text-sm text-typography-600">
                          {order.customerName}
                        </Text>
                        <Text className="text-xs text-typography-500">
                          {new Date(order.date).toLocaleDateString()}
                        </Text>
                      </VStack>
                      <VStack className="items-end">
                        <Text className="font-bold">${order.amount.toFixed(2)}</Text>
                        {getOrderStatusBadge(order.status)}
                      </VStack>
                      </HStack>
                    </Pressable>
                    {index < recentOrders.length - 1 && <Divider />}
                  </VStack>
                ))}
              </VStack>
            </CardBody>
          </Card>
        </GridItem>

        {/* Top Products */}
        <GridItem>
          <Card>
            <CardHeader>
              <HStack className="justify-between items-center">
                <Heading size="md">Top Products</Heading>
                <Button size="sm" variant="outline" onPress={onViewAllProducts}>
                  <ButtonText>View All</ButtonText>
                </Button>
              </HStack>
            </CardHeader>
            <CardBody>
              <VStack space="sm">
                {topProducts.map((product, index) => (
                  <VStack key={product.id}>
                    <Pressable onPress={() => onProductClick?.(product.id)} className="w-full">
                      <HStack 
                        space="md" 
                        className="items-center w-full cursor-pointer hover:bg-background-50 p-2 rounded"
                      >
                      <Image
                        source={{ uri: product.image }}
                        alt={product.name}
                        className="w-12 h-12 rounded-lg"
                      />
                      <VStack className="flex-1">
                        <Text className="font-medium" numberOfLines={1}>
                          {product.name}
                        </Text>
                        <HStack space="xs" className="items-center">
                          {renderStars(product.rating)}
                          <Text className="text-xs text-typography-500">
                            ({product.rating.toFixed(1)})
                          </Text>
                        </HStack>
                        <Text className="text-sm text-typography-600">
                          {product.sales} sales
                        </Text>
                      </VStack>
                      <VStack className="items-end">
                        <Text className="font-bold text-green-600">
                          ${product.revenue.toFixed(2)}
                        </Text>
                      </VStack>
                      </HStack>
                    </Pressable>
                    {index < topProducts.length - 1 && <Divider />}
                  </VStack>
                ))}
              </VStack>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <Heading size="md">Quick Actions</Heading>
        </CardHeader>
        <CardBody>
          <Grid className="grid-cols-2 md:grid-cols-4 gap-4">
            <GridItem>
              <Button variant="outline" onPress={onAddProduct} className="w-full">
                <ButtonText>Add Product</ButtonText>
              </Button>
            </GridItem>
            <GridItem>
              <Button variant="outline" onPress={onViewAllOrders} className="w-full">
                <ButtonText>Manage Orders</ButtonText>
              </Button>
            </GridItem>
            <GridItem>
              <Button variant="outline" onPress={onViewAnalytics} className="w-full">
                <ButtonText>View Analytics</ButtonText>
              </Button>
            </GridItem>
            <GridItem>
              <Button variant="outline" className="w-full">
                <ButtonText>Settings</ButtonText>
              </Button>
            </GridItem>
          </Grid>
        </CardBody>
      </Card>
    </VStack>
  );
});

VendorDashboard.displayName = "VendorDashboard";
export default VendorDashboard;