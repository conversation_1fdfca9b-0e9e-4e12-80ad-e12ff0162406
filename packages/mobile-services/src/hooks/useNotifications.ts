"use client"

import { useState, useEffect, useCallback } from 'react'
import * as Notifications from 'expo-notifications'
import { NotificationService, NotificationData } from '../services/NotificationService'
import { AppwriteService } from '../services/AppwriteService'

export interface UseNotificationsOptions {
  enableAppwriteSync?: boolean
  onNotificationReceived?: (notification: Notifications.Notification) => void
  onNotificationResponse?: (response: Notifications.NotificationResponse) => void
  onError?: (error: Error) => void
}

export interface UseNotificationsReturn {
  pushToken: string | null
  isInitialized: boolean
  isLoading: boolean
  error: Error | null
  scheduleNotification: (notification: NotificationData, trigger?: Notifications.NotificationTriggerInput) => Promise<string | null>
  cancelNotification: (identifier: string) => Promise<void>
  cancelAllNotifications: () => Promise<void>
  setBadgeCount: (count: number) => Promise<void>
  getBadgeCount: () => Promise<number>
  clearAllNotifications: () => Promise<void>
  sendOrderNotification: (orderId: string, status: string, message: string, data?: Record<string, any>) => Promise<string | null>
  sendDeliveryNotification: (title: string, message: string, data?: Record<string, any>) => Promise<string | null>
  sendPromotionNotification: (title: string, message: string, data?: Record<string, any>) => Promise<string | null>
}

/**
 * useNotifications hook - React hook for notification services with Appwrite integration
 * 
 * Features:
 * - Push notification management
 * - Local notification scheduling
 * - Token synchronization with Appwrite
 * - Notification categories and actions
 * - Badge count management
 * - Deep linking from notifications
 */
export function useNotifications(options: UseNotificationsOptions = {}): UseNotificationsReturn {
  const [pushToken, setPushToken] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const notificationService = NotificationService.getInstance()
  const appwriteService = options.enableAppwriteSync ? AppwriteService.getInstance() : null

  // Initialize notification service
  useEffect(() => {
    const initializeNotificationService = async () => {
      try {
        setIsLoading(true)
        setError(null)

        await notificationService.initialize({
          onNotificationReceived: handleNotificationReceived,
          onNotificationResponse: handleNotificationResponse,
          onTokenReceived: handleTokenReceived,
          onError: handleError,
        })

        setIsInitialized(true)
      } catch (err) {
        handleError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeNotificationService()
  }, [])

  // Handle notification received
  const handleNotificationReceived = useCallback(
    (notification: Notifications.Notification) => {
      options.onNotificationReceived?.(notification)
    },
    [options.onNotificationReceived]
  )

  // Handle notification response
  const handleNotificationResponse = useCallback(
    (response: Notifications.NotificationResponse) => {
      options.onNotificationResponse?.(response)
      
      // Handle deep linking based on notification data
      const data = response.notification.request.content.data
      if (data && data.type) {
        handleNotificationDeepLink(data)
      }
    },
    [options.onNotificationResponse]
  )

  // Handle token received
  const handleTokenReceived = useCallback(
    async (token: string) => {
      setPushToken(token)

      // Sync token with Appwrite if enabled
      if (options.enableAppwriteSync && appwriteService) {
        try {
          const platform = require('expo-device').osName === 'iOS' ? 'ios' : 'android'
          await appwriteService.storePushToken(token, platform)
        } catch (err) {
          console.warn('Failed to sync push token with Appwrite:', err)
        }
      }
    },
    [options.enableAppwriteSync, appwriteService]
  )

  // Handle errors
  const handleError = useCallback(
    (err: Error) => {
      setError(err)
      options.onError?.(err)
    },
    [options.onError]
  )

  // Handle notification deep linking
  const handleNotificationDeepLink = useCallback((data: Record<string, any>) => {
    switch (data.type) {
      case 'order_update':
        // Navigate to order details
        console.log('Navigate to order:', data.orderId)
        break
      case 'delivery_update':
        // Navigate to delivery tracking
        console.log('Navigate to delivery tracking:', data)
        break
      case 'promotion':
        // Navigate to promotion details
        console.log('Navigate to promotion:', data)
        break
      default:
        console.log('Unknown notification type:', data.type)
    }
  }, [])

  // Schedule notification
  const scheduleNotification = useCallback(
    async (
      notification: NotificationData,
      trigger?: Notifications.NotificationTriggerInput
    ): Promise<string | null> => {
      try {
        setError(null)
        return await notificationService.scheduleLocalNotification(notification, trigger)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  // Cancel notification
  const cancelNotification = useCallback(
    async (identifier: string): Promise<void> => {
      try {
        setError(null)
        await notificationService.cancelNotification(identifier)
      } catch (err) {
        handleError(err as Error)
      }
    },
    [handleError]
  )

  // Cancel all notifications
  const cancelAllNotifications = useCallback(async (): Promise<void> => {
    try {
      setError(null)
      await notificationService.cancelAllNotifications()
    } catch (err) {
      handleError(err as Error)
    }
  }, [handleError])

  // Set badge count
  const setBadgeCount = useCallback(
    async (count: number): Promise<void> => {
      try {
        setError(null)
        await notificationService.setBadgeCount(count)
      } catch (err) {
        handleError(err as Error)
      }
    },
    [handleError]
  )

  // Get badge count
  const getBadgeCount = useCallback(async (): Promise<number> => {
    try {
      setError(null)
      return await notificationService.getBadgeCount()
    } catch (err) {
      handleError(err as Error)
      return 0
    }
  }, [handleError])

  // Clear all notifications
  const clearAllNotifications = useCallback(async (): Promise<void> => {
    try {
      setError(null)
      await notificationService.clearAllNotifications()
    } catch (err) {
      handleError(err as Error)
    }
  }, [handleError])

  // Send order notification
  const sendOrderNotification = useCallback(
    async (
      orderId: string,
      status: string,
      message: string,
      data?: Record<string, any>
    ): Promise<string | null> => {
      try {
        setError(null)
        return await notificationService.sendOrderNotification(orderId, status, message, data)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  // Send delivery notification
  const sendDeliveryNotification = useCallback(
    async (
      title: string,
      message: string,
      data?: Record<string, any>
    ): Promise<string | null> => {
      try {
        setError(null)
        return await notificationService.sendDeliveryNotification(title, message, data)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  // Send promotion notification
  const sendPromotionNotification = useCallback(
    async (
      title: string,
      message: string,
      data?: Record<string, any>
    ): Promise<string | null> => {
      try {
        setError(null)
        return await notificationService.sendPromotionNotification(title, message, data)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  return {
    pushToken,
    isInitialized,
    isLoading,
    error,
    scheduleNotification,
    cancelNotification,
    cancelAllNotifications,
    setBadgeCount,
    getBadgeCount,
    clearAllNotifications,
    sendOrderNotification,
    sendDeliveryNotification,
    sendPromotionNotification,
  }
}
