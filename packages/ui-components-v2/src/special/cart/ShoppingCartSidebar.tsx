import React from "react";
import {
  V<PERSON><PERSON>ck,
  HStack,
  Heading,
  Text,
  Button,
  ButtonText,
  Card,
  CardBody,
  Image,
  Pressable,
  Divider,
  Badge,
  BadgeText,
  Icon,
  Input,
  InputField,
} from "@/ui";
import { MinusIcon, PlusIcon, TrashIcon } from "@/ui";

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  image: string;
  vendor: {
    id: string;
    name: string;
  };
  inStock: boolean;
  maxQuantity?: number;
}

export interface ShoppingCartSidebarProps {
  /** Cart items */
  items?: CartItem[];
  /** Loading state */
  loading?: boolean;
  /** Cart actions */
  onUpdateQuantity?: (itemId: string, quantity: number) => void;
  onRemoveItem?: (itemId: string) => void;
  onClearCart?: () => void;
  onCheckout?: () => void;
  onContinueShopping?: () => void;
  /** Pricing */
  subtotal?: number;
  shipping?: number;
  tax?: number;
  total?: number;
  /** Promo code */
  promoCode?: string;
  onApplyPromo?: (code: string) => void;
  promoDiscount?: number;
  /** Free shipping threshold */
  freeShippingThreshold?: number;
}

const ShoppingCartSidebar = React.memo<ShoppingCartSidebarProps>(({
  items = [],
  loading = false,
  onUpdateQuantity,
  onRemoveItem,
  onClearCart,
  onCheckout,
  onContinueShopping,
  subtotal = 0,
  shipping = 0,
  tax = 0,
  total = 0,
  promoCode,
  onApplyPromo,
  promoDiscount = 0,
  freeShippingThreshold = 50
}) => {
  const [promoInput, setPromoInput] = React.useState("");

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    onUpdateQuantity?.(itemId, newQuantity);
  };

  const handleApplyPromo = () => {
    if (promoInput.trim()) {
      onApplyPromo?.(promoInput.trim());
      setPromoInput("");
    }
  };

  const remainingForFreeShipping = Math.max(0, freeShippingThreshold - subtotal);

  if (items.length === 0) {
    return (
      <VStack space="lg" className="p-4">
        <Heading size="lg">Shopping Cart</Heading>
        <VStack space="md" className="items-center py-8">
          <Text className="text-center text-typography-500">
            Your cart is empty
          </Text>
          <Button onPress={onContinueShopping}>
            <ButtonText>Continue Shopping</ButtonText>
          </Button>
        </VStack>
      </VStack>
    );
  }

  return (
    <VStack space="md" className="p-4 h-full">
      <HStack className="justify-between items-center">
        <Heading size="lg">Shopping Cart ({items.length})</Heading>
        <Button size="sm" variant="outline" onPress={onClearCart}>
          <ButtonText>Clear All</ButtonText>
        </Button>
      </HStack>

      {/* Free Shipping Progress */}
      {remainingForFreeShipping > 0 && (
        <Card className="bg-blue-50">
          <CardBody>
            <Text className="text-sm text-blue-800">
              Add ${remainingForFreeShipping.toFixed(2)} more for free shipping!
            </Text>
            <VStack className="mt-2">
              <VStack className="w-full bg-blue-200 rounded-full h-2">
                <VStack 
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${Math.min(100, (subtotal / freeShippingThreshold) * 100)}%` }}
                />
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      )}

      {/* Cart Items */}
      <VStack space="sm" className="flex-1 overflow-auto">
        {items.map((item) => (
          <Card key={item.id}>
            <CardBody>
              <HStack space="sm">
                <Image
                  source={{ uri: item.image }}
                  alt={item.name}
                  className="w-16 h-16 rounded-lg"
                />
                
                <VStack className="flex-1" space="xs">
                  <Text className="font-medium text-sm" numberOfLines={2}>
                    {item.name}
                  </Text>
                  <Text className="text-xs text-typography-500">
                    by {item.vendor.name}
                  </Text>
                  
                  <HStack className="justify-between items-center">
                    <HStack space="sm" className="items-center">
                      <Text className="font-bold">
                        ${item.price.toFixed(2)}
                      </Text>
                      {item.originalPrice && item.originalPrice > item.price && (
                        <Text className="text-xs text-typography-500 line-through">
                          ${item.originalPrice.toFixed(2)}
                        </Text>
                      )}
                    </HStack>
                    
                    <Pressable onPress={() => onRemoveItem?.(item.id)}>
                      <Icon as={TrashIcon} size="sm" className="text-red-500" />
                    </Pressable>
                  </HStack>
                  
                  <HStack className="justify-between items-center">
                    <HStack space="xs" className="items-center">
                      <Button
                        size="xs"
                        variant="outline"
                        onPress={() => handleQuantityChange(item.id, item.quantity - 1)}
                        isDisabled={item.quantity <= 1}
                      >
                        <Icon as={MinusIcon} size="xs" />
                      </Button>
                      
                      <Text className="px-2 text-sm font-medium">
                        {item.quantity}
                      </Text>
                      
                      <Button
                        size="xs"
                        variant="outline"
                        onPress={() => handleQuantityChange(item.id, item.quantity + 1)}
                        isDisabled={item.maxQuantity ? item.quantity >= item.maxQuantity : false}
                      >
                        <Icon as={PlusIcon} size="xs" />
                      </Button>
                    </HStack>
                    
                    {!item.inStock && (
                      <Badge className="bg-red-100">
                        <BadgeText className="text-red-800 text-xs">Out of Stock</BadgeText>
                      </Badge>
                    )}
                  </HStack>
                </VStack>
              </HStack>
            </CardBody>
          </Card>
        ))}
      </VStack>

      {/* Promo Code */}
      <Card>
        <CardBody>
          <VStack space="sm">
            <Text className="font-medium">Promo Code</Text>
            <HStack space="sm">
              <Input className="flex-1">
                <InputField
                  placeholder="Enter promo code"
                  value={promoInput}
                  onChangeText={setPromoInput}
                />
              </Input>
              <Button size="sm" onPress={handleApplyPromo}>
                <ButtonText>Apply</ButtonText>
              </Button>
            </HStack>
            {promoCode && promoDiscount > 0 && (
              <HStack className="justify-between">
                <Text className="text-sm text-green-600">
                  Code "{promoCode}" applied
                </Text>
                <Text className="text-sm text-green-600">
                  -${promoDiscount.toFixed(2)}
                </Text>
              </HStack>
            )}
          </VStack>
        </CardBody>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardBody>
          <VStack space="sm">
            <Text className="font-medium">Order Summary</Text>
            
            <HStack className="justify-between">
              <Text className="text-sm">Subtotal</Text>
              <Text className="text-sm">${subtotal.toFixed(2)}</Text>
            </HStack>
            
            {promoDiscount > 0 && (
              <HStack className="justify-between">
                <Text className="text-sm text-green-600">Discount</Text>
                <Text className="text-sm text-green-600">-${promoDiscount.toFixed(2)}</Text>
              </HStack>
            )}
            
            <HStack className="justify-between">
              <Text className="text-sm">Shipping</Text>
              <Text className="text-sm">
                {shipping === 0 ? "Free" : `$${shipping.toFixed(2)}`}
              </Text>
            </HStack>
            
            <HStack className="justify-between">
              <Text className="text-sm">Tax</Text>
              <Text className="text-sm">${tax.toFixed(2)}</Text>
            </HStack>
            
            <Divider />
            
            <HStack className="justify-between">
              <Text className="font-bold">Total</Text>
              <Text className="font-bold text-lg">${total.toFixed(2)}</Text>
            </HStack>
          </VStack>
        </CardBody>
      </Card>

      {/* Action Buttons */}
      <VStack space="sm">
        <Button 
          onPress={onCheckout}
          isDisabled={items.some(item => !item.inStock)}
          className="w-full"
        >
          <ButtonText>Proceed to Checkout</ButtonText>
        </Button>
        
        <Button variant="outline" onPress={onContinueShopping} className="w-full">
          <ButtonText>Continue Shopping</ButtonText>
        </Button>
      </VStack>
    </VStack>
  );
});

ShoppingCartSidebar.displayName = "ShoppingCartSidebar";
export default ShoppingCartSidebar;