/**
 * Metro configuration with NativeWind
 * Extracted from ui-example-nativewind
 * 
 * This is an example configuration - adapt for your needs
 */

const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require("nativewind/metro");

const config = getDefaultConfig(__dirname, {
  isCSSEnabled: true,
});

module.exports = withNativeWind(config, {
  input: "./global.css",
  inlineRem: 16,
});