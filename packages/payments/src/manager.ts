// Payment Manager - Orchestrates multiple payment providers

import type {
  PaymentProvider,
  PaymentRequest,
  PaymentResult,
  RefundRequest,
  RefundResult,
  PaymentIntent,
  PaymentMethod,
  ProviderConfig
} from './types';
import { createPaymentProvider } from './providers';
import { PaymentErrorFactory } from './types/errors';

export interface PaymentManagerConfig {
  providers: ProviderConfig[];
  defaultProvider?: string | undefined;
  fallbackProviders?: string[];
  retryAttempts?: number;
  retryDelay?: number;
}

export class PaymentManager {
  private providers: Map<string, PaymentProvider> = new Map();
  private defaultProvider?: string | undefined;
  private fallbackProviders: string[] = [];
  private retryAttempts: number = 3;
  private retryDelay: number = 1000;

  constructor(config: PaymentManagerConfig) {
    // Initialize providers
    for (const providerConfig of config.providers) {
      const provider = createPaymentProvider(providerConfig);
      this.providers.set(providerConfig.type, provider);
    }

    this.defaultProvider = config.defaultProvider;
    this.fallbackProviders = config.fallbackProviders || [];
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 1000;
  }

  async createPaymentIntent(
    request: PaymentRequest,
    providerType?: string
  ): Promise<PaymentResult> {
    const provider = this.getProvider(request.paymentMethod, providerType);
    
    if (!provider) {
      throw PaymentErrorFactory.createBusinessError(
        'PAYMENT_METHOD_NOT_SUPPORTED',
        `Payment method ${request.paymentMethod} is not supported`,
        'payment_method_support',
        request.paymentMethod,
        Array.from(this.providers.keys())
      );
    }

    return this.executeWithRetry(
      () => provider.createPaymentIntent(request),
      provider.type
    );
  }

  async confirmPayment(
    paymentIntentId: string,
    paymentMethodData?: any,
    providerType?: string
  ): Promise<PaymentResult> {
    const provider = this.getProviderByType(providerType);
    
    if (!provider) {
      throw PaymentErrorFactory.createBusinessError(
        'PAYMENT_METHOD_NOT_SUPPORTED',
        `Provider ${providerType} is not available`,
        'provider_availability',
        providerType,
        Array.from(this.providers.keys())
      );
    }

    return this.executeWithRetry(
      () => provider.confirmPayment(paymentIntentId, paymentMethodData),
      provider.type
    );
  }

  async cancelPayment(
    paymentIntentId: string,
    providerType?: string
  ): Promise<PaymentResult> {
    const provider = this.getProviderByType(providerType);
    
    if (!provider) {
      throw PaymentErrorFactory.createBusinessError(
        'PAYMENT_METHOD_NOT_SUPPORTED',
        `Provider ${providerType} is not available`,
        'provider_availability',
        providerType,
        Array.from(this.providers.keys())
      );
    }

    return provider.cancelPayment(paymentIntentId);
  }

  async refundPayment(
    request: RefundRequest,
    providerType?: string
  ): Promise<RefundResult> {
    const provider = this.getProviderByType(providerType);
    
    if (!provider) {
      throw PaymentErrorFactory.createBusinessError(
        'PAYMENT_METHOD_NOT_SUPPORTED',
        `Provider ${providerType} is not available`,
        'provider_availability',
        providerType,
        Array.from(this.providers.keys())
      );
    }

    return provider.refundPayment(request);
  }

  async getPaymentStatus(
    paymentIntentId: string,
    providerType?: string
  ): Promise<PaymentIntent> {
    const provider = this.getProviderByType(providerType);
    
    if (!provider) {
      throw PaymentErrorFactory.createBusinessError(
        'PAYMENT_METHOD_NOT_SUPPORTED',
        `Provider ${providerType} is not available`,
        'provider_availability',
        providerType,
        Array.from(this.providers.keys())
      );
    }

    return provider.getPaymentStatus(paymentIntentId);
  }

  async handleWebhook(
    payload: string,
    signature: string,
    providerType: string
  ): Promise<void> {
    const provider = this.getProviderByType(providerType);
    
    if (!provider) {
      throw PaymentErrorFactory.createBusinessError(
        'PAYMENT_METHOD_NOT_SUPPORTED',
        `Provider ${providerType} is not available`,
        'provider_availability',
        providerType,
        Array.from(this.providers.keys())
      );
    }

    // Verify webhook signature
    if (!provider.verifyWebhook(payload, signature)) {
      throw PaymentErrorFactory.createSecurityError(
        'WEBHOOK_VERIFICATION_FAILED',
        'Webhook signature verification failed',
        'high'
      );
    }

    // Handle webhook event
    const webhookData = JSON.parse(payload);
    await provider.handleWebhook(webhookData);
  }

  // Provider management
  addProvider(config: ProviderConfig): void {
    const provider = createPaymentProvider(config);
    this.providers.set(config.type, provider);
  }

  removeProvider(providerType: string): void {
    this.providers.delete(providerType);
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  isProviderAvailable(providerType: string): boolean {
    return this.providers.has(providerType);
  }

  // Payment method routing
  getProviderForPaymentMethod(paymentMethod: PaymentMethod): PaymentProvider | null {
    // Route payment methods to appropriate providers
    const routingMap: Record<PaymentMethod, string> = {
      'polar_card': 'polar',
      'polar_checkout': 'polar',
      'payfast_eft': 'payfast',
      'payfast_card': 'payfast',
      'mpesa': 'mpesa',
      'voucher_1voucher': 'voucher',
      'voucher_ott': 'voucher',
      'cash_on_delivery': 'cash'
    };

    const providerType = routingMap[paymentMethod];
    return providerType ? this.providers.get(providerType) || null : null;
  }

  // Private helper methods
  private getProvider(
    paymentMethod: PaymentMethod,
    providerType?: string
  ): PaymentProvider | null {
    if (providerType) {
      return this.providers.get(providerType) || null;
    }

    return this.getProviderForPaymentMethod(paymentMethod);
  }

  private getProviderByType(providerType?: string): PaymentProvider | null {
    if (providerType) {
      return this.providers.get(providerType) || null;
    }

    if (this.defaultProvider) {
      return this.providers.get(this.defaultProvider) || null;
    }

    // Return first available provider
    const firstProvider = this.providers.values().next().value;
    return firstProvider || null;
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    providerType: string
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on validation errors or security errors
        if (error instanceof Error) {
          const errorCode = (error as any).code;
          if (errorCode?.includes('VALIDATION') || errorCode?.includes('SECURITY')) {
            throw error;
          }
        }

        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    // Try fallback providers
    for (const fallbackType of this.fallbackProviders) {
      if (fallbackType !== providerType && this.providers.has(fallbackType)) {
        try {
          return await operation();
        } catch (error) {
          // Continue to next fallback
          lastError = error as Error;
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
