import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Switch,
  TextInput
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  PlusIcon,
  SearchIcon,
  FilterIcon,
  MoreVerticalIcon,
  EditIcon,
  CopyIcon,
  TrashIcon,
  EyeIcon,
  EyeOffIcon,
  DollarSignIcon,
  ClockIcon,
  StarIcon,
  TrendingUpIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useServicesStore } from '../../stores/servicesStore'
import type { VendorService } from '../../stores/servicesStore'
import type { ServicesStackScreenProps } from '../../types/navigation'

type Props = ServicesStackScreenProps<'ServicesList'>

export function ServicesScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const {
    services,
    activeServices,
    inactiveServices,
    fetchServices,
    toggleServiceStatus,
    duplicateService,
    deleteService,
    getServicesStats,
    isLoading
  } = useServicesStore()

  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTab, setSelectedTab] = useState<'all' | 'active' | 'inactive'>('all')
  const [showActionMenu, setShowActionMenu] = useState<string | null>(null)

  useEffect(() => {
    loadServices()
  }, [])

  const loadServices = async () => {
    try {
      await fetchServices()
    } catch (error) {
      console.error('Failed to load services:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadServices()
    setRefreshing(false)
  }

  const handleAddService = () => {
    navigation.navigate('ServiceForm')
  }

  const handleEditService = (serviceId: string) => {
    navigation.navigate('ServiceForm', { serviceId })
    setShowActionMenu(null)
  }

  const handleToggleStatus = async (serviceId: string) => {
    try {
      await toggleServiceStatus(serviceId)
      setShowActionMenu(null)
    } catch (error) {
      Alert.alert('Error', 'Failed to update service status')
    }
  }

  const handleDuplicateService = async (serviceId: string) => {
    try {
      await duplicateService(serviceId)
      setShowActionMenu(null)
      Alert.alert('Success', 'Service duplicated successfully!')
    } catch (error) {
      Alert.alert('Error', 'Failed to duplicate service')
    }
  }

  const handleDeleteService = (serviceId: string) => {
    Alert.alert(
      'Delete Service',
      'Are you sure you want to delete this service? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteService(serviceId)
              setShowActionMenu(null)
              Alert.alert('Success', 'Service deleted successfully!')
            } catch (error) {
              Alert.alert('Error', 'Failed to delete service')
            }
          }
        }
      ]
    )
  }

  const getFilteredServices = () => {
    let filteredServices: VendorService[] = []

    switch (selectedTab) {
      case 'active':
        filteredServices = activeServices
        break
      case 'inactive':
        filteredServices = inactiveServices
        break
      default:
        filteredServices = services
    }

    if (searchQuery.trim()) {
      filteredServices = filteredServices.filter(service =>
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filteredServices
  }

  const renderHeader = () => {
    const stats = getServicesStats()

    return (
      <View style={{
        backgroundColor: colors.background.primary,
        paddingTop: 50,
        paddingBottom: 16,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.primary,
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
          <Text style={[typography.headline.large, { color: colors.text.primary }]}>
            My Services
          </Text>

          <TouchableOpacity
            style={{
              backgroundColor: colors.primary[500],
              borderRadius: 20,
              paddingHorizontal: 16,
              paddingVertical: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}
            onPress={handleAddService}
          >
            <PlusIcon size={16} color={colors.white} />
            <Text style={[typography.label.small, { color: colors.white, marginLeft: 6 }]}>
              Add Service
            </Text>
          </TouchableOpacity>
        </View>

        {/* Stats Cards */}
        <View style={{ flexDirection: 'row', gap: 8, marginBottom: 16 }}>
          <View style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderRadius: 8,
            padding: 12,
            alignItems: 'center',
          }}>
            <Text style={[typography.title.medium, { color: colors.text.primary, fontWeight: '700' }]}>
              {stats.total}
            </Text>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              Total
            </Text>
          </View>

          <View style={{
            flex: 1,
            backgroundColor: colors.success[100],
            borderRadius: 8,
            padding: 12,
            alignItems: 'center',
          }}>
            <Text style={[typography.title.medium, { color: colors.success[700], fontWeight: '700' }]}>
              {stats.active}
            </Text>
            <Text style={[typography.body.small, { color: colors.success[600] }]}>
              Active
            </Text>
          </View>

          <View style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderRadius: 8,
            padding: 12,
            alignItems: 'center',
          }}>
            <Text style={[typography.title.medium, { color: colors.text.primary, fontWeight: '700' }]}>
              R{stats.totalRevenue.toLocaleString()}
            </Text>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              Revenue
            </Text>
          </View>
        </View>

        {/* Search Bar */}
        <View style={{
          flexDirection: 'row',
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          paddingHorizontal: 16,
          paddingVertical: 12,
          alignItems: 'center',
          marginBottom: 16,
        }}>
          <SearchIcon size={20} color={colors.text.secondary} />
          <TextInput
            style={[
              typography.body.medium,
              {
                flex: 1,
                marginLeft: 12,
                color: colors.text.primary,
              }
            ]}
            placeholder="Search services..."
            placeholderTextColor={colors.text.tertiary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Tab Navigation */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            {[
              { key: 'all', label: 'All Services', count: services.length },
              { key: 'active', label: 'Active', count: activeServices.length },
              { key: 'inactive', label: 'Inactive', count: inactiveServices.length },
            ].map((tab) => (
              <TouchableOpacity
                key={tab.key}
                style={{
                  backgroundColor: selectedTab === tab.key ? colors.primary[500] : colors.background.secondary,
                  borderRadius: 20,
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onPress={() => setSelectedTab(tab.key as any)}
              >
                <Text style={[
                  typography.body.small,
                  {
                    color: selectedTab === tab.key ? colors.white : colors.text.primary,
                    fontWeight: '600'
                  }
                ]}>
                  {tab.label}
                </Text>
                {tab.count > 0 && (
                  <View style={{
                    backgroundColor: selectedTab === tab.key ? colors.white : colors.primary[500],
                    borderRadius: 10,
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    marginLeft: 6,
                  }}>
                    <Text style={[
                      typography.body.small,
                      {
                        color: selectedTab === tab.key ? colors.primary[500] : colors.white,
                        fontSize: 10,
                        fontWeight: '600'
                      }
                    ]}>
                      {tab.count}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    )
  }

  const renderServiceCard = (service: VendorService) => (
    <View
      key={service.id}
      style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: colors.border.primary,
      }}
    >
      {/* Service Header */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
            <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
              {service.name}
            </Text>
            <View style={{
              backgroundColor: service.isActive ? colors.success[100] : colors.gray[100],
              borderRadius: 12,
              paddingHorizontal: 8,
              paddingVertical: 2,
              marginLeft: 8,
            }}>
              <Text style={[
                typography.body.small,
                {
                  color: service.isActive ? colors.success[700] : colors.gray[600],
                  fontSize: 10,
                  fontWeight: '600'
                }
              ]}>
                {service.isActive ? 'ACTIVE' : 'INACTIVE'}
              </Text>
            </View>
          </View>

          <Text style={[typography.body.small, { color: colors.text.secondary, marginBottom: 4 }]}>
            {service.category} • {service.subcategory}
          </Text>

          <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
            {service.description.length > 100
              ? `${service.description.substring(0, 100)}...`
              : service.description
            }
          </Text>
        </View>

        <TouchableOpacity
          style={{
            backgroundColor: colors.background.primary,
            borderRadius: 16,
            padding: 4,
            marginLeft: 8,
          }}
          onPress={() => setShowActionMenu(showActionMenu === service.id ? null : service.id)}
        >
          <MoreVerticalIcon size={16} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      {/* Service Stats */}
      <View style={{ flexDirection: 'row', gap: 16, marginBottom: 12 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <DollarSignIcon size={14} color={colors.text.secondary} />
          <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 4 }]}>
            R{service.basePrice} {service.priceType === 'hourly' ? '/hr' : ''}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <ClockIcon size={14} color={colors.text.secondary} />
          <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 4 }]}>
            {Math.floor(service.duration / 60)}h {service.duration % 60}m
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <StarIcon size={14} color={colors.warning[500]} />
          <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 4 }]}>
            {service.stats.averageRating.toFixed(1)} ({service.stats.reviewCount})
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TrendingUpIcon size={14} color={colors.success[500]} />
          <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 4 }]}>
            {service.stats.totalBookings} bookings
          </Text>
        </View>
      </View>

      {/* Action Menu */}
      {showActionMenu === service.id && (
        <View style={{
          backgroundColor: colors.background.primary,
          borderRadius: 8,
          padding: 8,
          borderWidth: 1,
          borderColor: colors.border.primary,
        }}>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 12,
            }}
            onPress={() => handleEditService(service.id)}
          >
            <EditIcon size={16} color={colors.text.primary} />
            <Text style={[typography.body.small, { color: colors.text.primary, marginLeft: 8 }]}>
              Edit Service
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 12,
            }}
            onPress={() => handleToggleStatus(service.id)}
          >
            {service.isActive ? (
              <EyeOffIcon size={16} color={colors.text.primary} />
            ) : (
              <EyeIcon size={16} color={colors.text.primary} />
            )}
            <Text style={[typography.body.small, { color: colors.text.primary, marginLeft: 8 }]}>
              {service.isActive ? 'Deactivate' : 'Activate'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 12,
            }}
            onPress={() => handleDuplicateService(service.id)}
          >
            <CopyIcon size={16} color={colors.text.primary} />
            <Text style={[typography.body.small, { color: colors.text.primary, marginLeft: 8 }]}>
              Duplicate
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 12,
            }}
            onPress={() => handleDeleteService(service.id)}
          >
            <TrashIcon size={16} color={colors.error[500]} />
            <Text style={[typography.body.small, { color: colors.error[500], marginLeft: 8 }]}>
              Delete
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  )

  const renderServicesList = () => {
    const filteredServices = getFilteredServices()

    if (filteredServices.length === 0) {
      return (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 24,
          paddingVertical: 48,
        }}>
          <Text style={[typography.body.large, { color: colors.text.secondary, textAlign: 'center' }]}>
            {searchQuery.trim() ? 'No services match your search' : 'No services found'}
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.tertiary, textAlign: 'center', marginTop: 8 }]}>
            {searchQuery.trim()
              ? 'Try adjusting your search terms'
              : 'Add your first service to start receiving bookings'
            }
          </Text>
          {!searchQuery.trim() && (
            <TouchableOpacity
              style={{
                backgroundColor: colors.primary[500],
                borderRadius: 12,
                paddingHorizontal: 24,
                paddingVertical: 12,
                marginTop: 16,
              }}
              onPress={handleAddService}
            >
              <Text style={[typography.label.medium, { color: colors.white }]}>
                Add Your First Service
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )
    }

    return (
      <View style={{ padding: 16 }}>
        {filteredServices.map(renderServiceCard)}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      {renderHeader()}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderServicesList()}
      </ScrollView>
    </SafeAreaView>
  )
}
