#!/bin/bash

# HVPPYPlug+ Monorepo Setup Script
echo "🔌 Setting up HVPPYPlug+ Monorepo..."

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm not found. Installing pnpm..."
    npm install -g pnpm
fi

# Check if Expo CLI is installed
if ! command -v expo &> /dev/null; then
    echo "❌ Expo CLI not found. Installing Expo CLI..."
    npm install -g @expo/cli
fi

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Copy environment file
if [ ! -f .env ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env
    echo "⚠️  Please edit .env with your configuration values"
fi

# Build common package
echo "🔨 Building common package..."
pnpm build:common

echo "✅ Setup complete!"
echo ""
echo "🚀 Quick start commands:"
echo "  pnpm dev              # Run all apps in development"
echo "  pnpm build            # Build all packages"
echo "  pnpm type-check       # Run TypeScript checks"
echo "  pnpm lint             # Lint all code"
echo ""
echo "📱 Individual app commands:"
echo "  cd apps/customer-app && pnpm dev     # Customer app"
echo "  cd apps/vendor-app && pnpm dev       # Vendor app"
echo "  cd apps/runner-app && pnpm dev       # Runner app"
echo "  cd apps/web-admin && pnpm dev        # Web admin"
echo "  cd backend && pnpm dev               # Backend API"
echo ""
echo "🔗 Don't forget to:"
echo "  1. Edit .env with your API keys and configuration"
echo "  2. Set up Firebase project"
echo "  3. Configure Stripe/PayFast accounts"
echo "  4. Get Google Maps API key"
echo ""
echo "📚 Check README.md for detailed documentation"
echo ""
echo "Happy coding! 🎉"
