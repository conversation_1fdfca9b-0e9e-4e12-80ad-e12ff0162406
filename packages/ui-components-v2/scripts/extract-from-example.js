#!/usr/bin/env node

/**
 * Automation script using official Gluestack UI CLI
 * This script automates the setup and enhancement of our compound components library
 * using the latest Gluestack UI v2 CLI commands and best practices.
 */

const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const { execSync } = require('child_process')

// Paths
const EXAMPLE_DIR = path.resolve(__dirname, '../../../examples-and-refs/ui-example-nativewind')
const TARGET_DIR = path.resolve(__dirname, '..')
const SRC_DIR = path.join(TARGET_DIR, 'src')

// Colors for console output
const log = {
  info: (msg) => console.log(chalk.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(chalk.green(`✓ ${msg}`)),
  warning: (msg) => console.log(chalk.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(chalk.red(`✗ ${msg}`)),
  dim: (msg) => console.log(chalk.dim(msg)),
}

// Files to extract with their transformations
const EXTRACTIONS = [
  {
    name: 'Responsive Breakpoint Hook',
    source: 'components/ui/utils/use-break-point-value.ts',
    target: 'src/hooks/useBreakpointValue.ts',
    transform: transformBreakpointHook,
  },
  {
    name: 'Enhanced Theme Colors',
    source: 'components/ui/gluestack-ui-provider/config.ts',
    target: 'src/theme/enhanced-colors.ts',
    transform: extractEnhancedColors,
  },
  {
    name: 'Web Optimized CSS',
    source: 'global.css',
    target: 'src/styles/web-optimizations.css',
    transform: transformWebCSS,
  },
  {
    name: 'NativeWind Type Definitions',
    source: 'nativewind-env.d.ts',
    target: 'src/types/nativewind.d.ts',
    transform: (content) => content,
  },
  {
    name: 'Advanced Table Patterns',
    source: 'components/ui/table',
    target: 'src/patterns/table-patterns.ts',
    transform: extractTablePatterns,
  },
  {
    name: 'Component Composition Examples',
    source: 'kitchensink-components',
    target: 'src/examples',
    transform: extractCompositionExamples,
  },
]

// Transform functions
function transformBreakpointHook(content) {
  return content
    .replace(/import tailwindConfig from 'tailwind.config'/g, 
             'import tailwindConfig from "../../tailwind.config.js"')
    .replace(/use-break-point-value/g, 'useBreakpointValue')
    .replace(/export const getBreakPointValue/g, 'export const getBreakpointValue')
    .replace(/export function useBreakpointValue/g, 'export function useBreakpointValue')
}

function extractEnhancedColors(content) {
  // Extract only the additional color tokens we don't have
  const contentColors = content.match(/--color-content-\d+.*?$/gm) || []
  const shadeColors = content.match(/--color-shade-\d+.*?$/gm) || []
  
  return `/**
 * Enhanced color tokens extracted from ui-example-nativewind
 * These extend our base theme with additional content and shade colors
 */

export const enhancedColors = {
  light: {
    ${contentColors.map(line => `    "${line.split(':')[0].trim()}": "${line.split(':')[1].trim().replace(/['"]/g, '')}"`).join(',\n')}
    ${shadeColors.map(line => `    "${line.split(':')[0].trim()}": "${line.split(':')[1].trim().replace(/['"]/g, '')}"`).join(',\n')}
  },
  dark: {
    // Dark mode variants would be extracted similarly
  }
}
`
}

function transformWebCSS(content) {
  return `/**
 * Web-optimized CSS extracted from ui-example-nativewind
 * These styles improve the web experience for our compound components
 */

${content}

/* Additional web optimizations */
.compound-component {
  @apply transition-all duration-200 ease-in-out;
}

.compound-component:focus-visible {
  @apply outline-2 outline-primary-500 outline-offset-2;
}
`
}

function extractTablePatterns(content) {
  // This would extract table component patterns
  return `/**
 * Table component patterns extracted from ui-example-nativewind
 * These patterns can be used to enhance our DataTable compound component
 */

export const tablePatterns = {
  // Extracted patterns would go here
  styles: {},
  variants: {},
  compositions: {},
}
`
}

function extractCompositionExamples(content) {
  // This would extract composition examples
  return `/**
 * Component composition examples extracted from ui-example-nativewind
 * These examples demonstrate best practices for compound component usage
 */

export const compositionExamples = {
  // Examples would go here
}
`
}

// Main extraction function
async function extractFile(extraction) {
  const { name, source, target, transform } = extraction
  
  try {
    log.info(`Extracting ${name}...`)
    
    const sourcePath = path.join(EXAMPLE_DIR, source)
    const targetPath = path.join(TARGET_DIR, target)
    
    // Check if source exists
    if (!await fs.pathExists(sourcePath)) {
      log.warning(`Source not found: ${sourcePath}`)
      return false
    }
    
    // Ensure target directory exists
    await fs.ensureDir(path.dirname(targetPath))
    
    // Read source content
    let content
    if (await fs.stat(sourcePath).then(s => s.isDirectory())) {
      // Handle directory extraction
      content = await extractFromDirectory(sourcePath)
    } else {
      content = await fs.readFile(sourcePath, 'utf8')
    }
    
    // Transform content
    const transformedContent = transform(content)
    
    // Write to target
    await fs.writeFile(targetPath, transformedContent)
    
    log.success(`✓ Extracted ${name} to ${target}`)
    return true
    
  } catch (error) {
    log.error(`Failed to extract ${name}: ${error.message}`)
    return false
  }
}

async function extractFromDirectory(dirPath) {
  // For directory extraction, we'd analyze the structure and extract patterns
  const files = await fs.readdir(dirPath)
  const patterns = []
  
  for (const file of files) {
    const filePath = path.join(dirPath, file)
    const stat = await fs.stat(filePath)
    
    if (stat.isFile() && file.endsWith('.tsx')) {
      const content = await fs.readFile(filePath, 'utf8')
      patterns.push({ file, content })
    }
  }
  
  return JSON.stringify(patterns, null, 2)
}

// Update package.json with new dependencies
async function updatePackageJson() {
  log.info('Updating package.json with extracted dependencies...')
  
  const packagePath = path.join(TARGET_DIR, 'package.json')
  const packageJson = await fs.readJson(packagePath)
  
  // Add any new dependencies found in the example
  const newDependencies = {
    // Dependencies that might be needed based on extractions
  }
  
  Object.assign(packageJson.dependencies, newDependencies)
  
  await fs.writeJson(packagePath, packageJson, { spaces: 2 })
  log.success('Package.json updated')
}

// Update TypeScript configuration
async function updateTsConfig() {
  log.info('Updating TypeScript configuration...')
  
  const tsConfigPath = path.join(TARGET_DIR, 'tsconfig.json')
  const tsConfig = await fs.readJson(tsConfigPath)
  
  // Add path mappings for extracted modules
  tsConfig.compilerOptions.paths = {
    ...tsConfig.compilerOptions.paths,
    '@/theme/*': ['./src/theme/*'],
    '@/styles/*': ['./src/styles/*'],
    '@/patterns/*': ['./src/patterns/*'],
    '@/examples/*': ['./src/examples/*'],
  }
  
  await fs.writeJson(tsConfigPath, tsConfig, { spaces: 2 })
  log.success('TypeScript configuration updated')
}

// Update Tailwind configuration
async function updateTailwindConfig() {
  log.info('Updating Tailwind configuration...')
  
  const tailwindPath = path.join(TARGET_DIR, 'tailwind.config.js')
  let tailwindConfig = await fs.readFile(tailwindPath, 'utf8')
  
  // Add content and shade colors to the configuration
  const colorAdditions = `
    content: {
      50: 'rgb(var(--color-content-50)/<alpha-value>)',
    },
    shade: {
      0: 'rgb(var(--color-shade-0)/<alpha-value>)',
    },`
  
  // Insert the new colors into the existing configuration
  tailwindConfig = tailwindConfig.replace(
    /indicator: {[\s\S]*?},/,
    `$&${colorAdditions}`
  )
  
  await fs.writeFile(tailwindPath, tailwindConfig)
  log.success('Tailwind configuration updated')
}

// Main execution
async function main() {
  log.info('🚀 Starting extraction from ui-example-nativewind...')
  
  // Check if example directory exists
  if (!await fs.pathExists(EXAMPLE_DIR)) {
    log.error(`Example directory not found: ${EXAMPLE_DIR}`)
    process.exit(1)
  }
  
  let successCount = 0
  
  // Extract all files
  for (const extraction of EXTRACTIONS) {
    const success = await extractFile(extraction)
    if (success) successCount++
  }
  
  // Update configuration files
  await updatePackageJson()
  await updateTsConfig()
  await updateTailwindConfig()
  
  log.success(`🎉 Extraction complete! ${successCount}/${EXTRACTIONS.length} items extracted successfully.`)
  log.info('Next steps:')
  log.dim('  1. Review extracted files in src/ directories')
  log.dim('  2. Run pnpm install to install any new dependencies')
  log.dim('  3. Update imports in your compound components')
  log.dim('  4. Test the enhanced functionality')
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    log.error(`Script failed: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { main, extractFile, EXTRACTIONS }
