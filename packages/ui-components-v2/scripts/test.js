#!/usr/bin/env node

const { execSync } = require('child_process')
const path = require('path')
const chalk = require('chalk')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')

const argv = yargs(hideBin(process.argv))
  .option('watch', {
    alias: 'w',
    type: 'boolean',
    description: 'Watch for changes and re-run tests'
  })
  .option('coverage', {
    alias: 'c',
    type: 'boolean',
    description: 'Generate coverage report'
  })
  .option('verbose', {
    alias: 'v',
    type: 'boolean',
    description: 'Verbose output'
  })
  .option('updateSnapshot', {
    alias: 'u',
    type: 'boolean',
    description: 'Update snapshots'
  })
  .help()
  .argv

const ROOT_DIR = path.resolve(__dirname, '..')

// Colors for console output
const colors = {
  info: chalk.blue,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  dim: chalk.dim,
}

// Log with timestamp
function log(message, color = 'info') {
  const timestamp = new Date().toLocaleTimeString()
  console.log(`${colors.dim(`[${timestamp}]`)} ${colors[color](message)}`)
}

// Jest configuration
const jestConfig = {
  preset: 'react-native',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}'
  ],
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest'
  },
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|@gluestack-ui|react-native-reanimated|react-native-svg)/)'
  ]
}

// Create Jest setup file
const jestSetup = `
import '@testing-library/jest-native/extend-expect'

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock')
  Reanimated.default.call = () => {}
  return Reanimated
})

// Mock react-native-gesture-handler
jest.mock('react-native-gesture-handler', () => {
  const View = require('react-native/Libraries/Components/View/View')
  return {
    Swipeable: View,
    DrawerLayout: View,
    State: {},
    ScrollView: View,
    Slider: View,
    Switch: View,
    TextInput: View,
    ToolbarAndroid: View,
    ViewPagerAndroid: View,
    DrawerLayoutAndroid: View,
    WebView: View,
    NativeViewGestureHandler: View,
    TapGestureHandler: View,
    FlingGestureHandler: View,
    ForceTouchGestureHandler: View,
    LongPressGestureHandler: View,
    PanGestureHandler: View,
    PinchGestureHandler: View,
    RotationGestureHandler: View,
    RawButton: View,
    BaseButton: View,
    RectButton: View,
    BorderlessButton: View,
    FlatList: View,
    gestureHandlerRootHOC: jest.fn(component => component),
    Directions: {}
  }
})

// Mock Expo modules
jest.mock('expo-document-picker', () => ({
  getDocumentAsync: jest.fn(() => Promise.resolve({ canceled: true }))
}))

jest.mock('expo-image-picker', () => ({
  launchImageLibraryAsync: jest.fn(() => Promise.resolve({ canceled: true })),
  launchCameraAsync: jest.fn(() => Promise.resolve({ canceled: true })),
  MediaTypeOptions: {
    All: 'All',
    Videos: 'Videos',
    Images: 'Images'
  }
}))

// Mock @react-native-community/datetimepicker
jest.mock('@react-native-community/datetimepicker', () => {
  const React = require('react')
  const { View } = require('react-native')
  return React.forwardRef((props, ref) => React.createElement(View, props))
})

// Global test utilities
global.mockUser = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  status: 'active',
  createdAt: new Date('2023-01-01'),
  lastLogin: new Date('2023-12-01')
}

global.mockUsers = [
  global.mockUser,
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    createdAt: new Date('2023-01-02'),
    lastLogin: new Date('2023-12-02')
  }
]
`

// Create Babel config
const babelConfig = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    'react-native-reanimated/plugin',
    ['module-resolver', {
      root: ['./src'],
      alias: {
        '@': './src'
      }
    }]
  ]
}

// Run tests
async function runTests() {
  const fs = require('fs-extra')
  
  try {
    log('🧪 Setting up test environment...', 'info')
    
    // Write Jest config
    await fs.writeJson(path.join(ROOT_DIR, 'jest.config.json'), jestConfig, { spaces: 2 })
    
    // Write Jest setup
    await fs.writeFile(path.join(ROOT_DIR, 'jest.setup.js'), jestSetup)
    
    // Write Babel config
    await fs.writeJson(path.join(ROOT_DIR, 'babel.config.json'), babelConfig, { spaces: 2 })
    
    log('✓ Test environment configured', 'success')
    
    // Build Jest command
    const jestArgs = []
    
    if (argv.watch) jestArgs.push('--watch')
    if (argv.coverage) jestArgs.push('--coverage')
    if (argv.verbose) jestArgs.push('--verbose')
    if (argv.updateSnapshot) jestArgs.push('--updateSnapshot')
    
    const command = `npx jest ${jestArgs.join(' ')}`
    
    log('🚀 Running tests...', 'info')
    log(`Command: ${command}`, 'dim')
    
    execSync(command, {
      cwd: ROOT_DIR,
      stdio: 'inherit'
    })
    
    log('✅ Tests completed successfully', 'success')
    
  } catch (error) {
    if (error.status === 1) {
      log('❌ Some tests failed', 'error')
    } else {
      log(`❌ Test runner failed: ${error.message}`, 'error')
    }
    process.exit(1)
  }
}

// Create sample test files
async function createSampleTests() {
  const fs = require('fs-extra')
  
  const testFiles = [
    {
      path: 'src/data/DataTable/__tests__/DataTable.test.tsx',
      content: `
import React from 'react'
import { render, fireEvent } from '@testing-library/react-native'
import { DataTable } from '../DataTable'

const mockData = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' }
]

const mockColumns = [
  { key: 'name', title: 'Name', sortable: true },
  { key: 'email', title: 'Email', sortable: true },
  { key: 'status', title: 'Status' }
]

describe('DataTable', () => {
  it('renders correctly', () => {
    const { getByText } = render(
      <DataTable data={mockData} columns={mockColumns} />
    )
    
    expect(getByText('Name')).toBeTruthy()
    expect(getByText('Email')).toBeTruthy()
    expect(getByText('John Doe')).toBeTruthy()
    expect(getByText('<EMAIL>')).toBeTruthy()
  })
  
  it('handles sorting', () => {
    const onSort = jest.fn()
    const { getByText } = render(
      <DataTable 
        data={mockData} 
        columns={mockColumns} 
        onSort={onSort}
      />
    )
    
    fireEvent.press(getByText('Name'))
    expect(onSort).toHaveBeenCalledWith('name', 'asc')
  })
  
  it('handles row press', () => {
    const onRowPress = jest.fn()
    const { getByText } = render(
      <DataTable 
        data={mockData} 
        columns={mockColumns} 
        onRowPress={onRowPress}
      />
    )
    
    fireEvent.press(getByText('John Doe'))
    expect(onRowPress).toHaveBeenCalledWith(mockData[0], 0)
  })
})
`
    },
    {
      path: 'src/forms/MultiStepForm/__tests__/MultiStepForm.test.tsx',
      content: `
import React from 'react'
import { render, fireEvent } from '@testing-library/react-native'
import { MultiStepForm } from '../MultiStepForm'

const MockStep = ({ data, onDataChange }) => (
  <input 
    testID="step-input"
    value={data.test || ''}
    onChange={(e) => onDataChange({ test: e.target.value })}
  />
)

const mockSteps = [
  {
    id: 'step1',
    title: 'Step 1',
    component: MockStep,
    validation: (data) => !!data.test
  },
  {
    id: 'step2', 
    title: 'Step 2',
    component: MockStep
  }
]

describe('MultiStepForm', () => {
  it('renders first step', () => {
    const { getByText } = render(
      <MultiStepForm steps={mockSteps} />
    )
    
    expect(getByText('Step 1')).toBeTruthy()
  })
  
  it('navigates between steps', () => {
    const { getByText, getByTestId } = render(
      <MultiStepForm steps={mockSteps} />
    )
    
    // Fill first step
    fireEvent.changeText(getByTestId('step-input'), 'test value')
    
    // Go to next step
    fireEvent.press(getByText('Next'))
    
    expect(getByText('Step 2')).toBeTruthy()
  })
  
  it('calls onComplete when finished', () => {
    const onComplete = jest.fn()
    const { getByText, getByTestId } = render(
      <MultiStepForm steps={mockSteps} onComplete={onComplete} />
    )
    
    // Fill first step and navigate
    fireEvent.changeText(getByTestId('step-input'), 'test value')
    fireEvent.press(getByText('Next'))
    
    // Complete form
    fireEvent.press(getByText('Complete'))
    
    expect(onComplete).toHaveBeenCalledWith({ test: 'test value' })
  })
})
`
    }
  ]
  
  for (const file of testFiles) {
    const filePath = path.join(ROOT_DIR, file.path)
    await fs.ensureDir(path.dirname(filePath))
    await fs.writeFile(filePath, file.content.trim())
    log(`✓ Created ${file.path}`, 'dim')
  }
  
  log('✓ Sample test files created', 'success')
}

// Main function
async function main() {
  try {
    await createSampleTests()
    await runTests()
  } catch (error) {
    log(`❌ Error: ${error.message}`, 'error')
    process.exit(1)
  }
}

main()
