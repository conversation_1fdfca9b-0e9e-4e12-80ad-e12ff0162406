{"cli": {"version": ">= 12.0.0"}, "build": {"base": {"node": "20.11.1", "pnpm": "8.15.0"}, "development": {"extends": "base", "developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true, "buildConfiguration": "Debug"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "channel": "development", "env": {"EXPO_DEBUG": "true", "NODE_ENV": "development"}}, "preview": {"extends": "base", "distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": false, "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "preview", "env": {"NODE_ENV": "production"}}, "production": {"extends": "base", "ios": {"resourceClass": "m-large", "simulator": false, "buildConfiguration": "Release"}, "android": {"resourceClass": "large", "buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "channel": "production", "env": {"NODE_ENV": "production"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "ABCDEFGHIJ"}, "android": {"serviceAccountKeyPath": "../path/to/api-key.json", "track": "internal"}}}}