// Payment Provider Types and Configurations

import type { 
  PaymentRequest, 
  PaymentResult, 
  RefundRequest, 
  RefundResult,
  PaymentIntent,
  Amount,
  Currency
} from './payment';

export type PaymentProviderType = 'polar' | 'payfast' | 'mpesa' | 'voucher';

// Base Payment Provider Interface
export interface PaymentProvider {
  readonly type: PaymentProviderType;
  readonly name: string;
  readonly supportedCurrencies: Currency[];
  readonly testMode: boolean;

  // Core payment operations
  createPaymentIntent(request: PaymentRequest): Promise<PaymentResult>;
  confirmPayment(paymentIntentId: string, paymentMethodData?: any): Promise<PaymentResult>;
  cancelPayment(paymentIntentId: string): Promise<PaymentResult>;
  refundPayment(request: RefundRequest): Promise<RefundResult>;
  
  // Payment status and retrieval
  getPaymentStatus(paymentIntentId: string): Promise<PaymentIntent>;
  
  // Webhook handling
  verifyWebhook(payload: string, signature: string): boolean;
  handleWebhook(payload: any): Promise<void>;
  
  // Provider-specific methods
  validatePaymentMethod?(paymentMethodData: any): Promise<boolean>;
  getPaymentMethodDetails?(paymentMethodId: string): Promise<any>;
}

// Polar Configuration
export interface PolarConfig {
  accessToken: string;
  webhookSecret: string;
  organizationId?: string;
  testMode: boolean;
  server?: 'sandbox' | 'production';
  successUrl?: string;
  cancelUrl?: string;
  merchantDisplayName?: string;
}

// PayFast Configuration
export interface PayFastConfig {
  merchantId: string;
  merchantKey: string;
  passphrase?: string;
  testMode: boolean;
  returnUrl: string;
  cancelUrl: string;
  notifyUrl: string;
  nameFirst?: string;
  nameLast?: string;
  emailAddress?: string;
}

// M-Pesa Configuration
export interface MPesaConfig {
  consumerKey: string;
  consumerSecret: string;
  businessShortCode: string;
  passkey: string;
  testMode: boolean;
  callbackUrl: string;
  timeoutUrl: string;
  accountReference?: string;
  transactionDesc?: string;
}

// Voucher Provider Configuration
export interface VoucherConfig {
  apiKey: string;
  apiSecret: string;
  testMode: boolean;
  supportedVoucherTypes: ('ott' | '1voucher')[];
  validationUrl: string;
  redemptionUrl: string;
  webhookUrl?: string;
}

// Provider-specific request/response types

// Polar Types
export interface PolarCheckoutSession {
  id: string;
  url: string;
  customer_id?: string;
  product_id?: string;
  product_price_id?: string;
  success_url?: string;
  cancel_url?: string;
  metadata?: Record<string, string>;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface PolarOrder {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  customer_id: string;
  product_id: string;
  product_price_id: string;
  checkout_id?: string;
  metadata?: Record<string, string>;
  created_at: string;
  updated_at: string;
}

export interface PolarCustomer {
  id: string;
  email?: string;
  name?: string;
  avatar_url?: string;
  metadata?: Record<string, string>;
  created_at: string;
  updated_at: string;
}

// PayFast Types
export interface PayFastPaymentData {
  merchantId: string;
  merchantKey: string;
  returnUrl: string;
  cancelUrl: string;
  notifyUrl: string;
  nameFirst?: string;
  nameLast?: string;
  emailAddress?: string;
  mPaymentId: string;
  amount: string;
  itemName: string;
  itemDescription?: string;
  customInt1?: string;
  customStr1?: string;
  signature: string;
}

export interface PayFastNotification {
  mPaymentId: string;
  pfPaymentId: string;
  paymentStatus: 'COMPLETE' | 'FAILED' | 'CANCELLED';
  itemName: string;
  itemDescription?: string;
  amountGross: string;
  amountFee: string;
  amountNet: string;
  customStr1?: string;
  customInt1?: string;
  nameFirst?: string;
  nameLast?: string;
  emailAddress?: string;
  merchantId: string;
  signature: string;
}

// M-Pesa Types
export interface MPesaSTKPushRequest {
  businessShortCode: string;
  password: string;
  timestamp: string;
  transactionType: 'CustomerPayBillOnline';
  amount: number;
  partyA: string; // Phone number
  partyB: string; // Business short code
  phoneNumber: string;
  callBackURL: string;
  accountReference: string;
  transactionDesc: string;
}

export interface MPesaSTKPushResponse {
  merchantRequestID: string;
  checkoutRequestID: string;
  responseCode: string;
  responseDescription: string;
  customerMessage: string;
}

export interface MPesaCallback {
  merchantRequestID: string;
  checkoutRequestID: string;
  resultCode: number;
  resultDesc: string;
  callbackMetadata?: {
    item: Array<{
      name: string;
      value: string | number;
    }>;
  };
}

// Voucher Types
export interface VoucherValidationRequest {
  voucherCode: string;
  voucherType: 'ott' | '1voucher';
  amount: Amount;
  merchantId: string;
  transactionId: string;
}

export interface VoucherValidationResponse {
  valid: boolean;
  balance?: Amount;
  expiresAt?: Date;
  error?: string;
  voucherDetails?: {
    serialNumber: string;
    batchNumber?: string;
    issueDate?: Date;
  };
}

export interface VoucherRedemptionRequest {
  voucherCode: string;
  voucherType: 'ott' | '1voucher';
  amount: Amount;
  merchantId: string;
  transactionId: string;
  validationToken?: string;
}

export interface VoucherRedemptionResponse {
  success: boolean;
  transactionId: string;
  remainingBalance?: Amount;
  redemptionDate: Date;
  error?: string;
}

// Provider Factory Type
export type PaymentProviderFactory = {
  polar: (config: PolarConfig) => PaymentProvider;
  payfast: (config: PayFastConfig) => PaymentProvider;
  mpesa: (config: MPesaConfig) => PaymentProvider;
  voucher: (config: VoucherConfig) => PaymentProvider;
};

// Provider Configuration Union Type
export type ProviderConfig =
  | { type: 'polar'; config: PolarConfig }
  | { type: 'payfast'; config: PayFastConfig }
  | { type: 'mpesa'; config: MPesaConfig }
  | { type: 'voucher'; config: VoucherConfig };
