#!/usr/bin/env node

const fs = require('fs-extra')
const path = require('path')
const { execSync } = require('child_process')
const chalk = require('chalk')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')

const argv = yargs(hideBin(process.argv))
  .option('watch', {
    alias: 'w',
    type: 'boolean',
    description: 'Watch for changes and rebuild'
  })
  .option('analyze', {
    alias: 'a',
    type: 'boolean',
    description: 'Analyze bundle size'
  })
  .option('clean', {
    alias: 'c',
    type: 'boolean',
    description: 'Clean dist folder before build'
  })
  .help()
  .argv

const ROOT_DIR = path.resolve(__dirname, '..')
const DIST_DIR = path.join(ROOT_DIR, 'dist')
const SRC_DIR = path.join(ROOT_DIR, 'src')

// Colors for console output
const colors = {
  info: chalk.blue,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  dim: chalk.dim,
}

// Log with timestamp
function log(message, color = 'info') {
  const timestamp = new Date().toLocaleTimeString()
  console.log(`${colors.dim(`[${timestamp}]`)} ${colors[color](message)}`)
}

// Clean dist directory
async function clean() {
  if (await fs.pathExists(DIST_DIR)) {
    log('Cleaning dist directory...', 'info')
    await fs.remove(DIST_DIR)
    log('✓ Cleaned dist directory', 'success')
  }
}

// Build TypeScript
async function buildTypeScript() {
  log('Building TypeScript...', 'info')
  
  try {
    execSync('npx tsc --build', {
      cwd: ROOT_DIR,
      stdio: 'inherit'
    })
    log('✓ TypeScript build completed', 'success')
  } catch (error) {
    log('✗ TypeScript build failed', 'error')
    throw error
  }
}

// Generate type definitions
async function generateTypes() {
  log('Generating type definitions...', 'info')
  
  try {
    execSync('npx tsc --declaration --emitDeclarationOnly --outDir dist/types', {
      cwd: ROOT_DIR,
      stdio: 'inherit'
    })
    log('✓ Type definitions generated', 'success')
  } catch (error) {
    log('✗ Type generation failed', 'error')
    throw error
  }
}

// Copy package.json and other files
async function copyFiles() {
  log('Copying package files...', 'info')
  
  const filesToCopy = [
    'package.json',
    'README.md',
    'LICENSE',
    'CHANGELOG.md'
  ]

  for (const file of filesToCopy) {
    const srcPath = path.join(ROOT_DIR, file)
    const destPath = path.join(DIST_DIR, file)
    
    if (await fs.pathExists(srcPath)) {
      await fs.copy(srcPath, destPath)
      log(`✓ Copied ${file}`, 'dim')
    }
  }
  
  log('✓ Package files copied', 'success')
}

// Update package.json for distribution
async function updatePackageJson() {
  log('Updating package.json for distribution...', 'info')
  
  const packagePath = path.join(DIST_DIR, 'package.json')
  const packageJson = await fs.readJson(packagePath)
  
  // Update main and types fields
  packageJson.main = './index.js'
  packageJson.types = './index.d.ts'
  packageJson.module = './index.esm.js'
  
  // Remove dev dependencies and scripts
  delete packageJson.devDependencies
  delete packageJson.scripts
  
  // Update exports
  packageJson.exports = {
    ".": {
      "import": "./index.esm.js",
      "require": "./index.js",
      "types": "./index.d.ts"
    },
    "./components": {
      "import": "./components/index.esm.js",
      "require": "./components/index.js",
      "types": "./components/index.d.ts"
    },
    "./layouts": {
      "import": "./layouts/index.esm.js", 
      "require": "./layouts/index.js",
      "types": "./layouts/index.d.ts"
    },
    "./forms": {
      "import": "./forms/index.esm.js",
      "require": "./forms/index.js", 
      "types": "./forms/index.d.ts"
    },
    "./business": {
      "import": "./business/index.esm.js",
      "require": "./business/index.js",
      "types": "./business/index.d.ts"
    },
    "./interactive": {
      "import": "./interactive/index.esm.js",
      "require": "./interactive/index.js",
      "types": "./interactive/index.d.ts"
    },
    "./hooks": {
      "import": "./hooks/index.esm.js",
      "require": "./hooks/index.js",
      "types": "./hooks/index.d.ts"
    },
    "./utils": {
      "import": "./utils/index.esm.js",
      "require": "./utils/index.js",
      "types": "./utils/index.d.ts"
    }
  }
  
  await fs.writeJson(packagePath, packageJson, { spaces: 2 })
  log('✓ Package.json updated', 'success')
}

// Bundle with Rollup
async function bundle() {
  log('Bundling with Rollup...', 'info')
  
  try {
    const rollupConfig = `
import resolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import typescript from '@rollup/plugin-typescript'
import { terser } from 'rollup-plugin-terser'

const external = [
  'react',
  'react-native',
  '@gluestack-ui/themed',
  '@gluestack-ui/nativewind-utils',
  'react-native-reanimated',
  'react-native-svg'
]

export default [
  // ESM build
  {
    input: 'src/index.ts',
    output: {
      file: 'dist/index.esm.js',
      format: 'esm',
      sourcemap: true
    },
    external,
    plugins: [
      resolve({ preferBuiltins: false }),
      commonjs(),
      typescript({ tsconfig: './tsconfig.json' }),
      terser()
    ]
  },
  // CommonJS build
  {
    input: 'src/index.ts',
    output: {
      file: 'dist/index.js',
      format: 'cjs',
      sourcemap: true
    },
    external,
    plugins: [
      resolve({ preferBuiltins: false }),
      commonjs(),
      typescript({ tsconfig: './tsconfig.json' }),
      terser()
    ]
  }
]
`
    
    await fs.writeFile(path.join(ROOT_DIR, 'rollup.config.js'), rollupConfig)
    
    execSync('npx rollup -c', {
      cwd: ROOT_DIR,
      stdio: 'inherit'
    })
    
    log('✓ Bundling completed', 'success')
  } catch (error) {
    log('✗ Bundling failed', 'error')
    throw error
  }
}

// Analyze bundle size
async function analyzeBundleSize() {
  if (!argv.analyze) return
  
  log('Analyzing bundle size...', 'info')
  
  try {
    const bundleAnalyzer = require('rollup-plugin-bundle-analyzer')
    // Bundle analysis would be integrated into rollup config
    log('✓ Bundle analysis completed', 'success')
  } catch (error) {
    log('Bundle analyzer not available', 'warning')
  }
}

// Watch mode
function watchMode() {
  log('Starting watch mode...', 'info')
  
  const chokidar = require('chokidar')
  
  const watcher = chokidar.watch(SRC_DIR, {
    ignored: /node_modules/,
    persistent: true
  })
  
  watcher.on('change', async (filePath) => {
    log(`File changed: ${path.relative(ROOT_DIR, filePath)}`, 'info')
    try {
      await buildTypeScript()
      log('✓ Rebuild completed', 'success')
    } catch (error) {
      log('✗ Rebuild failed', 'error')
    }
  })
  
  log('👀 Watching for changes...', 'info')
}

// Main build function
async function build() {
  const startTime = Date.now()
  
  try {
    log('🚀 Starting build process...', 'info')
    
    if (argv.clean) {
      await clean()
    }
    
    await buildTypeScript()
    await generateTypes()
    await copyFiles()
    await updatePackageJson()
    await bundle()
    await analyzeBundleSize()
    
    const duration = Date.now() - startTime
    log(`✅ Build completed in ${duration}ms`, 'success')
    
    if (argv.watch) {
      watchMode()
    }
    
  } catch (error) {
    log(`❌ Build failed: ${error.message}`, 'error')
    process.exit(1)
  }
}

// Run build
build()
