import axios from 'axios';

interface PushNotificationData {
  title: string;
  body: string;
  data?: any;
  sound?: string;
  badge?: number;
}

interface ExpoTicket {
  id: string;
  status: 'ok' | 'error';
  message?: string;
}

export class NotificationService {
  private readonly expoPushUrl = 'https://exp.host/--/api/v2/push/send';
  private readonly accessToken = process.env.EXPO_ACCESS_TOKEN;

  async sendPushNotification(
    pushTokens: string | string[],
    notification: PushNotificationData
  ): Promise<ExpoTicket[]> {
    if (!this.accessToken) {
      throw new Error('Expo access token not configured');
    }

    const tokens = Array.isArray(pushTokens) ? pushTokens : [pushTokens];
    
    // Filter valid Expo push tokens
    const validTokens = tokens.filter(token => 
      token.startsWith('ExponentPushToken[') || 
      token.startsWith('ExpoPushToken[')
    );

    if (validTokens.length === 0) {
      throw new Error('No valid push tokens provided');
    }

    const messages = validTokens.map(token => ({
      to: token,
      title: notification.title,
      body: notification.body,
      data: notification.data || {},
      sound: notification.sound || 'default',
      badge: notification.badge,
      channelId: 'hvppyplug-notifications',
      priority: 'high' as const,
    }));

    try {
      const response = await axios.post(this.expoPushUrl, messages, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      return response.data.data || [];
    } catch (error: any) {
      throw new Error(`Failed to send push notification: ${error.message}`);
    }
  }

  // Send order status update notifications
  async sendOrderStatusUpdate(
    customerToken: string,
    orderId: string,
    status: string,
    vendorName?: string
  ) {
    const statusMessages = {
      'pending': 'Your order has been placed successfully!',
      'accepted': `${vendorName || 'Vendor'} has accepted your order and is preparing it.`,
      'picked_up': 'Your order has been picked up and is on the way!',
      'en_route': 'Your delivery is on the way to you!',
      'delivered': 'Your order has been delivered. Enjoy your meal!',
      'cancelled': 'Your order has been cancelled.',
    };

    const message = statusMessages[status as keyof typeof statusMessages] || 
      'Your order status has been updated.';

    return await this.sendPushNotification(customerToken, {
      title: 'Order Update',
      body: message,
      data: {
        orderId,
        status,
        type: 'order_update',
      },
    });
  }

  // Send new order notifications to vendors
  async sendNewOrderToVendor(
    vendorToken: string,
    orderId: string,
    customerName: string,
    total: number
  ) {
    return await this.sendPushNotification(vendorToken, {
      title: 'New Order Received!',
      body: `Order from ${customerName} • R${total.toFixed(2)}`,
      data: {
        orderId,
        customerName,
        total,
        type: 'new_order',
      },
    });
  }

  // Send job assignment notifications to runners
  async sendJobAssignmentToRunner(
    runnerToken: string,
    orderId: string,
    vendorName: string,
    estimatedEarnings: number
  ) {
    return await this.sendPushNotification(runnerToken, {
      title: 'New Delivery Job!',
      body: `Pickup from ${vendorName} • Earn ~R${estimatedEarnings.toFixed(2)}`,
      data: {
        orderId,
        vendorName,
        estimatedEarnings,
        type: 'job_assignment',
      },
    });
  }

  // Send promotional notifications
  async sendPromotion(
    pushTokens: string | string[],
    title: string,
    message: string,
    promoCode?: string
  ) {
    return await this.sendPushNotification(pushTokens, {
      title,
      body: message,
      data: {
        promoCode,
        type: 'promotion',
      },
    });
  }

  // Send location-based notifications
  async sendLocationBasedNotification(
    pushTokens: string | string[],
    title: string,
    message: string,
    location: { lat: number; lng: number }
  ) {
    return await this.sendPushNotification(pushTokens, {
      title,
      body: message,
      data: {
        location,
        type: 'location_based',
      },
    });
  }

  // Batch send notifications
  async sendBatchNotifications(
    notifications: Array<{
      tokens: string | string[];
      notification: PushNotificationData;
    }>
  ): Promise<ExpoTicket[][]> {
    const promises = notifications.map(({ tokens, notification }) =>
      this.sendPushNotification(tokens, notification)
    );

    return Promise.all(promises);
  }
}

export const notificationService = new NotificationService();
