"use client"

import React, { useState, useCallback } from 'react'
import { View, Pressable, Alert } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { 
  UploadIcon, 
  FileIcon, 
  ImageIcon, 
  VideoIcon, 
  XIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  LoaderIcon 
} from 'lucide-react-native'
import * as DocumentPicker from 'expo-document-picker'
import * as ImagePicker from 'expo-image-picker'

// Types
export interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  uri: string
  uploadProgress?: number
  uploadStatus?: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

export interface FileUploadProps {
  value?: UploadedFile[]
  onChange?: (files: UploadedFile[]) => void
  onUpload?: (file: UploadedFile) => Promise<string> // Returns uploaded URL
  accept?: string[]
  multiple?: boolean
  maxFiles?: number
  maxSize?: number // in bytes
  disabled?: boolean
  placeholder?: string
  className?: string
  variant?: 'dropzone' | 'button' | 'compact'
  showPreview?: boolean
  allowCamera?: boolean
  allowGallery?: boolean
}

// Styles
const containerStyles = tva({
  base: "w-full",
  variants: {
    disabled: {
      true: "opacity-50",
    },
  },
})

const dropzoneStyles = tva({
  base: "border-2 border-dashed rounded-lg p-6 items-center justify-center min-h-32",
  variants: {
    variant: {
      dropzone: "border-outline-300 bg-background-50",
      button: "border-primary-300 bg-primary-50",
      compact: "border-outline-200 bg-background-0 p-4 min-h-20",
    },
    dragOver: {
      true: "border-primary-500 bg-primary-100",
    },
    error: {
      true: "border-error-500 bg-error-50",
    },
  },
})

const fileItemStyles = tva({
  base: "flex-row items-center p-3 bg-background-0 border border-outline-200 rounded-lg mb-2",
  variants: {
    status: {
      pending: "bg-background-50",
      uploading: "bg-info-50",
      success: "bg-success-50",
      error: "bg-error-50",
    },
  },
})

const progressBarStyles = tva({
  base: "h-1 bg-outline-200 rounded-full overflow-hidden mt-2",
})

const progressFillStyles = tva({
  base: "h-full bg-primary-500 rounded-full",
})

export function FileUpload({
  value = [],
  onChange,
  onUpload,
  accept = [],
  multiple = false,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  disabled = false,
  placeholder = "Drop files here or click to upload",
  className,
  variant = 'dropzone',
  showPreview = true,
  allowCamera = true,
  allowGallery = true,
}: FileUploadProps) {
  const [dragOver, setDragOver] = useState(false)
  const [uploading, setUploading] = useState(false)

  // Get file icon based on type
  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return ImageIcon
    if (type.startsWith('video/')) return VideoIcon
    return FileIcon
  }

  // Get status icon
  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'uploading':
        return LoaderIcon
      case 'success':
        return CheckCircleIcon
      case 'error':
        return AlertCircleIcon
      default:
        return null
    }
  }

  // Validate file
  const validateFile = (file: any): string | null => {
    if (maxSize && file.size > maxSize) {
      return `File size must be less than ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    }

    if (accept.length > 0 && !accept.some(type => file.type?.includes(type))) {
      return `File type not supported. Accepted: ${accept.join(', ')}`
    }

    if (!multiple && value.length >= 1) {
      return 'Only one file allowed'
    }

    if (value.length >= maxFiles) {
      return `Maximum ${maxFiles} files allowed`
    }

    return null
  }

  // Process selected files
  const processFiles = useCallback(async (selectedFiles: any[]) => {
    const newFiles: UploadedFile[] = []

    for (const file of selectedFiles) {
      const error = validateFile(file)
      
      const uploadedFile: UploadedFile = {
        id: `${Date.now()}-${Math.random()}`,
        name: file.name,
        size: file.size,
        type: file.type || file.mimeType,
        uri: file.uri,
        uploadStatus: error ? 'error' : 'pending',
        error,
      }

      newFiles.push(uploadedFile)
    }

    const updatedFiles = multiple ? [...value, ...newFiles] : newFiles
    onChange?.(updatedFiles)

    // Upload files without errors
    for (const file of newFiles.filter(f => !f.error)) {
      await uploadFile(file)
    }
  }, [value, multiple, onChange, maxSize, accept, maxFiles])

  // Upload single file
  const uploadFile = async (file: UploadedFile) => {
    if (!onUpload) {
      // Mark as success if no upload handler
      updateFileStatus(file.id, 'success')
      return
    }

    try {
      setUploading(true)
      updateFileStatus(file.id, 'uploading', 0)

      // Simulate progress (in real app, this would come from upload progress)
      const progressInterval = setInterval(() => {
        updateFileProgress(file.id, Math.min(90, (file.uploadProgress || 0) + 10))
      }, 200)

      const uploadedUrl = await onUpload(file)
      
      clearInterval(progressInterval)
      updateFileStatus(file.id, 'success', 100)
      
    } catch (error) {
      updateFileStatus(file.id, 'error', 0, error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  // Update file status
  const updateFileStatus = (
    fileId: string, 
    status: UploadedFile['uploadStatus'], 
    progress?: number,
    error?: string
  ) => {
    const updatedFiles = value.map(file => 
      file.id === fileId 
        ? { 
            ...file, 
            uploadStatus: status, 
            uploadProgress: progress,
            error: error || (status === 'error' ? file.error : undefined)
          }
        : file
    )
    onChange?.(updatedFiles)
  }

  // Update file progress
  const updateFileProgress = (fileId: string, progress: number) => {
    const updatedFiles = value.map(file => 
      file.id === fileId ? { ...file, uploadProgress: progress } : file
    )
    onChange?.(updatedFiles)
  }

  // Remove file
  const removeFile = (fileId: string) => {
    const updatedFiles = value.filter(file => file.id !== fileId)
    onChange?.(updatedFiles)
  }

  // Handle document picker
  const handleDocumentPicker = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        multiple,
        type: accept.length > 0 ? accept : '*/*',
      })

      if (!result.canceled) {
        await processFiles(result.assets)
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick document')
    }
  }

  // Handle image picker
  const handleImagePicker = async (useCamera: boolean = false) => {
    try {
      const result = useCamera
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.All,
            allowsEditing: true,
            quality: 1,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.All,
            allowsMultipleSelection: multiple,
            quality: 1,
          })

      if (!result.canceled) {
        await processFiles(result.assets)
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image')
    }
  }

  // Show picker options
  const showPickerOptions = () => {
    const options = []
    
    if (allowGallery) options.push({ text: 'Choose from Gallery', onPress: () => handleImagePicker(false) })
    if (allowCamera) options.push({ text: 'Take Photo', onPress: () => handleImagePicker(true) })
    options.push({ text: 'Choose Document', onPress: handleDocumentPicker })
    options.push({ text: 'Cancel', style: 'cancel' })

    Alert.alert('Select File', 'Choose how you want to add files', options)
  }

  // Render file item
  const renderFileItem = (file: UploadedFile) => {
    const FileIconComponent = getFileIcon(file.type)
    const StatusIconComponent = getStatusIcon(file.uploadStatus)

    return (
      <View key={file.id} className={fileItemStyles({ status: file.uploadStatus })}>
        <FileIconComponent size={24} className="text-typography-600 mr-3" />
        
        <View className="flex-1">
          <Text className="font-medium text-typography-900" numberOfLines={1}>
            {file.name}
          </Text>
          <Text className="text-sm text-typography-600">
            {(file.size / 1024).toFixed(1)} KB
          </Text>
          
          {file.uploadStatus === 'uploading' && (
            <View className={progressBarStyles()}>
              <View 
                className={progressFillStyles()} 
                style={{ width: `${file.uploadProgress || 0}%` }}
              />
            </View>
          )}
          
          {file.error && (
            <Text className="text-sm text-error-600 mt-1">
              {file.error}
            </Text>
          )}
        </View>

        <View className="flex-row items-center ml-2">
          {StatusIconComponent && (
            <StatusIconComponent 
              size={20} 
              className={`mr-2 ${
                file.uploadStatus === 'success' ? 'text-success-600' :
                file.uploadStatus === 'error' ? 'text-error-600' :
                file.uploadStatus === 'uploading' ? 'text-info-600 animate-spin' :
                'text-typography-400'
              }`}
            />
          )}
          
          <Pressable onPress={() => removeFile(file.id)}>
            <XIcon size={20} className="text-typography-400" />
          </Pressable>
        </View>
      </View>
    )
  }

  // Render dropzone
  const renderDropzone = () => (
    <Pressable
      className={dropzoneStyles({ 
        variant, 
        dragOver, 
        error: value.some(f => f.error) 
      })}
      onPress={disabled ? undefined : showPickerOptions}
      disabled={disabled}
    >
      <UploadIcon size={32} className="text-typography-400 mb-2" />
      <Text className="text-center text-typography-600 mb-1">
        {placeholder}
      </Text>
      <Text className="text-sm text-typography-500 text-center">
        {accept.length > 0 && `Supported: ${accept.join(', ')}`}
        {maxSize && ` • Max size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`}
      </Text>
    </Pressable>
  )

  return (
    <View className={containerStyles({ disabled, class: className })}>
      {renderDropzone()}
      
      {showPreview && value.length > 0 && (
        <View className="mt-4">
          <Text className="font-medium text-typography-700 mb-2">
            Uploaded Files ({value.length})
          </Text>
          {value.map(renderFileItem)}
        </View>
      )}
    </View>
  )
}
