"use client"

import React, { useState } from 'react'
import { View, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  MoreHorizontalIcon,
  RefreshCwIcon,
  ExternalLinkIcon 
} from 'lucide-react-native'

// Types
export interface DashboardWidgetData {
  title: string
  value: string | number
  subtitle?: string
  trend?: {
    value: number
    direction: 'up' | 'down' | 'neutral'
    label?: string
  }
  comparison?: {
    value: string | number
    label: string
  }
  status?: 'success' | 'warning' | 'error' | 'info' | 'neutral'
}

export interface DashboardWidgetAction {
  label: string
  icon?: React.ComponentType<any>
  onPress: () => void
}

export interface DashboardWidgetProps {
  data: DashboardWidgetData
  loading?: boolean
  refreshing?: boolean
  onRefresh?: () => void
  onPress?: () => void
  actions?: DashboardWidgetAction[]
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'outlined' | 'filled'
  className?: string
  children?: React.ReactNode
}

// Styles
const widgetStyles = tva({
  base: "rounded-lg border border-outline-200 overflow-hidden",
  variants: {
    variant: {
      default: "bg-background-0",
      outlined: "bg-background-0 border-2",
      filled: "bg-background-50",
    },
    size: {
      small: "p-3",
      medium: "p-4",
      large: "p-6",
    },
    pressable: {
      true: "active:bg-background-50",
    },
    loading: {
      true: "opacity-60",
    },
  },
})

const headerStyles = tva({
  base: "flex-row items-center justify-between mb-3",
})

const titleStyles = tva({
  base: "font-medium text-typography-700",
  variants: {
    size: {
      small: "text-sm",
      medium: "text-base",
      large: "text-lg",
    },
  },
})

const valueStyles = tva({
  base: "font-bold text-typography-900",
  variants: {
    size: {
      small: "text-lg",
      medium: "text-2xl",
      large: "text-3xl",
    },
  },
})

const trendStyles = tva({
  base: "flex-row items-center mt-2",
  variants: {
    direction: {
      up: "text-success-600",
      down: "text-error-600",
      neutral: "text-typography-500",
    },
  },
})

const statusIndicatorStyles = tva({
  base: "w-2 h-2 rounded-full mr-2",
  variants: {
    status: {
      success: "bg-success-500",
      warning: "bg-warning-500",
      error: "bg-error-500",
      info: "bg-info-500",
      neutral: "bg-typography-300",
    },
  },
})

const actionButtonStyles = tva({
  base: "p-2 rounded-lg",
  variants: {
    variant: {
      ghost: "hover:bg-background-100",
      filled: "bg-background-100",
    },
  },
})

export function DashboardWidget({
  data,
  loading = false,
  refreshing = false,
  onRefresh,
  onPress,
  actions = [],
  size = 'medium',
  variant = 'default',
  className,
  children,
}: DashboardWidgetProps) {
  const [showActions, setShowActions] = useState(false)

  // Format large numbers
  const formatValue = (value: string | number): string => {
    if (typeof value === 'string') return value
    
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  }

  // Get trend icon
  const getTrendIcon = () => {
    if (!data.trend) return null
    
    switch (data.trend.direction) {
      case 'up':
        return <TrendingUpIcon size={16} className="text-success-600 mr-1" />
      case 'down':
        return <TrendingDownIcon size={16} className="text-error-600 mr-1" />
      default:
        return null
    }
  }

  // Render header
  const renderHeader = () => (
    <View className={headerStyles()}>
      <View className="flex-row items-center flex-1">
        {data.status && (
          <View className={statusIndicatorStyles({ status: data.status })} />
        )}
        <Text className={titleStyles({ size })} numberOfLines={1}>
          {data.title}
        </Text>
      </View>
      
      <View className="flex-row items-center">
        {onRefresh && (
          <Pressable
            className={actionButtonStyles({ variant: 'ghost' })}
            onPress={onRefresh}
            disabled={refreshing}
          >
            <RefreshCwIcon 
              size={16} 
              className={`text-typography-500 ${refreshing ? 'animate-spin' : ''}`} 
            />
          </Pressable>
        )}
        
        {actions.length > 0 && (
          <Pressable
            className={actionButtonStyles({ variant: 'ghost' })}
            onPress={() => setShowActions(!showActions)}
          >
            <MoreHorizontalIcon size={16} className="text-typography-500" />
          </Pressable>
        )}
        
        {onPress && (
          <Pressable
            className={actionButtonStyles({ variant: 'ghost' })}
            onPress={onPress}
          >
            <ExternalLinkIcon size={16} className="text-typography-500" />
          </Pressable>
        )}
      </View>
    </View>
  )

  // Render main content
  const renderContent = () => (
    <View>
      <Text className={valueStyles({ size })}>
        {formatValue(data.value)}
      </Text>
      
      {data.subtitle && (
        <Text className="text-typography-600 text-sm mt-1">
          {data.subtitle}
        </Text>
      )}
      
      {data.trend && (
        <View className={trendStyles({ direction: data.trend.direction })}>
          {getTrendIcon()}
          <Text className={`text-sm font-medium ${
            data.trend.direction === 'up' ? 'text-success-600' :
            data.trend.direction === 'down' ? 'text-error-600' :
            'text-typography-500'
          }`}>
            {data.trend.value > 0 ? '+' : ''}{data.trend.value}%
          </Text>
          {data.trend.label && (
            <Text className="text-typography-500 text-sm ml-1">
              {data.trend.label}
            </Text>
          )}
        </View>
      )}
      
      {data.comparison && (
        <View className="mt-2 pt-2 border-t border-outline-100">
          <Text className="text-typography-600 text-sm">
            {data.comparison.label}: {formatValue(data.comparison.value)}
          </Text>
        </View>
      )}
    </View>
  )

  // Render actions dropdown
  const renderActions = () => {
    if (!showActions || actions.length === 0) return null
    
    return (
      <View className="absolute top-12 right-0 bg-background-0 border border-outline-200 rounded-lg shadow-lg z-10 min-w-32">
        {actions.map((action, index) => (
          <Pressable
            key={index}
            className="flex-row items-center px-3 py-2 hover:bg-background-50"
            onPress={() => {
              action.onPress()
              setShowActions(false)
            }}
          >
            {action.icon && (
              <action.icon size={16} className="text-typography-600 mr-2" />
            )}
            <Text className="text-typography-700 text-sm">
              {action.label}
            </Text>
          </Pressable>
        ))}
      </View>
    )
  }

  return (
    <Pressable
      className={widgetStyles({ 
        variant, 
        size, 
        pressable: !!onPress,
        loading,
        class: className 
      })}
      onPress={onPress}
      disabled={loading}
    >
      <View className="relative">
        {renderHeader()}
        {renderContent()}
        {children}
        {renderActions()}
      </View>
    </Pressable>
  )
}

// Predefined widget variants for common use cases
export const MetricWidget = (props: Omit<DashboardWidgetProps, 'variant'>) => (
  <DashboardWidget {...props} variant="outlined" />
)

export const KPIWidget = (props: Omit<DashboardWidgetProps, 'variant' | 'size'>) => (
  <DashboardWidget {...props} variant="filled" size="large" />
)

export const CompactWidget = (props: Omit<DashboardWidgetProps, 'size'>) => (
  <DashboardWidget {...props} size="small" />
)
