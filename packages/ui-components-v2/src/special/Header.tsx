import React from "react";
import {
  Box,
  HStack,
  VStack,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  SearchIcon,
  Badge,
  BadgeText,
  Button,
  ButtonText,
} from "@/ui";
import HeaderTabs from "./header/HeaderTabs";
import MarketplaceLogo from "./header/MarketplaceLogo";
import ToggleMode from "./header/ToggleMode";
import UserProfile from "./header/UserProfile";
import ShoppingCart from "./header/ShoppingCart";
// import { ThemeContext } from "@/App";

export interface HeaderProps {
  /** Logo configuration */
  logo?: {
    src?: string;
    alt?: string;
    width?: string;
    height?: string;
  };
  /** Search configuration */
  search?: {
    placeholder?: string;
    onSearch?: (query: string) => void;
    suggestions?: string[];
  };
  /** Cart configuration */
  cart?: {
    itemCount?: number;
    onCartClick?: () => void;
    showBadge?: boolean;
  };
  /** User profile configuration */
  user?: {
    isLoggedIn?: boolean;
    avatar?: string;
    name?: string;
    onProfileClick?: () => void;
    onLoginClick?: () => void;
  };
  /** Navigation tabs */
  tabs?: Array<{
    label: string;
    value: string;
    href?: string;
    onClick?: () => void;
  }>;
  /** Additional actions */
  actions?: React.ReactNode;
  /** Theme toggle visibility */
  showThemeToggle?: boolean;
  /** Vendor/seller quick access */
  vendorAccess?: {
    show?: boolean;
    onVendorDashboard?: () => void;
    onBecomeVendor?: () => void;
  };
}

const Header = React.memo<HeaderProps>(({
  logo,
  search = { placeholder: "Search products, brands, categories..." },
  cart = { itemCount: 0, showBadge: true },
  user = { isLoggedIn: false },
  tabs,
  actions,
  showThemeToggle = true,
  vendorAccess = { show: true }
}) => {
  // Theme support can be added later
  const colorMode = 'light';
  
  return (
    <>
      {/* Desktop Header */}
      <Box className="px-16 w-full border-b hidden md:flex border-outline-100 min-h-20">
        <HStack className="items-center justify-between mx-auto w-full">
          <MarketplaceLogo {...logo} />
          
          {/* Search Bar - Desktop */}
          <Box className="flex-1 max-w-2xl mx-8">
            <Input variant="rounded" size="md" className="w-full">
              <InputField 
                placeholder={search.placeholder}
                onChangeText={search.onSearch}
              />
              <InputSlot className="bg-primary-500 rounded-full h-8 w-8 m-1">
                <InputIcon
                  as={SearchIcon}
                  color={colorMode === "light" ? "#FEFEFF" : "#171717"}
                />
              </InputSlot>
            </Input>
          </Box>

          <HStack space="lg" className="items-center">
            {/* Vendor Access */}
            {vendorAccess.show && (
              <Button 
                variant="outline" 
                size="sm"
                onPress={vendorAccess.onVendorDashboard}
              >
                <ButtonText>Sell on Platform</ButtonText>
              </Button>
            )}
            
            {/* Shopping Cart */}
            <ShoppingCart 
              itemCount={cart.itemCount}
              onPress={cart.onCartClick}
              showBadge={cart.showBadge}
            />
            
            {/* Theme Toggle */}
            {showThemeToggle && <ToggleMode />}
            
            {/* User Profile */}
            <UserProfile 
              isLoggedIn={user.isLoggedIn}
              avatar={user.avatar}
              name={user.name}
              onProfileClick={user.onProfileClick}
              onLoginClick={user.onLoginClick}
            />
            
            {/* Additional Actions */}
            {actions}
          </HStack>
        </HStack>
      </Box>

      {/* Navigation Tabs - Desktop */}
      {tabs && (
        <Box className="px-16 w-full border-b hidden md:flex border-outline-50">
          <HeaderTabs tabs={tabs} />
        </Box>
      )}

      {/* Mobile Header */}
      <Box className="p-4 md:hidden w-full">
        <VStack space="md">
          {/* Top Row - Logo, Cart, Profile */}
          <HStack className="items-center justify-between w-full">
            <MarketplaceLogo {...logo} compact />
            <HStack space="md" className="items-center">
              <ShoppingCart 
                itemCount={cart.itemCount}
                onPress={cart.onCartClick}
                showBadge={cart.showBadge}
                size="sm"
              />
              <UserProfile 
                isLoggedIn={user.isLoggedIn}
                avatar={user.avatar}
                onProfileClick={user.onProfileClick}
                onLoginClick={user.onLoginClick}
                compact
              />
            </HStack>
          </HStack>

          {/* Search Bar - Mobile */}
          <Input variant="rounded" size="sm" className="w-full">
            <InputField 
              placeholder={search.placeholder}
              onChangeText={search.onSearch}
            />
            <InputSlot className="bg-primary-500 rounded-full h-6 w-6 m-1.5">
              <InputIcon
                as={SearchIcon}
                color={colorMode === "light" ? "#FEFEFF" : "#171717"}
              />
            </InputSlot>
          </Input>
        </VStack>
      </Box>
    </>
  );
});

Header.displayName = "Header";
export default Header;
