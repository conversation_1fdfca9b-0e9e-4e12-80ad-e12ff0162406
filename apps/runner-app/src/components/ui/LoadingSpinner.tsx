import React from 'react'
import { View, ActivityIndicator, Text, ViewStyle, TextStyle } from 'react-native'
import { useTheme } from '../../providers/ThemeProvider'

export interface LoadingSpinnerProps {
  size?: 'small' | 'large' | number
  color?: string
  message?: string
  overlay?: boolean
  style?: ViewStyle
  textStyle?: TextStyle
  testID?: string
}

export function LoadingSpinner({
  size = 'large',
  color,
  message,
  overlay = false,
  style,
  textStyle,
  testID,
}: LoadingSpinnerProps) {
  const { theme } = useTheme()

  const spinnerColor = color || theme.colors.primary[600]

  const getContainerStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      alignItems: 'center',
      justifyContent: 'center',
    }

    if (overlay) {
      return {
        ...baseStyles,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        zIndex: 1000,
        ...style,
      }
    }

    return {
      ...baseStyles,
      padding: theme.spacing.lg,
      ...style,
    }
  }

  const getTextStyles = (): TextStyle => {
    return {
      fontSize: theme.fontSize.base,
      color: theme.colors.gray[600],
      marginTop: theme.spacing.md,
      textAlign: 'center',
      ...textStyle,
    }
  }

  return (
    <View style={getContainerStyles()} testID={testID}>
      <ActivityIndicator size={size} color={spinnerColor} />
      {message && (
        <Text style={getTextStyles()}>
          {message}
        </Text>
      )}
    </View>
  )
}

// Full screen loading component
export interface FullScreenLoadingProps {
  message?: string
  visible?: boolean
}

export function FullScreenLoading({ 
  message = 'Loading...', 
  visible = true 
}: FullScreenLoadingProps) {
  if (!visible) return null

  return (
    <LoadingSpinner
      overlay
      message={message}
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
      }}
      textStyle={{
        color: 'white',
      }}
    />
  )
}

// Inline loading for buttons and small components
export interface InlineLoadingProps {
  size?: 'small' | 'large' | number
  color?: string
  style?: ViewStyle
}

export function InlineLoading({ 
  size = 'small', 
  color, 
  style 
}: InlineLoadingProps) {
  const { theme } = useTheme()
  
  return (
    <View style={[{ padding: theme.spacing.xs }, style]}>
      <ActivityIndicator 
        size={size} 
        color={color || theme.colors.primary[600]} 
      />
    </View>
  )
}
