#!/usr/bin/env node

/**
 * HVPPYPlug+ Import Update Script
 * Automatically updates import statements to use the new mobile-services package
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`)

// Configuration
const ROOT_DIR = path.resolve(__dirname, '..')
const APPS_DIR = path.join(ROOT_DIR, 'apps')
const PACKAGES_DIR = path.join(ROOT_DIR, 'packages')

// Import mappings
const IMPORT_MAPPINGS = {
  // Services
  "from '../services/LocationService'": "from '@hvppyplug/mobile-services'",
  "from '../services/NotificationService'": "from '@hvppyplug/mobile-services'",
  "from '../services/CameraService'": "from '@hvppyplug/mobile-services'",
  "from '../services/AppwriteService'": "from '@hvppyplug/mobile-services'",
  "from './services/LocationService'": "from '@hvppyplug/mobile-services'",
  "from './services/NotificationService'": "from '@hvppyplug/mobile-services'",
  "from './services/CameraService'": "from '@hvppyplug/mobile-services'",
  "from './services/AppwriteService'": "from '@hvppyplug/mobile-services'",
  
  // Hooks
  "from '../hooks/useLocation'": "from '@hvppyplug/mobile-services'",
  "from '../hooks/useNotifications'": "from '@hvppyplug/mobile-services'",
  "from '../hooks/useCamera'": "from '@hvppyplug/mobile-services'",
  "from './hooks/useLocation'": "from '@hvppyplug/mobile-services'",
  "from './hooks/useNotifications'": "from '@hvppyplug/mobile-services'",
  "from './hooks/useCamera'": "from '@hvppyplug/mobile-services'",
  
  // UI Components package references
  "from '@hvppyplug/compound-components'": "from '@hvppyplug/mobile-services'",
  "from '@hvppyplug/ui-components-v2'": "from '@hvppyplug/mobile-services'",
}

// File extensions to process
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx']

/**
 * Get all files recursively from a directory
 */
function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) {
    return fileList
  }

  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // Skip node_modules and dist directories
      if (!['node_modules', 'dist', '.git', '.expo'].includes(file)) {
        getAllFiles(filePath, fileList)
      }
    } else {
      // Only process relevant file types
      if (FILE_EXTENSIONS.includes(path.extname(file))) {
        fileList.push(filePath)
      }
    }
  })
  
  return fileList
}

/**
 * Update imports in a single file
 */
function updateImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let hasChanges = false
    
    // Apply import mappings
    Object.entries(IMPORT_MAPPINGS).forEach(([oldImport, newImport]) => {
      const regex = new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
      if (content.includes(oldImport)) {
        content = content.replace(regex, newImport)
        hasChanges = true
      }
    })
    
    // Special handling for service imports that might need specific updates
    const serviceImportRegex = /import\s*{([^}]+)}\s*from\s*['"]@hvppyplug\/(compound-components|ui-components-v2)['"];?/g
    let match
    
    while ((match = serviceImportRegex.exec(content)) !== null) {
      const imports = match[1].split(',').map(imp => imp.trim())
      const serviceImports = imports.filter(imp => 
        imp.includes('LocationService') || 
        imp.includes('NotificationService') || 
        imp.includes('CameraService') || 
        imp.includes('AppwriteService') ||
        imp.includes('useLocation') ||
        imp.includes('useNotifications') ||
        imp.includes('useCamera')
      )
      
      if (serviceImports.length > 0) {
        const otherImports = imports.filter(imp => !serviceImports.includes(imp))
        
        // Replace with mobile-services import
        let replacement = `import { ${serviceImports.join(', ')} } from '@hvppyplug/mobile-services'`
        
        // Add remaining imports if any
        if (otherImports.length > 0) {
          replacement += `\nimport { ${otherImports.join(', ')} } from '@hvppyplug/${match[2]}'`
        }
        
        content = content.replace(match[0], replacement)
        hasChanges = true
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8')
      return true
    }
    
    return false
  } catch (error) {
    log('red', `❌ Error updating ${filePath}: ${error.message}`)
    return false
  }
}

/**
 * Update package.json dependencies
 */
function updatePackageJsonDependencies(packageJsonPath) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    let hasChanges = false
    
    // Add mobile-services dependency
    if (!packageJson.dependencies) {
      packageJson.dependencies = {}
    }
    
    if (!packageJson.dependencies['@hvppyplug/mobile-services']) {
      packageJson.dependencies['@hvppyplug/mobile-services'] = 'workspace:*'
      hasChanges = true
    }
    
    if (hasChanges) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n', 'utf8')
      return true
    }
    
    return false
  } catch (error) {
    log('red', `❌ Error updating ${packageJsonPath}: ${error.message}`)
    return false
  }
}

/**
 * Main execution
 */
function main() {
  log('blue', '🚀 Starting import update process...')
  
  let totalFilesProcessed = 0
  let totalFilesUpdated = 0
  let totalPackagesUpdated = 0
  
  // Process apps directory
  log('yellow', '\n📱 Processing apps...')
  const appsToProcess = ['customer-app', 'vendor-app', 'runner-app']
  
  appsToProcess.forEach(appName => {
    const appDir = path.join(APPS_DIR, appName)
    if (fs.existsSync(appDir)) {
      log('cyan', `\n  Processing ${appName}...`)
      
      // Update source files
      const files = getAllFiles(appDir)
      files.forEach(file => {
        totalFilesProcessed++
        if (updateImportsInFile(file)) {
          totalFilesUpdated++
          log('green', `    ✅ Updated ${path.relative(ROOT_DIR, file)}`)
        }
      })
      
      // Update package.json
      const packageJsonPath = path.join(appDir, 'package.json')
      if (fs.existsSync(packageJsonPath)) {
        if (updatePackageJsonDependencies(packageJsonPath)) {
          totalPackagesUpdated++
          log('green', `    ✅ Updated ${appName}/package.json`)
        }
      }
    } else {
      log('yellow', `    ⚠️ ${appName} directory not found`)
    }
  })
  
  // Process packages directory (excluding mobile-services itself)
  log('yellow', '\n📦 Processing packages...')
  const packagesToProcess = ['ui-components-v2', 'common']
  
  packagesToProcess.forEach(packageName => {
    const packageDir = path.join(PACKAGES_DIR, packageName)
    if (fs.existsSync(packageDir)) {
      log('cyan', `\n  Processing ${packageName}...`)
      
      // Update source files
      const files = getAllFiles(packageDir)
      files.forEach(file => {
        totalFilesProcessed++
        if (updateImportsInFile(file)) {
          totalFilesUpdated++
          log('green', `    ✅ Updated ${path.relative(ROOT_DIR, file)}`)
        }
      })
      
      // Update package.json
      const packageJsonPath = path.join(packageDir, 'package.json')
      if (fs.existsSync(packageJsonPath)) {
        if (updatePackageJsonDependencies(packageJsonPath)) {
          totalPackagesUpdated++
          log('green', `    ✅ Updated ${packageName}/package.json`)
        }
      }
    } else {
      log('yellow', `    ⚠️ ${packageName} directory not found`)
    }
  })
  
  // Clean up old service files from ui-components-v2
  log('yellow', '\n🧹 Cleaning up old service files...')
  const uiComponentsServicesDir = path.join(PACKAGES_DIR, 'ui-components-v2', 'src', 'services')
  if (fs.existsSync(uiComponentsServicesDir)) {
    const servicesToRemove = ['LocationService', 'NotificationService', 'CameraService', 'AppwriteService']
    servicesToRemove.forEach(service => {
      const serviceDir = path.join(uiComponentsServicesDir, service)
      if (fs.existsSync(serviceDir)) {
        fs.rmSync(serviceDir, { recursive: true, force: true })
        log('green', `    ✅ Removed ${service} directory`)
      }
    })
    
    // Remove services index if it only exports mobile services
    const servicesIndexPath = path.join(uiComponentsServicesDir, 'index.ts')
    if (fs.existsSync(servicesIndexPath)) {
      const content = fs.readFileSync(servicesIndexPath, 'utf8')
      const lines = content.split('\n').filter(line => 
        line.trim() && 
        !line.includes('LocationService') &&
        !line.includes('NotificationService') &&
        !line.includes('CameraService') &&
        !line.includes('AppwriteService')
      )
      
      if (lines.length === 0) {
        fs.unlinkSync(servicesIndexPath)
        log('green', `    ✅ Removed empty services index`)
      } else {
        fs.writeFileSync(servicesIndexPath, lines.join('\n') + '\n', 'utf8')
        log('green', `    ✅ Updated services index`)
      }
    }
  }
  
  // Clean up old hooks from ui-components-v2
  const uiComponentsHooksPath = path.join(PACKAGES_DIR, 'ui-components-v2', 'src', 'hooks')
  if (fs.existsSync(uiComponentsHooksPath)) {
    const hooksToRemove = ['useLocation.ts', 'useNotifications.ts', 'useCamera.ts']
    hooksToRemove.forEach(hook => {
      const hookPath = path.join(uiComponentsHooksPath, hook)
      if (fs.existsSync(hookPath)) {
        fs.unlinkSync(hookPath)
        log('green', `    ✅ Removed ${hook}`)
      }
    })
  }
  
  // Summary
  log('green', '\n🎉 Import update completed!')
  log('blue', `\n📊 Summary:`)
  log('blue', `  • Files processed: ${totalFilesProcessed}`)
  log('blue', `  • Files updated: ${totalFilesUpdated}`)
  log('blue', `  • Package.json files updated: ${totalPackagesUpdated}`)
  
  log('yellow', '\n📝 Next steps:')
  log('yellow', '  1. Run "pnpm install" in the root directory')
  log('yellow', '  2. Build the mobile-services package: "cd packages/mobile-services && pnpm build"')
  log('yellow', '  3. Test your applications to ensure everything works')
  log('yellow', '  4. Commit the changes')
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { updateImportsInFile, updatePackageJsonDependencies }
