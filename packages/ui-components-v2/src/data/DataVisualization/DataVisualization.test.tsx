import React from 'react'
import { render } from '@testing-library/react-native'
import { DataVisualization } from '../DataVisualization'

describe('DataVisualization', () => {
  it('renders correctly', () => {
    const { getByText } = render(<DataVisualization />)
    expect(getByText('DataVisualization Component')).toBeTruthy()
  })

  it('applies custom className', () => {
    const { getByTestId } = render(
      <DataVisualization className="custom-class" testID="datavisualization" />
    )
    expect(getByTestId('datavisualization')).toBeTruthy()
  })

  it('handles disabled state', () => {
    const { getByTestId } = render(
      <DataVisualization disabled testID="datavisualization" />
    )
    expect(getByTestId('datavisualization')).toBeTruthy()
  })
})
