import React, { useState, useEffect, useRef } from 'react'
import { View, Text, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeftIcon,
  MapPinIcon,
  ClockIcon,
  PhoneIcon,
  MessageCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertCircleIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useOrdersStore } from '../../stores/ordersStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import { realtimeService, type RealtimeSubscription, type OrderUpdate } from '../../services/realtimeService'

// Import UI components from the library
// import { LiveTrackingMap } from '@hvppyplug/ui-components-v2'
// import { OrderProgressTracker } from '@hvppyplug/ui-components-v2'
// import { StatusBadge } from '@hvppyplug/ui-components-v2'

// Temporary placeholder components
const LiveTrackingMap = ({ location, providerLocation, route }: any) => {
  return (
    <View className="h-64 bg-gray-200 rounded-lg justify-center items-center">
      <Text className="text-gray-600">Map Placeholder</Text>
    </View>
  )
}

const OrderProgressTracker = ({ steps, currentStep }: any) => {
  return (
    <View className="p-4 bg-white rounded-lg">
      <Text className="font-semibold mb-2">Order Progress</Text>
      <Text className="text-gray-600">Progress Tracker Placeholder</Text>
    </View>
  )
}

const StatusBadge = ({ status, variant }: any) => {
  return (
    <View className="px-3 py-1 bg-blue-100 rounded-full">
      <Text className="text-blue-800 text-sm font-medium">{status}</Text>
    </View>
  )
}

import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'OrderTracking'>

export function OrderTrackingScreen({ navigation, route }: Props) {
  const { orderId } = route.params
  const { colors, typography } = useTheme()
  const { getOrder, isLoading } = useOrdersStore()

  const [order, setOrder] = useState<any>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [providerLocation, setProviderLocation] = useState<any>(null)
  const [realtimeUpdates, setRealtimeUpdates] = useState<OrderUpdate[]>([])

  const orderSubscription = useRef<RealtimeSubscription | null>(null)
  const locationSubscription = useRef<RealtimeSubscription | null>(null)

  useEffect(() => {
    loadOrder()
    setupRealtimeSubscriptions()

    return () => {
      // Cleanup subscriptions on unmount
      orderSubscription.current?.unsubscribe()
      locationSubscription.current?.unsubscribe()
    }
  }, [orderId])

  const setupRealtimeSubscriptions = () => {
    // Subscribe to order updates
    orderSubscription.current = realtimeService.subscribeToOrderUpdates(
      orderId,
      (update: OrderUpdate) => {
        console.log('Received order update:', update)
        setRealtimeUpdates(prev => [...prev, update])

        // Update order status if it changed
        if (order && update.status !== order.status) {
          setOrder(prev => ({ ...prev, status: update.status }))
        }
      }
    )

    // Subscribe to provider location updates
    locationSubscription.current = realtimeService.subscribeToProviderLocation(
      'provider_123', // In real app, get from order
      (location) => {
        setProviderLocation(location)
      }
    )
  }

  const loadOrder = async () => {
    try {
      const orderData = await getOrder(orderId)
      setOrder(orderData)
    } catch (error) {
      Alert.alert('Error', 'Failed to load order details')
      navigation.goBack()
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadOrder()
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return colors.warning[500]
      case 'confirmed':
        return colors.info[500]
      case 'in_progress':
        return colors.primary[500]
      case 'completed':
        return colors.success[500]
      case 'cancelled':
        return colors.error[500]
      default:
        return colors.gray[500]
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return AlertCircleIcon
      case 'confirmed':
      case 'in_progress':
        return ClockIcon
      case 'completed':
        return CheckCircleIcon
      case 'cancelled':
        return XCircleIcon
      default:
        return AlertCircleIcon
    }
  }

  const handleContactProvider = () => {
    navigation.navigate('Chat', {
      orderId: order.id,
      providerId: order.providerId
    })
  }

  const handleCancelOrder = () => {
    Alert.alert(
      'Cancel Order',
      'Are you sure you want to cancel this order?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => {
            // Handle order cancellation
            Alert.alert('Order Cancelled', 'Your order has been cancelled successfully.')
          }
        }
      ]
    )
  }

  if (isLoading || !order) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <LoadingSpinner message="Loading order details..." />
      </SafeAreaView>
    )
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeftIcon size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginLeft: 16 }
        ]}>
          Order #{order.id.slice(-6).toUpperCase()}
        </Text>
      </View>

      {/* Status Badge */}
      <View style={{
        backgroundColor: getStatusColor(order.status),
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        alignSelf: 'flex-start',
      }}>
        <Text style={[
          typography.label.medium,
          { color: colors.white, fontWeight: '600', textTransform: 'capitalize' }
        ]}>
          {order.status.replace('_', ' ')}
        </Text>
      </View>
    </View>
  )

  const renderOrderProgress = () => {
    const steps = [
      { key: 'pending', label: 'Order Placed', time: order.createdAt },
      { key: 'confirmed', label: 'Confirmed', time: order.confirmedAt },
      { key: 'in_progress', label: 'In Progress', time: order.startedAt },
      { key: 'completed', label: 'Completed', time: order.completedAt }
    ]

    const currentStepIndex = steps.findIndex(step => step.key === order.status)

    return (
      <View style={{ padding: 16 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 16 }
        ]}>
          Order Progress
        </Text>

        {steps.map((step, index) => {
          const isCompleted = index <= currentStepIndex
          const isActive = index === currentStepIndex
          const StatusIcon = getStatusIcon(step.key)

          return (
            <View key={step.key} style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 16 }}>
              <View style={{ alignItems: 'center', marginRight: 16 }}>
                <View style={{
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  backgroundColor: isCompleted ? getStatusColor(step.key) : colors.background.tertiary,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <StatusIcon
                    size={16}
                    color={isCompleted ? colors.white : colors.text.tertiary}
                  />
                </View>
                {index < steps.length - 1 && (
                  <View style={{
                    width: 2,
                    height: 24,
                    backgroundColor: isCompleted ? getStatusColor(step.key) : colors.background.tertiary,
                    marginTop: 4,
                  }} />
                )}
              </View>

              <View style={{ flex: 1 }}>
                <Text style={[
                  typography.body.medium,
                  {
                    color: isCompleted ? colors.text.primary : colors.text.tertiary,
                    fontWeight: isActive ? '600' : '400'
                  }
                ]}>
                  {step.label}
                </Text>
                {step.time && (
                  <Text style={[
                    typography.body.small,
                    { color: colors.text.secondary, marginTop: 2 }
                  ]}>
                    {new Date(step.time).toLocaleString()}
                  </Text>
                )}
              </View>
            </View>
          )
        })}
      </View>
    )
  }

  const renderServiceDetails = () => (
    <View style={{
      margin: 16,
      backgroundColor: colors.background.primary,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.border.primary,
    }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
      ]}>
        Service Details
      </Text>

      {order.items.map((item: any, index: number) => (
        <View key={index} style={{ marginBottom: index < order.items.length - 1 ? 12 : 0 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <View style={{ flex: 1 }}>
              <Text style={[
                typography.body.medium,
                { color: colors.text.primary, fontWeight: '600' }
              ]}>
                {item.serviceName}
              </Text>
              <Text style={[
                typography.body.small,
                { color: colors.text.secondary, marginTop: 2 }
              ]}>
                {item.description}
              </Text>
            </View>
            <Text style={[
              typography.body.medium,
              { color: colors.text.primary, fontWeight: '600' }
            ]}>
              R{item.totalPrice}
            </Text>
          </View>
        </View>
      ))}

      <View style={{
        borderTopWidth: 1,
        borderTopColor: colors.border.primary,
        paddingTop: 12,
        marginTop: 12,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '700' }
          ]}>
            Total
          </Text>
          <Text style={[
            typography.body.large,
            { color: colors.primary[600], fontWeight: '700' }
          ]}>
            R{order.totalAmount}
          </Text>
        </View>
      </View>
    </View>
  )

  const renderLocationInfo = () => (
    <View style={{
      margin: 16,
      backgroundColor: colors.background.primary,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.border.primary,
    }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
      ]}>
        Service Location
      </Text>

      <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
        <MapPinIcon size={20} color={colors.text.tertiary} style={{ marginTop: 2, marginRight: 12 }} />
        <View style={{ flex: 1 }}>
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, fontWeight: '600' }
          ]}>
            {order.deliveryAddress.label}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginTop: 2 }
          ]}>
            {order.deliveryAddress.street}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary }
          ]}>
            {order.deliveryAddress.city}, {order.deliveryAddress.postalCode}
          </Text>
        </View>
      </View>

      {order.scheduledDate && (
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginTop: 12,
          paddingTop: 12,
          borderTopWidth: 1,
          borderTopColor: colors.border.primary
        }}>
          <ClockIcon size={20} color={colors.text.tertiary} style={{ marginRight: 12 }} />
          <View>
            <Text style={[
              typography.body.medium,
              { color: colors.text.primary, fontWeight: '600' }
            ]}>
              Scheduled Time
            </Text>
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary, marginTop: 2 }
            ]}>
              {new Date(order.scheduledDate).toLocaleString()}
            </Text>
          </View>
        </View>
      )}
    </View>
  )

  const renderProviderInfo = () => (
    <View style={{
      margin: 16,
      backgroundColor: colors.background.primary,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.border.primary,
    }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
      ]}>
        Service Provider
      </Text>

      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
        <View style={{
          width: 50,
          height: 50,
          borderRadius: 25,
          backgroundColor: colors.background.tertiary,
          marginRight: 12,
        }} />

        <View style={{ flex: 1 }}>
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, fontWeight: '600' }
          ]}>
            {order.provider?.name || 'Provider Name'}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginTop: 2 }
          ]}>
            Professional Service Provider
          </Text>
        </View>
      </View>

      <View style={{ flexDirection: 'row', gap: 12 }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderWidth: 1,
            borderColor: colors.border.primary,
            borderRadius: 8,
            paddingVertical: 12,
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'center',
          }}
          onPress={() => {/* Handle call */}}
        >
          <PhoneIcon size={16} color={colors.text.primary} />
          <Text style={[
            typography.label.medium,
            { color: colors.text.primary, marginLeft: 8 }
          ]}>
            Call
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.primary[500],
            borderRadius: 8,
            paddingVertical: 12,
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'center',
          }}
          onPress={handleContactProvider}
        >
          <MessageCircleIcon size={16} color={colors.white} />
          <Text style={[
            typography.label.medium,
            { color: colors.white, marginLeft: 8 }
          ]}>
            Message
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  const renderActionButtons = () => {
    if (order.status === 'completed' || order.status === 'cancelled') {
      return null
    }

    return (
      <View style={{
        backgroundColor: colors.background.primary,
        borderTopWidth: 1,
        borderTopColor: colors.border.primary,
        padding: 16,
      }}>
        {order.status === 'pending' && (
          <TouchableOpacity
            style={{
              backgroundColor: colors.error[500],
              borderRadius: 12,
              paddingVertical: 16,
              alignItems: 'center',
            }}
            onPress={handleCancelOrder}
          >
            <Text style={[
              typography.label.medium,
              { color: colors.white, fontWeight: '600' }
            ]}>
              Cancel Order
            </Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }

  const renderLiveTracking = () => {
    if (!providerLocation || order.status === 'completed' || order.status === 'cancelled') {
      return null
    }

    return (
      <View style={{ margin: 16 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
        ]}>
          Live Tracking
        </Text>

        <View style={{ height: 200, borderRadius: 12, overflow: 'hidden' }}>
          <LiveTrackingMap
            providerLocation={providerLocation}
            customerLocation={{
              latitude: order.deliveryAddress.coordinates?.latitude || -26.2678,
              longitude: order.deliveryAddress.coordinates?.longitude || 27.8546,
            }}
            onLocationUpdate={(location) => setProviderLocation(location)}
            showRoute={true}
            showETA={true}
          />
        </View>
      </View>
    )
  }

  const renderRealtimeUpdates = () => {
    if (realtimeUpdates.length === 0) return null

    return (
      <View style={{ margin: 16 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
        ]}>
          Live Updates
        </Text>

        {realtimeUpdates.slice(-3).map((update, index) => (
          <View
            key={index}
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 8,
              padding: 12,
              marginBottom: 8,
              borderLeftWidth: 3,
              borderLeftColor: colors.primary[500],
            }}
          >
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary, marginBottom: 4 }
            ]}>
              {new Date(update.timestamp).toLocaleTimeString()}
            </Text>
            <Text style={[
              typography.body.medium,
              { color: colors.text.primary }
            ]}>
              {update.message || `Order status updated to ${update.status}`}
            </Text>
          </View>
        ))}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderHeader()}
        {renderLiveTracking()}
        {renderOrderProgress()}
        {renderRealtimeUpdates()}
        {renderServiceDetails()}
        {renderLocationInfo()}
        {renderProviderInfo()}
      </ScrollView>
      {renderActionButtons()}
    </SafeAreaView>
  )
}
