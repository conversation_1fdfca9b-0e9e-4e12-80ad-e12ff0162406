import { Client, Account, Databases, Storage, Functions } from 'appwrite';

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || 'hvppyplug');

// Initialize Appwrite services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const functions = new Functions(client);

// Export client for custom usage
export { client };

// Database and collection IDs
export const DATABASE_ID = 'hvppyplug-main';
export const COLLECTIONS = {
  USERS: 'users',
  VENDORS: 'vendors',
  MENU_ITEMS: 'menu-items',
  ORDERS: 'orders',
  OTP_CODES: 'otp-codes',
} as const;

// Storage bucket IDs
export const BUCKETS = {
  IMAGES: 'images',
} as const;

// Function IDs
export const FUNCTIONS_ID = {
  HVPPY_API: 'hvppy-api',
} as const;
