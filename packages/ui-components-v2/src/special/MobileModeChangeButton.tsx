import React from "react";
import { Fab, FabIcon } from "@/ui";
import { Moon, Sun } from "lucide-react-native";
// import { ThemeContext } from "../App";

const MobileModeChangeButton = () => {
  // Theme support can be added later
  const colorMode = 'light';
  const toggleColorMode = () => {};
  return (
    <Fab onPress={toggleColorMode} className="md:hidden bottom-4 right-4">
      <FabIcon
        as={colorMode === "light" ? Moon : Sun}
        className="fill-typography-50"
      />
    </Fab>
  );
};

export default MobileModeChangeButton;
