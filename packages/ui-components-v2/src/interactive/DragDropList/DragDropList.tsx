"use client"

import React, { useState, useCallback } from 'react'
import { View, Pressable, PanGestureHandler, State } from 'react-native'
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  runOnJS,
  withSpring,
} from 'react-native-reanimated'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { GripVerticalIcon, TrashIcon, EditIcon } from 'lucide-react-native'

// Types
export interface DragDropItem {
  id: string | number
  [key: string]: any
}

export interface DragDropListProps<T extends DragDropItem> {
  data: T[]
  renderItem: (item: T, index: number, isDragging: boolean) => React.ReactNode
  onReorder?: (newData: T[]) => void
  onDelete?: (item: T, index: number) => void
  onEdit?: (item: T, index: number) => void
  keyExtractor?: (item: T, index: number) => string
  disabled?: boolean
  showActions?: boolean
  className?: string
  itemClassName?: string
  dragHandleClassName?: string
}

// Styles
const containerStyles = tva({
  base: "flex-1",
})

const itemContainerStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg mb-2 overflow-hidden",
  variants: {
    dragging: {
      true: "shadow-lg scale-105 z-10",
      false: "shadow-sm",
    },
    disabled: {
      true: "opacity-50",
    },
  },
})

const itemContentStyles = tva({
  base: "flex-row items-center p-3",
})

const dragHandleStyles = tva({
  base: "p-2 mr-2 rounded",
  variants: {
    active: {
      true: "bg-background-100",
      false: "bg-transparent",
    },
  },
})

const actionsStyles = tva({
  base: "flex-row items-center ml-auto",
})

const actionButtonStyles = tva({
  base: "p-2 rounded ml-1",
  variants: {
    variant: {
      edit: "hover:bg-info-100",
      delete: "hover:bg-error-100",
    },
  },
})

const AnimatedPressable = Animated.createAnimatedComponent(Pressable)

export function DragDropList<T extends DragDropItem>({
  data,
  renderItem,
  onReorder,
  onDelete,
  onEdit,
  keyExtractor,
  disabled = false,
  showActions = true,
  className,
  itemClassName,
  dragHandleClassName,
}: DragDropListProps<T>) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [items, setItems] = useState(data)

  // Update items when data changes
  React.useEffect(() => {
    setItems(data)
  }, [data])

  // Default key extractor
  const getItemKey = useCallback((item: T, index: number) => 
    keyExtractor ? keyExtractor(item, index) : item.id.toString()
  , [keyExtractor])

  // Handle reorder
  const handleReorder = useCallback((fromIndex: number, toIndex: number) => {
    const newItems = [...items]
    const [movedItem] = newItems.splice(fromIndex, 1)
    newItems.splice(toIndex, 0, movedItem)
    
    setItems(newItems)
    onReorder?.(newItems)
  }, [items, onReorder])

  // Render drag handle
  const renderDragHandle = (index: number) => {
    if (disabled) return null

    const translateY = useSharedValue(0)
    const isDragging = useSharedValue(false)

    const gestureHandler = useAnimatedGestureHandler({
      onStart: () => {
        isDragging.value = true
        runOnJS(setDraggedIndex)(index)
      },
      onActive: (event) => {
        translateY.value = event.translationY
      },
      onEnd: () => {
        isDragging.value = false
        translateY.value = withSpring(0)
        runOnJS(setDraggedIndex)(null)
        
        // Calculate new position based on translation
        const itemHeight = 60 // Approximate item height
        const newIndex = Math.round(translateY.value / itemHeight) + index
        const clampedIndex = Math.max(0, Math.min(items.length - 1, newIndex))
        
        if (clampedIndex !== index) {
          runOnJS(handleReorder)(index, clampedIndex)
        }
      },
    })

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ translateY: translateY.value }],
      zIndex: isDragging.value ? 1000 : 1,
    }))

    return (
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={animatedStyle}>
          <View className={dragHandleStyles({ 
            active: draggedIndex === index,
            class: dragHandleClassName 
          })}>
            <GripVerticalIcon 
              size={20} 
              className="text-typography-400" 
            />
          </View>
        </Animated.View>
      </PanGestureHandler>
    )
  }

  // Render action buttons
  const renderActions = (item: T, index: number) => {
    if (!showActions) return null

    return (
      <View className={actionsStyles()}>
        {onEdit && (
          <Pressable
            className={actionButtonStyles({ variant: 'edit' })}
            onPress={() => onEdit(item, index)}
          >
            <EditIcon size={16} className="text-info-600" />
          </Pressable>
        )}
        
        {onDelete && (
          <Pressable
            className={actionButtonStyles({ variant: 'delete' })}
            onPress={() => onDelete(item, index)}
          >
            <TrashIcon size={16} className="text-error-600" />
          </Pressable>
        )}
      </View>
    )
  }

  // Render list item
  const renderListItem = (item: T, index: number) => {
    const isDragging = draggedIndex === index

    return (
      <View
        key={getItemKey(item, index)}
        className={itemContainerStyles({ 
          dragging: isDragging,
          disabled,
          class: itemClassName 
        })}
      >
        <View className={itemContentStyles()}>
          {renderDragHandle(index)}
          
          <View className="flex-1">
            {renderItem(item, index, isDragging)}
          </View>
          
          {renderActions(item, index)}
        </View>
      </View>
    )
  }

  return (
    <View className={containerStyles({ class: className })}>
      {items.map((item, index) => renderListItem(item, index))}
    </View>
  )
}

// Simple list item component for common use cases
export interface SimpleListItemProps {
  title: string
  subtitle?: string
  badge?: string
  badgeColor?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
}

export const SimpleListItem: React.FC<SimpleListItemProps> = ({
  title,
  subtitle,
  badge,
  badgeColor = 'primary',
}) => (
  <View className="flex-1">
    <View className="flex-row items-center justify-between">
      <Text className="font-medium text-typography-900 flex-1" numberOfLines={1}>
        {title}
      </Text>
      {badge && (
        <View className={`px-2 py-1 rounded-full ml-2 bg-${badgeColor}-100`}>
          <Text className={`text-xs font-medium text-${badgeColor}-700`}>
            {badge}
          </Text>
        </View>
      )}
    </View>
    {subtitle && (
      <Text className="text-sm text-typography-600 mt-1" numberOfLines={2}>
        {subtitle}
      </Text>
    )}
  </View>
)

// Predefined list variants
export const TaskList = <T extends DragDropItem>(props: DragDropListProps<T>) => (
  <DragDropList {...props} showActions={true} />
)

export const SimpleReorderList = <T extends DragDropItem>(
  props: Omit<DragDropListProps<T>, 'showActions'>
) => (
  <DragDropList {...props} showActions={false} />
)
