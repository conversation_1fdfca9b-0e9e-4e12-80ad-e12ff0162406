import type { NavigatorScreenParams } from '@react-navigation/native'

// Main Tab Navigator Types
export type MainTabParamList = {
  Dashboard: undefined
  Orders: undefined
  Earnings: undefined
  Profile: undefined
}

// Root Stack Navigator Types
export type RootStackParamList = {
  // Auth Flow
  Onboarding: undefined
  Login: undefined
  Register: undefined
  ForgotPassword: undefined
  OTPVerification: { email: string; type: 'register' | 'forgot-password' }
  
  // Main App
  MainTabs: NavigatorScreenParams<MainTabParamList>
  
  // Modal Screens
  OrderDetail: { orderId: string }
  OrderTracking: { orderId: string }
  DeliveryProof: { orderId: string }
  Chat: { orderId: string; customerId: string }
  LocationPicker: { currentLocation?: { latitude: number; longitude: number; address: string } }
  NotificationDetail: { notificationId: string }
  EarningsDetail: { period: 'daily' | 'weekly' | 'monthly' }
  Help: undefined
  Support: undefined
  Settings: undefined
}

// Dashboard Stack Navigator Types
export type DashboardStackParamList = {
  DashboardHome: undefined
  AvailableOrders: undefined
  ActiveDeliveries: undefined
}

// Orders Stack Navigator Types
export type OrdersStackParamList = {
  OrdersList: undefined
  OrderDetails: { orderId: string }
  OrderHistory: undefined
}

// Earnings Stack Navigator Types
export type EarningsStackParamList = {
  EarningsOverview: undefined
  EarningsHistory: undefined
  PayoutSettings: undefined
}

// Profile Stack Navigator Types
export type ProfileStackParamList = {
  ProfileHome: undefined
  EditProfile: undefined
  Documents: undefined
  VehicleInfo: undefined
  BankDetails: undefined
  Preferences: undefined
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
