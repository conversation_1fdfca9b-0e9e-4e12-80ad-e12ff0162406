// Core Payment Types and Interfaces

export type Currency = 'ZAR' | 'USD' | 'EUR' | 'GBP' | 'CAD' | 'AUD';

export type Amount = {
  value: number;
  currency: Currency;
  formatted: string;
};

export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'succeeded'
  | 'failed'
  | 'cancelled'
  | 'refunded'
  | 'partially_refunded'
  | 'expired'
  | 'requires_action'
  | 'requires_confirmation';

export type PaymentMethod =
  | 'polar_card'
  | 'polar_checkout'
  | 'payfast_eft'
  | 'payfast_card'
  | 'mpesa'
  | 'voucher_1voucher'
  | 'voucher_ott'
  | 'cash_on_delivery';

export type TransactionId = string;
export type UserId = string;
export type OrderId = string;
export type PaymentIntentId = string;

export interface PaymentIntent {
  id: PaymentIntentId;
  amount: Amount;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  customerId: UserId;
  orderId: OrderId;
  description?: string | undefined;
  metadata?: Record<string, string> | undefined;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
}

export interface PaymentResult {
  success: boolean;
  paymentIntent: PaymentIntent;
  transactionId?: TransactionId;
  providerResponse?: any;
  error?: string;
  requiresAction?: boolean;
  actionUrl?: string;
}

export interface PaymentRequest {
  amount: Amount;
  paymentMethod: PaymentMethod;
  customerId: UserId;
  orderId: OrderId;
  description?: string;
  metadata?: Record<string, string>;
  returnUrl?: string;
  cancelUrl?: string;
}

export interface RefundRequest {
  paymentIntentId: PaymentIntentId;
  amount?: Amount; // Partial refund if specified
  reason?: string;
  metadata?: Record<string, string>;
}

export interface RefundResult {
  success: boolean;
  refundId: string;
  amount: Amount;
  status: 'pending' | 'succeeded' | 'failed';
  error?: string;
}

// Customer Payment Information
export interface PaymentCustomer {
  id: UserId;
  email?: string;
  phone?: string;
  name?: string;
  address?: {
    line1: string;
    line2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
  };
}

// Payment Method Details
export interface CardDetails {
  last4: string;
  brand: string;
  expiryMonth: number;
  expiryYear: number;
  country?: string;
}

export interface VoucherDetails {
  code: string;
  type: 'ott' | '1voucher';
  balance?: Amount;
  expiresAt?: Date;
}

export interface MPesaDetails {
  phoneNumber: string;
  accountReference?: string;
  transactionDesc?: string;
}

export interface PaymentMethodDetails {
  type: PaymentMethod;
  card?: CardDetails;
  voucher?: VoucherDetails;
  mpesa?: MPesaDetails;
}

// Transaction History
export interface Transaction {
  id: TransactionId;
  paymentIntentId: PaymentIntentId;
  amount: Amount;
  status: PaymentStatus;
  paymentMethod: PaymentMethodDetails;
  customerId: UserId;
  orderId: OrderId;
  description?: string;
  metadata?: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
  providerTransactionId?: string;
  providerResponse?: any;
}

// Payment Configuration
export interface PaymentConfig {
  defaultCurrency: Currency;
  supportedCurrencies: Currency[];
  supportedPaymentMethods: PaymentMethod[];
  minimumAmount: Record<Currency, number>;
  maximumAmount: Record<Currency, number>;
  testMode: boolean;
  webhookUrl?: string;
  returnUrl?: string;
  cancelUrl?: string;
}

// Payment Analytics
export interface PaymentAnalytics {
  totalTransactions: number;
  totalAmount: Amount;
  successRate: number;
  averageTransactionAmount: Amount;
  topPaymentMethods: Array<{
    method: PaymentMethod;
    count: number;
    percentage: number;
  }>;
  failureReasons: Array<{
    reason: string;
    count: number;
    percentage: number;
  }>;
}
