import type { Meta, StoryObj } from '@storybook/react-native'
import { DataVisualization } from './DataVisualization'

const meta: Meta<typeof DataVisualization> = {
  title: 'data/DataVisualization',
  component: DataVisualization,
  parameters: {
    docs: {
      description: {
        component: 'DataVisualization component description.',
      },
    },
  },
  argTypes: {
    disabled: {
      control: 'boolean',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
}

export const WithChildren: Story = {
  args: {
    children: 'Custom content',
  },
}
