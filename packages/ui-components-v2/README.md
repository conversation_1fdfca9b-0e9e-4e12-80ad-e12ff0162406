# @hvppyplug/compound-components

Business-focused compound components for HVPPYPlug+ built on Gluestack UI v2 primitives.

## Overview

This library provides high-level, compound components that combine Gluestack UI v2 primitives into business-specific components optimized for the HVPPYPlug+ platform. Instead of recreating basic UI primitives, we focus on complex, feature-rich components that accelerate development.

## Features

- 🚀 **Production Ready**: Built on stable Gluestack UI v2 foundation
- 📱 **Cross-Platform**: Works seamlessly on React Native (iOS/Android) and Web
- 🎨 **NativeWind Styling**: Utility-first CSS with Tailwind classes
- 📝 **TypeScript First**: Comprehensive type safety and IntelliSense
- ♿ **Accessible**: WCAG compliant with screen reader support
- 🎯 **Business Focused**: Components designed for real-world applications
- 🔧 **Customizable**: Extensive theming and styling options

## Installation

```bash
pnpm add @hvppyplug/compound-components
```

### Peer Dependencies

```bash
pnpm add @gluestack-ui/themed react-native-svg react-native-reanimated
```

## Quick Start

```tsx
import { GluestackUIProvider, DataTable, DashboardWidget } from '@hvppyplug/compound-components'

function App() {
  return (
    <GluestackUIProvider mode="light">
      {/* Your app content */}
    </GluestackUIProvider>
  )
}
```

## Components

### Data Management

#### DataTable
Advanced table component with sorting, filtering, search, and pagination.

```tsx
import { DataTable } from '@hvppyplug/compound-components'

const columns = [
  { key: 'name', title: 'Name', sortable: true },
  { key: 'email', title: 'Email', sortable: true },
  { key: 'status', title: 'Status', render: (value) => <StatusBadge status={value} /> },
]

<DataTable
  data={users}
  columns={columns}
  searchable
  pagination
  onRowPress={(user) => navigate(`/users/${user.id}`)}
/>
```

#### SearchableList
Infinite scrolling list with search and filtering capabilities.

```tsx
import { SearchableList } from '@hvppyplug/compound-components'

<SearchableList
  data={items}
  searchKeys={['title', 'description']}
  filters={[
    { key: 'category', label: 'Category', type: 'select', options: categories },
    { key: 'active', label: 'Active', type: 'boolean' },
  ]}
  renderItem={(item) => <ItemCard item={item} />}
  onLoadMore={loadMoreItems}
  hasMore={hasMoreData}
/>
```

### Forms

#### MultiStepForm
Complex form wizard with validation and progress tracking.

```tsx
import { MultiStepForm } from '@hvppyplug/compound-components'

const steps = [
  {
    id: 'personal',
    title: 'Personal Information',
    component: PersonalInfoStep,
    validation: (data) => validatePersonalInfo(data),
  },
  {
    id: 'business',
    title: 'Business Details',
    component: BusinessStep,
    validation: (data) => validateBusiness(data),
  },
]

<MultiStepForm
  steps={steps}
  onComplete={(data) => submitForm(data)}
  showProgress
  allowSkip
/>
```

### Business Logic

#### DashboardWidget
Configurable widget for displaying KPIs and metrics.

```tsx
import { DashboardWidget, MetricWidget, KPIWidget } from '@hvppyplug/compound-components'

<DashboardWidget
  data={{
    title: 'Total Revenue',
    value: 125000,
    trend: { value: 12.5, direction: 'up', label: 'vs last month' },
    status: 'success',
  }}
  onRefresh={refreshData}
  actions={[
    { label: 'View Details', onPress: () => navigate('/revenue') },
    { label: 'Export', onPress: exportData },
  ]}
/>

{/* Predefined variants */}
<MetricWidget data={metricData} />
<KPIWidget data={kpiData} />
```

### Interactive

#### DragDropList
Reorderable list with drag-and-drop functionality.

```tsx
import { DragDropList, SimpleListItem } from '@hvppyplug/compound-components'

<DragDropList
  data={tasks}
  renderItem={(task) => (
    <SimpleListItem
      title={task.title}
      subtitle={task.description}
      badge={task.priority}
      badgeColor={task.priority === 'high' ? 'error' : 'primary'}
    />
  )}
  onReorder={(newOrder) => updateTaskOrder(newOrder)}
  onDelete={(task) => deleteTask(task.id)}
  onEdit={(task) => editTask(task)}
/>
```

## Hooks

The library includes powerful hooks for common patterns:

```tsx
import { 
  useDataManagement, 
  useSearchAndFilter, 
  useSort, 
  usePagination,
  useComponentState,
  usePlatform 
} from '@hvppyplug/compound-components'

// Combined data management
const {
  processedData,
  searchState,
  sortState,
  paginationState,
  setSearch,
  sort,
  nextPage,
} = useDataManagement(data, {
  searchKeys: ['name', 'email'],
  pageSize: 20,
})

// Platform detection
const { isWeb, isMobile, isIOS } = usePlatform()

// Component state management
const { isLoading, setLoading, setSuccess, setError } = useComponentState()
```

## Utilities

Comprehensive utility functions for common operations:

```tsx
import { format, validation, array, color } from '@hvppyplug/compound-components'

// Format utilities
format.formatNumber(1250000) // "1.3M"
format.formatCurrency(99.99) // "$99.99"
format.formatRelativeTime(new Date()) // "2h ago"

// Validation utilities
validation.isValidEmail('<EMAIL>') // true
validation.isRequired(value) // boolean

// Array utilities
array.sortBy(users, 'name', 'asc')
array.groupBy(items, 'category')
array.moveItem(list, 0, 3)

// Color utilities
color.getStatusColor('success') // "success-500"
color.getColorClass('primary', 600) // "primary-600"
```

## Theming

Components use Gluestack UI v2's theming system with NativeWind classes:

```tsx
// Custom theme colors are automatically available
<DataTable className="bg-background-50 border-primary-200" />

// Dark mode support
<GluestackUIProvider mode="dark">
  <App />
</GluestackUIProvider>
```

## TypeScript Support

Full TypeScript support with comprehensive type definitions:

```tsx
import type { 
  DataTableColumn, 
  DashboardWidgetData, 
  SearchFilter,
  FormStep 
} from '@hvppyplug/compound-components'

const columns: DataTableColumn<User>[] = [
  {
    key: 'name',
    title: 'Name',
    render: (value, user) => <UserName user={user} />,
  },
]
```

## Performance

- **Optimized Rendering**: Uses React.memo and useMemo for expensive operations
- **Lazy Loading**: Components support infinite scrolling and pagination
- **Minimal Bundle**: Tree-shakeable exports, only import what you use
- **Native Performance**: Built on React Native's optimized components

## Contributing

1. Follow the existing component patterns
2. Include comprehensive TypeScript types
3. Add Storybook stories for new components
4. Write unit tests with React Native Testing Library
5. Update documentation

## License

MIT License - see LICENSE file for details.
