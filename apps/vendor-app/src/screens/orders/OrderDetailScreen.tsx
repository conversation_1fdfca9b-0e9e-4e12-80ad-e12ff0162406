import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeftIcon,
  PhoneIcon,
  MessageSquareIcon,
  MapPinIcon,
  ClockIcon,
  UserIcon,
  DollarSignIcon,
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useOrdersStore } from '../../stores/ordersStore'
import type { OrdersStackScreenProps } from '../../types/navigation'

type Props = OrdersStackScreenProps<'OrderDetail'>

export function OrderDetailScreen({ navigation, route }: Props) {
  const { orderId } = route.params
  const { colors, typography } = useTheme()
  const {
    getOrderById,
    acceptOrder,
    declineOrder,
    startOrder,
    completeOrder,
    updateProgress
  } = useOrdersStore()

  const [order, setOrder] = useState(getOrderById(orderId))

  useEffect(() => {
    const foundOrder = getOrderById(orderId)
    setOrder(foundOrder)
  }, [orderId])

  if (!order) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text style={[typography.body.large, { color: colors.text.secondary }]}>
            Order not found
          </Text>
        </View>
      </SafeAreaView>
    )
  }

  const handleAcceptOrder = async () => {
    try {
      await acceptOrder(order.id)
      setOrder(getOrderById(orderId))
      Alert.alert('Success', 'Order accepted successfully!')
    } catch (error) {
      Alert.alert('Error', 'Failed to accept order')
    }
  }

  const handleDeclineOrder = async () => {
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              await declineOrder(order.id, 'Unable to fulfill this order')
              navigation.goBack()
            } catch (error) {
              Alert.alert('Error', 'Failed to decline order')
            }
          }
        }
      ]
    )
  }

  const handleStartOrder = async () => {
    try {
      await startOrder(order.id)
      setOrder(getOrderById(orderId))
      Alert.alert('Success', 'Order started!')
    } catch (error) {
      Alert.alert('Error', 'Failed to start order')
    }
  }

  const handleCompleteOrder = async () => {
    Alert.alert(
      'Complete Order',
      'Mark this order as completed?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          onPress: async () => {
            try {
              await completeOrder(order.id, 'Order completed successfully')
              setOrder(getOrderById(orderId))
              Alert.alert('Success', 'Order completed!')
            } catch (error) {
              Alert.alert('Error', 'Failed to complete order')
            }
          }
        }
      ]
    )
  }

  const handleCallCustomer = () => {
    Linking.openURL(`tel:${order.customerPhone}`)
  }

  const handleMessageCustomer = () => {
    // Navigate to chat screen
    navigation.navigate('Chat', {
      orderId: order.id,
      customerId: order.customerId
    })
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeftIcon size={24} color={colors.text.primary} />
        </TouchableOpacity>

        <Text style={[typography.headline.medium, { color: colors.text.primary }]}>
          Order Details
        </Text>

        <View style={{ width: 24 }} />
      </View>
    </View>
  )

  const renderOrderStatus = () => (
    <View style={{ padding: 16 }}>
      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
        alignItems: 'center',
      }}>
        <View style={{
          backgroundColor: order.status === 'pending' ? colors.warning[100] :
                         order.status === 'accepted' ? colors.info[100] :
                         order.status === 'in_progress' ? colors.primary[100] :
                         order.status === 'completed' ? colors.success[100] : colors.error[100],
          borderRadius: 20,
          paddingHorizontal: 16,
          paddingVertical: 8,
          marginBottom: 8,
        }}>
          <Text style={[
            typography.body.medium,
            {
              color: order.status === 'pending' ? colors.warning[700] :
                     order.status === 'accepted' ? colors.info[700] :
                     order.status === 'in_progress' ? colors.primary[700] :
                     order.status === 'completed' ? colors.success[700] : colors.error[700],
              fontWeight: '600'
            }
          ]}>
            {order.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>

        <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '700' }]}>
          Order #{order.id.slice(-6)}
        </Text>

        <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 4 }]}>
          Created {new Date(order.createdAt).toLocaleDateString()} at {new Date(order.createdAt).toLocaleTimeString()}
        </Text>
      </View>
    </View>
  )

  const renderCustomerInfo = () => (
    <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
      <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 12 }]}>
        Customer Information
      </Text>

      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
          <View style={{
            backgroundColor: colors.primary[100],
            borderRadius: 20,
            padding: 8,
            marginRight: 12,
          }}>
            <UserIcon size={20} color={colors.primary[600]} />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
              {order.customerName}
            </Text>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              {order.customerPhone}
            </Text>
          </View>
        </View>

        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: colors.success[500],
              borderRadius: 8,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={handleCallCustomer}
          >
            <PhoneIcon size={16} color={colors.white} />
            <Text style={[typography.label.small, { color: colors.white, marginLeft: 6 }]}>
              Call
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: colors.primary[500],
              borderRadius: 8,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={handleMessageCustomer}
          >
            <MessageSquareIcon size={16} color={colors.white} />
            <Text style={[typography.label.small, { color: colors.white, marginLeft: 6 }]}>
              Message
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )

  const renderServiceDetails = () => (
    <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
      <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 12 }]}>
        Service Details
      </Text>

      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }]}>
          {order.serviceName}
        </Text>

        <Text style={[typography.body.small, { color: colors.text.secondary, marginBottom: 12 }]}>
          {order.description}
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <ClockIcon size={16} color={colors.text.secondary} />
          <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 8 }]}>
            Scheduled: {new Date(order.scheduledDate).toLocaleDateString()} at {order.scheduledTime}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <MapPinIcon size={16} color={colors.text.secondary} />
          <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 8, flex: 1 }]}>
            {order.serviceAddress.street}, {order.serviceAddress.city}
          </Text>
        </View>

        {order.serviceAddress.instructions && (
          <View style={{
            backgroundColor: colors.background.primary,
            borderRadius: 8,
            padding: 12,
            marginTop: 8
          }}>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              📝 Special Instructions:
            </Text>
            <Text style={[typography.body.small, { color: colors.text.primary, marginTop: 4 }]}>
              {order.serviceAddress.instructions}
            </Text>
          </View>
        )}
      </View>
    </View>
  )

  const renderPricingDetails = () => (
    <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
      <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 12 }]}>
        Pricing Details
      </Text>

      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
          <Text style={[typography.body.medium, { color: colors.text.primary }]}>
            Base Price
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.primary }]}>
            R{order.basePrice}
          </Text>
        </View>

        {order.additionalCharges.map((charge, index) => (
          <View key={index} style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              {charge.name}
            </Text>
            <Text style={[typography.body.small, { color: colors.text.secondary }]}>
              R{charge.amount}
            </Text>
          </View>
        ))}

        <View style={{
          borderTopWidth: 1,
          borderTopColor: colors.border.primary,
          paddingTop: 8,
          marginTop: 8
        }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '700' }]}>
              Total Amount
            </Text>
            <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '700' }]}>
              R{order.totalAmount}
            </Text>
          </View>
        </View>

        <View style={{
          backgroundColor: order.paymentStatus === 'paid' ? colors.success[100] : colors.warning[100],
          borderRadius: 8,
          padding: 8,
          marginTop: 12,
          alignItems: 'center',
        }}>
          <Text style={[
            typography.body.small,
            {
              color: order.paymentStatus === 'paid' ? colors.success[700] : colors.warning[700],
              fontWeight: '600'
            }
          ]}>
            Payment: {order.paymentStatus.toUpperCase()} via {order.paymentMethod.replace('_', ' ')}
          </Text>
        </View>
      </View>
    </View>
  )

  const renderActionButtons = () => {
    if (order.status === 'completed' || order.status === 'cancelled') {
      return null
    }

    return (
      <View style={{ padding: 16 }}>
        {order.status === 'pending' && (
          <View style={{ flexDirection: 'row', gap: 12 }}>
            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: colors.success[500],
                borderRadius: 12,
                padding: 16,
                alignItems: 'center',
              }}
              onPress={handleAcceptOrder}
            >
              <Text style={[typography.label.large, { color: colors.white, fontWeight: '600' }]}>
                Accept Order
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: colors.background.secondary,
                borderRadius: 12,
                padding: 16,
                alignItems: 'center',
                borderWidth: 1,
                borderColor: colors.border.primary,
              }}
              onPress={handleDeclineOrder}
            >
              <Text style={[typography.label.large, { color: colors.text.primary, fontWeight: '600' }]}>
                Decline
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {order.status === 'accepted' && (
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary[500],
              borderRadius: 12,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={handleStartOrder}
          >
            <PlayIcon size={20} color={colors.white} />
            <Text style={[typography.label.large, { color: colors.white, marginLeft: 8, fontWeight: '600' }]}>
              Start Work
            </Text>
          </TouchableOpacity>
        )}

        {order.status === 'in_progress' && (
          <TouchableOpacity
            style={{
              backgroundColor: colors.success[500],
              borderRadius: 12,
              padding: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={handleCompleteOrder}
          >
            <CheckCircleIcon size={20} color={colors.white} />
            <Text style={[typography.label.large, { color: colors.white, marginLeft: 8, fontWeight: '600' }]}>
              Mark as Complete
            </Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      {renderHeader()}
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderOrderStatus()}
        {renderCustomerInfo()}
        {renderServiceDetails()}
        {renderPricingDetails()}
      </ScrollView>
      {renderActionButtons()}
    </SafeAreaView>
  )
}
