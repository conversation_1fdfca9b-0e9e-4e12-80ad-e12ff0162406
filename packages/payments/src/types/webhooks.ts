// Webhook Types and Event Handling

export type WebhookEventType =
  | 'payment.succeeded'
  | 'payment.failed'
  | 'payment.cancelled'
  | 'payment.refunded'
  | 'payment.requires_action'
  | 'payment.processing'
  | 'payment.expired'
  | 'voucher.validated'
  | 'voucher.redeemed'
  | 'voucher.expired'
  | 'mpesa.callback'
  | 'payfast.notification'
  | 'order.created'
  | 'order.updated'
  | 'checkout.created'
  | 'checkout.updated'
  | 'customer.created'
  | 'customer.updated'
  | 'subscription.created'
  | 'subscription.updated';

export interface WebhookEvent {
  id: string;
  type: WebhookEventType;
  provider: string;
  data: any;
  timestamp: Date;
  livemode: boolean;
  apiVersion?: string;
}

export interface WebhookPayload {
  event: WebhookEvent;
  signature: string;
  rawBody: string;
  headers: Record<string, string>;
}

export interface WebhookSignature {
  timestamp: number;
  signature: string;
  algorithm: 'sha256' | 'sha1' | 'md5';
}

export interface WebhookHandler {
  eventType: WebhookEventType;
  handler: (event: WebhookEvent) => Promise<void>;
  retryOnFailure?: boolean;
  maxRetries?: number;
}

export interface WebhookConfig {
  url: string;
  secret: string;
  enabledEvents: WebhookEventType[];
  retryPolicy?: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
}

// Provider-specific webhook types

export interface StripeWebhookEvent {
  id: string;
  object: 'event';
  api_version: string;
  created: number;
  data: {
    object: any;
    previous_attributes?: any;
  };
  livemode: boolean;
  pending_webhooks: number;
  request: {
    id: string;
    idempotency_key?: string;
  };
  type: string;
}

export interface PayFastWebhookData {
  m_payment_id: string;
  pf_payment_id: string;
  payment_status: 'COMPLETE' | 'FAILED' | 'CANCELLED';
  item_name: string;
  item_description?: string;
  amount_gross: string;
  amount_fee: string;
  amount_net: string;
  custom_str1?: string;
  custom_int1?: string;
  name_first?: string;
  name_last?: string;
  email_address?: string;
  merchant_id: string;
  signature: string;
}

export interface MPesaWebhookData {
  Body: {
    stkCallback: {
      MerchantRequestID: string;
      CheckoutRequestID: string;
      ResultCode: number;
      ResultDesc: string;
      CallbackMetadata?: {
        Item: Array<{
          Name: string;
          Value: string | number;
        }>;
      };
    };
  };
}

export interface VoucherWebhookData {
  eventType: 'validation' | 'redemption' | 'expiry';
  voucherCode: string;
  voucherType: 'ott' | '1voucher';
  transactionId: string;
  amount?: {
    value: number;
    currency: string;
  };
  timestamp: string;
  status: 'success' | 'failed' | 'expired';
  error?: string;
}

// Webhook processing result
export interface WebhookProcessingResult {
  success: boolean;
  eventId: string;
  eventType: WebhookEventType;
  processedAt: Date;
  error?: string;
  retryCount?: number;
  nextRetryAt?: Date;
}

// Webhook verification
export interface WebhookVerification {
  isValid: boolean;
  provider: string;
  eventId?: string;
  error?: string;
  timestamp?: Date;
}

// Webhook retry configuration
export interface WebhookRetryConfig {
  maxRetries: number;
  backoffMultiplier: number;
  initialDelayMs: number;
  maxDelayMs: number;
  retryableStatusCodes: number[];
}

// Webhook security
export interface WebhookSecurity {
  verifySignature(payload: string, signature: string, secret: string): boolean;
  validateTimestamp(timestamp: number, toleranceSeconds?: number): boolean;
  sanitizePayload(payload: any): any;
}
