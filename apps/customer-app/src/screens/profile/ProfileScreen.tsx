import React, { useState } from 'react'
import { View, Text, ScrollView, TouchableOpacity, Alert, Switch } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  UserIcon,
  SettingsIcon,
  MapPinIcon,
  CreditCardIcon,
  BellIcon,
  HelpCircleIcon,
  LogOutIcon,
  ChevronRightIcon,
  EditIcon,
  MoonIcon,
  SunIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import type { ProfileStackScreenProps } from '../../types/navigation'

type Props = ProfileStackScreenProps<'ProfileMain'>

export function ProfileScreen({ navigation }: Props) {
  const { colors, typography, isDark, toggleTheme } = useTheme()
  const { user, logout } = useAuthStore()

  const [notificationsEnabled, setNotificationsEnabled] = useState(true)

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: logout
        }
      ]
    )
  }

  const menuItems = [
    {
      section: 'Account',
      items: [
        {
          icon: EditIcon,
          title: 'Edit Profile',
          subtitle: 'Update your personal information',
          onPress: () => navigation.navigate('EditProfile'),
        },
        {
          icon: MapPinIcon,
          title: 'Addresses',
          subtitle: 'Manage your saved addresses',
          onPress: () => navigation.navigate('Addresses'),
        },
        {
          icon: CreditCardIcon,
          title: 'Payment Methods',
          subtitle: 'Manage your payment options',
          onPress: () => navigation.navigate('PaymentMethods'),
        },
      ]
    },
    {
      section: 'Preferences',
      items: [
        {
          icon: BellIcon,
          title: 'Notifications',
          subtitle: 'Push notifications and alerts',
          onPress: () => {},
          rightComponent: (
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: colors.gray[300], true: colors.primary[200] }}
              thumbColor={notificationsEnabled ? colors.primary[500] : colors.gray[400]}
            />
          )
        },
        {
          icon: isDark ? SunIcon : MoonIcon,
          title: 'Theme',
          subtitle: isDark ? 'Switch to light mode' : 'Switch to dark mode',
          onPress: toggleTheme,
          rightComponent: (
            <Switch
              value={isDark}
              onValueChange={toggleTheme}
              trackColor={{ false: colors.gray[300], true: colors.primary[200] }}
              thumbColor={isDark ? colors.primary[500] : colors.gray[400]}
            />
          )
        },
      ]
    },
    {
      section: 'Support',
      items: [
        {
          icon: HelpCircleIcon,
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          onPress: () => navigation.navigate('Support'),
        },
      ]
    }
  ]

  const renderProfileHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingHorizontal: 16,
      paddingVertical: 24,
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      {/* Profile Avatar */}
      <View style={{
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: colors.background.tertiary,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
      }}>
        <UserIcon size={32} color={colors.text.tertiary} />
      </View>

      {/* User Info */}
      <Text style={[
        typography.display.small,
        { color: colors.text.primary, textAlign: 'center', marginBottom: 4 }
      ]}>
        {user?.name || 'Guest User'}
      </Text>

      <Text style={[
        typography.body.medium,
        { color: colors.text.secondary, textAlign: 'center', marginBottom: 16 }
      ]}>
        {user?.email || '<EMAIL>'}
      </Text>

      {/* Quick Stats */}
      <View style={{ flexDirection: 'row', gap: 32 }}>
        <View style={{ alignItems: 'center' }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '700' }
          ]}>
            12
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary }
          ]}>
            Orders
          </Text>
        </View>

        <View style={{ alignItems: 'center' }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '700' }
          ]}>
            4.8
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary }
          ]}>
            Rating
          </Text>
        </View>

        <View style={{ alignItems: 'center' }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '700' }
          ]}>
            3
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary }
          ]}>
            Favorites
          </Text>
        </View>
      </View>
    </View>
  )

  const renderMenuItem = (item: any) => (
    <TouchableOpacity
      key={item.title}
      style={{
        backgroundColor: colors.background.primary,
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.primary,
      }}
      onPress={item.onPress}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <View style={{
          width: 40,
          height: 40,
          borderRadius: 20,
          backgroundColor: colors.background.secondary,
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 16,
        }}>
          <item.icon size={20} color={colors.text.primary} />
        </View>

        <View style={{ flex: 1 }}>
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, fontWeight: '600' }
          ]}>
            {item.title}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginTop: 2 }
          ]}>
            {item.subtitle}
          </Text>
        </View>

        {item.rightComponent || (
          <ChevronRightIcon size={20} color={colors.text.tertiary} />
        )}
      </View>
    </TouchableOpacity>
  )

  const renderSection = (section: any) => (
    <View key={section.section} style={{ marginBottom: 24 }}>
      <Text style={[
        typography.body.large,
        {
          color: colors.text.primary,
          fontWeight: '600',
          paddingHorizontal: 16,
          paddingVertical: 12,
          backgroundColor: colors.background.secondary
        }
      ]}>
        {section.section}
      </Text>
      {section.items.map(renderMenuItem)}
    </View>
  )

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderProfileHeader()}

        <View style={{ paddingVertical: 16 }}>
          {menuItems.map(renderSection)}
        </View>

        {/* Logout Button */}
        <View style={{ padding: 16 }}>
          <TouchableOpacity
            style={{
              backgroundColor: colors.error[500],
              borderRadius: 12,
              paddingVertical: 16,
              alignItems: 'center',
              flexDirection: 'row',
              justifyContent: 'center',
            }}
            onPress={handleLogout}
          >
            <LogOutIcon size={20} color={colors.white} />
            <Text style={[
              typography.label.large,
              { color: colors.white, marginLeft: 8, fontWeight: '600' }
            ]}>
              Sign Out
            </Text>
          </TouchableOpacity>
        </View>

        {/* App Version */}
        <View style={{ alignItems: 'center', paddingBottom: 32 }}>
          <Text style={[
            typography.body.small,
            { color: colors.text.tertiary }
          ]}>
            HVPPYPlug+ v1.0.0
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
