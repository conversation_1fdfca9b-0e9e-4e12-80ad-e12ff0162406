{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/layouts/*": ["./src/layouts/*"], "@/data/*": ["./src/data/*"], "@/forms/*": ["./src/forms/*"], "@/business/*": ["./src/business/*"], "@/interactive/*": ["./src/interactive/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/provider/*": ["./src/provider/*"], "@/theme/*": ["./src/theme/*"], "@/styles/*": ["./src/styles/*"], "@/patterns/*": ["./src/patterns/*"], "@/examples/*": ["./src/examples/*"]}}, "include": ["src/**/*", "examples/**/*", "stories/**/*"], "exclude": ["node_modules", "dist", "scripts", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}