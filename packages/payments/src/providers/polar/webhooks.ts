// Polar Webhook Handler Implementation

import crypto from 'crypto-es';
import type { PolarConfig, WebhookEvent } from '../../types';
import { PaymentErrorFactory } from '../../types/errors';

export class PolarWebhookHandler {
  constructor(private config: PolarConfig) {}

  verifySignature(payload: string, signature: string): boolean {
    try {
      // Polar webhook signature verification
      // Format: "t=timestamp,v1=signature"
      const elements = signature.split(',');
      const signatureElements: Record<string, string> = {};

      for (const element of elements) {
        const [key, value] = element.split('=');
        if (key && value) {
          signatureElements[key] = value;
        }
      }

      const timestamp = signatureElements.t;
      const signatures = [
        signatureElements.v1,
        signatureElements.v0
      ].filter(Boolean);

      if (!timestamp || signatures.length === 0) {
        return false;
      }

      // Check timestamp tolerance (5 minutes)
      const timestampNumber = parseInt(timestamp, 10);
      const currentTime = Math.floor(Date.now() / 1000);
      const tolerance = 300; // 5 minutes

      if (Math.abs(currentTime - timestampNumber) > tolerance) {
        return false;
      }

      // Verify signature
      const signedPayload = `${timestamp}.${payload}`;
      const expectedSignature = crypto.HmacSHA256(
        signedPayload,
        this.config.webhookSecret
      ).toString();

      return signatures.some(sig =>
        this.secureCompare(sig || '', expectedSignature)
      );

    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      return false;
    }
  }

  async handleEvent(polarEvent: any): Promise<void> {
    try {
      const event = this.convertToWebhookEvent(polarEvent);
      
      switch (event.type) {
        case 'order.created':
          await this.handleOrderCreated(event);
          break;
          
        case 'order.updated':
          await this.handleOrderUpdated(event);
          break;
          
        case 'checkout.created':
          await this.handleCheckoutCreated(event);
          break;
          
        case 'checkout.updated':
          await this.handleCheckoutUpdated(event);
          break;
          
        case 'customer.created':
          await this.handleCustomerCreated(event);
          break;
          
        case 'customer.updated':
          await this.handleCustomerUpdated(event);
          break;
          
        case 'subscription.created':
          await this.handleSubscriptionCreated(event);
          break;
          
        case 'subscription.updated':
          await this.handleSubscriptionUpdated(event);
          break;
          
        default:
          console.log(`Unhandled Polar event type: ${event.type}`);
      }

    } catch (error) {
      console.error('Error handling Polar webhook:', error);
      throw PaymentErrorFactory.createProviderError(
        'WEBHOOK_VERIFICATION_FAILED',
        'Failed to handle webhook event',
        'polar',
        undefined,
        error instanceof Error ? error.message : 'Unknown error',
        error
      );
    }
  }

  private convertToWebhookEvent(polarEvent: any): WebhookEvent {
    return {
      id: polarEvent.id || `polar_${Date.now()}`,
      type: polarEvent.type,
      provider: 'polar',
      data: polarEvent.data,
      timestamp: new Date(polarEvent.created_at || Date.now()),
      livemode: !this.config.testMode
    } as WebhookEvent;
  }

  private async handleOrderCreated(event: WebhookEvent): Promise<void> {
    const order = event.data;
    
    console.log('Order created:', {
      orderId: order.id,
      amount: order.amount,
      currency: order.currency,
      customerId: order.customer_id,
      checkoutId: order.checkout_id,
      status: order.status
    });

    // Here you would typically:
    // 1. Update your database
    // 2. Send confirmation emails
    // 3. Trigger order fulfillment
    // 4. Update analytics
    
    // Example: Update order status
    if (order.metadata?.orderId) {
      await this.updateOrderStatus(
        order.metadata.orderId,
        'created',
        order.id
      );
    }
  }

  private async handleOrderUpdated(event: WebhookEvent): Promise<void> {
    const order = event.data;
    
    console.log('Order updated:', {
      orderId: order.id,
      status: order.status,
      customerId: order.customer_id,
      checkoutId: order.checkout_id
    });

    // Handle different order statuses
    switch (order.status) {
      case 'succeeded':
        await this.handlePaymentSucceeded(order);
        break;
      case 'failed':
        await this.handlePaymentFailed(order);
        break;
      case 'canceled':
        await this.handlePaymentCanceled(order);
        break;
    }

    // Update order status in your system
    if (order.metadata?.orderId) {
      await this.updateOrderStatus(
        order.metadata.orderId,
        order.status,
        order.id
      );
    }
  }

  private async handleCheckoutCreated(event: WebhookEvent): Promise<void> {
    const checkout = event.data;
    
    console.log('Checkout created:', {
      checkoutId: checkout.id,
      customerId: checkout.customer_id,
      url: checkout.url
    });
  }

  private async handleCheckoutUpdated(event: WebhookEvent): Promise<void> {
    const checkout = event.data;
    
    console.log('Checkout updated:', {
      checkoutId: checkout.id,
      customerId: checkout.customer_id
    });
  }

  private async handleCustomerCreated(event: WebhookEvent): Promise<void> {
    const customer = event.data;
    
    console.log('Customer created:', {
      customerId: customer.id,
      email: customer.email,
      name: customer.name
    });
  }

  private async handleCustomerUpdated(event: WebhookEvent): Promise<void> {
    const customer = event.data;
    
    console.log('Customer updated:', {
      customerId: customer.id,
      email: customer.email,
      name: customer.name
    });
  }

  private async handleSubscriptionCreated(event: WebhookEvent): Promise<void> {
    const subscription = event.data;
    
    console.log('Subscription created:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer_id,
      status: subscription.status
    });
  }

  private async handleSubscriptionUpdated(event: WebhookEvent): Promise<void> {
    const subscription = event.data;
    
    console.log('Subscription updated:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer_id,
      status: subscription.status
    });
  }

  private async handlePaymentSucceeded(order: any): Promise<void> {
    console.log('Payment succeeded:', {
      orderId: order.id,
      amount: order.amount,
      currency: order.currency,
      customerId: order.customer_id
    });

    // Send confirmation notification
    if (order.customer_id) {
      await this.sendPaymentConfirmation(
        order.customer_id,
        order.id,
        order.amount / 100,
        order.currency
      );
    }
  }

  private async handlePaymentFailed(order: any): Promise<void> {
    console.log('Payment failed:', {
      orderId: order.id,
      customerId: order.customer_id
    });

    // Send failure notification
    if (order.customer_id) {
      await this.sendPaymentFailureNotification(
        order.customer_id,
        order.id
      );
    }
  }

  private async handlePaymentCanceled(order: any): Promise<void> {
    console.log('Payment canceled:', {
      orderId: order.id,
      customerId: order.customer_id
    });
  }

  // Helper methods (these would integrate with your actual systems)
  private async updateOrderStatus(
    orderId: string,
    status: string,
    _polarOrderId: string
  ): Promise<void> {
    // This would integrate with your order management system
    console.log(`Updating order ${orderId} status to ${status}`);

    // Example implementation:
    // await orderService.updateStatus(orderId, status, {
    //   polarOrderId: _polarOrderId,
    //   updatedAt: new Date(),
    //   updatedBy: 'polar_webhook'
    // });
  }

  private async sendPaymentConfirmation(
    customerId: string,
    _orderId: string,
    _amount: number,
    _currency: string
  ): Promise<void> {
    // This would integrate with your notification system
    console.log(`Sending payment confirmation to customer ${customerId}`);

    // Example implementation:
    // await notificationService.sendPaymentConfirmation({
    //   customerId,
    //   orderId: _orderId,
    //   amount: _amount,
    //   currency: _currency,
    //   template: 'payment_success'
    // });
  }

  private async sendPaymentFailureNotification(
    customerId: string,
    _orderId: string
  ): Promise<void> {
    // This would integrate with your notification system
    console.log(`Sending payment failure notification to customer ${customerId}`);

    // Example implementation:
    // await notificationService.sendPaymentFailure({
    //   customerId,
    //   orderId: _orderId,
    //   template: 'payment_failed'
    // });
  }

  private secureCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }
}
