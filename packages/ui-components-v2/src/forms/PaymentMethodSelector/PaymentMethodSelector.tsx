"use client"

import React, { useState } from 'react'
import { View, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button } from '@gluestack-ui/themed'
import { CreditCardIcon, SmartphoneIcon, GiftIcon, CheckIcon, PlusIcon } from 'lucide-react-native'

// Types
export interface PaymentMethod {
  id: string
  type: 'card' | 'mpesa' | 'voucher' | 'payfast'
  name: string
  details: string
  icon?: React.ComponentType<any>
  isDefault?: boolean
  lastFour?: string
  expiryDate?: string
  balance?: number
}

export interface PaymentMethodSelectorProps {
  paymentMethods: PaymentMethod[]
  selectedMethodId?: string
  onSelectMethod: (methodId: string) => void
  onAddPaymentMethod?: () => void
  showAddButton?: boolean
  allowVouchers?: boolean
  className?: string
}

// Styles
const containerStyles = tva({
  base: "bg-background-0",
})

const methodCardStyles = tva({
  base: "border border-outline-200 rounded-lg p-4 mb-3 flex-row items-center",
  variants: {
    selected: {
      true: "border-primary-500 bg-primary-50",
      false: "bg-background-0",
    },
    pressable: {
      true: "active:bg-background-50",
    },
  },
})

const iconContainerStyles = tva({
  base: "w-12 h-12 rounded-lg items-center justify-center mr-4",
  variants: {
    type: {
      card: "bg-info-100",
      mpesa: "bg-success-100",
      voucher: "bg-warning-100",
      payfast: "bg-secondary-100",
    },
  },
})

const addButtonStyles = tva({
  base: "border-2 border-dashed border-outline-300 rounded-lg p-4 items-center justify-center",
  variants: {
    pressable: {
      true: "active:bg-background-50",
    },
  },
})

/**
 * PaymentMethodSelector component for choosing payment methods
 * 
 * Features:
 * - Multiple payment method types (card, M-Pesa, vouchers, PayFast)
 * - Visual selection indication
 * - Add new payment method option
 * - Payment method details display
 * - Default method indication
 */
export function PaymentMethodSelector({
  paymentMethods,
  selectedMethodId,
  onSelectMethod,
  onAddPaymentMethod,
  showAddButton = true,
  allowVouchers = true,
  className,
}: PaymentMethodSelectorProps) {
  const getPaymentIcon = (type: PaymentMethod['type']) => {
    const iconProps = { size: 24, className: "text-white" }
    
    switch (type) {
      case 'card':
        return <CreditCardIcon {...iconProps} className="text-info-700" />
      case 'mpesa':
        return <SmartphoneIcon {...iconProps} className="text-success-700" />
      case 'voucher':
        return <GiftIcon {...iconProps} className="text-warning-700" />
      case 'payfast':
        return <CreditCardIcon {...iconProps} className="text-secondary-700" />
      default:
        return <CreditCardIcon {...iconProps} className="text-outline-700" />
    }
  }

  const getPaymentTypeLabel = (type: PaymentMethod['type']) => {
    const labels = {
      card: 'Credit/Debit Card',
      mpesa: 'M-Pesa',
      voucher: 'Voucher',
      payfast: 'PayFast',
    }
    return labels[type]
  }

  const renderPaymentMethod = (method: PaymentMethod) => {
    const isSelected = selectedMethodId === method.id

    return (
      <Pressable
        key={method.id}
        onPress={() => onSelectMethod(method.id)}
        className={methodCardStyles({ 
          selected: isSelected, 
          pressable: true 
        })}
      >
        <View className={iconContainerStyles({ type: method.type })}>
          {method.icon ? <method.icon size={24} /> : getPaymentIcon(method.type)}
        </View>
        
        <View className="flex-1">
          <View className="flex-row items-center justify-between mb-1">
            <Text className="font-medium text-typography-900">
              {method.name}
            </Text>
            {method.isDefault && (
              <View className="bg-primary-100 px-2 py-1 rounded">
                <Text className="text-xs text-primary-700 font-medium">
                  Default
                </Text>
              </View>
            )}
          </View>
          
          <Text className="text-sm text-typography-600 mb-1">
            {getPaymentTypeLabel(method.type)}
          </Text>
          
          <Text className="text-sm text-typography-500">
            {method.details}
          </Text>
          
          {method.type === 'card' && method.lastFour && (
            <Text className="text-sm text-typography-500">
              •••• {method.lastFour}
              {method.expiryDate && ` • Expires ${method.expiryDate}`}
            </Text>
          )}
          
          {method.type === 'voucher' && method.balance !== undefined && (
            <Text className="text-sm text-success-600 font-medium">
              Balance: R{method.balance.toFixed(2)}
            </Text>
          )}
        </View>
        
        {isSelected && (
          <View className="w-6 h-6 bg-primary-500 rounded-full items-center justify-center ml-3">
            <CheckIcon size={16} className="text-white" />
          </View>
        )}
      </Pressable>
    )
  }

  const renderAddPaymentMethod = () => {
    if (!showAddButton || !onAddPaymentMethod) return null

    return (
      <Pressable
        onPress={onAddPaymentMethod}
        className={addButtonStyles({ pressable: true })}
      >
        <PlusIcon size={24} className="text-outline-500 mb-2" />
        <Text className="text-outline-700 font-medium">
          Add Payment Method
        </Text>
      </Pressable>
    )
  }

  // Filter payment methods based on allowVouchers
  const filteredMethods = allowVouchers 
    ? paymentMethods 
    : paymentMethods.filter(method => method.type !== 'voucher')

  return (
    <View className={containerStyles({ class: className })}>
      <Text className="text-lg font-semibold text-typography-900 mb-4">
        Payment Method
      </Text>
      
      {filteredMethods.map(renderPaymentMethod)}
      
      {renderAddPaymentMethod()}
      
      {selectedMethodId && (
        <View className="mt-4 p-4 bg-success-50 rounded-lg">
          <Text className="text-sm text-success-700">
            ✓ Payment method selected
          </Text>
        </View>
      )}
    </View>
  )
}

// Helper function to create default payment methods
export const createDefaultPaymentMethods = (): PaymentMethod[] => [
  {
    id: 'card-1',
    type: 'card',
    name: 'Visa Card',
    details: 'Primary card',
    lastFour: '4242',
    expiryDate: '12/25',
    isDefault: true,
  },
  {
    id: 'mpesa-1',
    type: 'mpesa',
    name: 'M-Pesa',
    details: '+27 123 456 789',
  },
  {
    id: 'voucher-1',
    type: 'voucher',
    name: '1Voucher',
    details: 'Digital voucher',
    balance: 150.00,
  },
  {
    id: 'payfast-1',
    type: 'payfast',
    name: 'PayFast',
    details: 'Secure payment gateway',
  },
]
