import React from 'react'
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../hooks/useTheme'

export function LoadingScreen() {
  const { colors, typography } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.content}>
        <ActivityIndicator size="large" color={colors.primary[500]} />
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, marginTop: 16 }
        ]}>
          Loading HVPPYPlug+ Vendor...
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
})
