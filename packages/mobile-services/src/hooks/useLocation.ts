"use client"

import { useState, useEffect, useCallback } from 'react'
import { LocationService, LocationData, LocationCoordinates } from '../services/LocationService'
import { AppwriteService } from '../services/AppwriteService'

export interface UseLocationOptions {
  enableBackgroundLocation?: boolean
  enableAppwriteSync?: boolean
  distanceInterval?: number
  timeInterval?: number
  onLocationUpdate?: (location: LocationData) => void
  onError?: (error: Error) => void
}

export interface UseLocationReturn {
  currentLocation: LocationData | null
  isTracking: boolean
  isLoading: boolean
  error: Error | null
  startTracking: () => Promise<boolean>
  stopTracking: () => Promise<void>
  getCurrentLocation: () => Promise<LocationData | null>
  calculateDistance: (coord1: LocationCoordinates, coord2: LocationCoordinates) => number
  geocode: (address: string) => Promise<LocationCoordinates | null>
  reverseGeocode: (latitude: number, longitude: number) => Promise<string | null>
}

/**
 * useLocation hook - React hook for location services with Appwrite integration
 * 
 * Features:
 * - Real-time location tracking
 * - Background location updates
 * - Appwrite synchronization
 * - Geocoding and reverse geocoding
 * - Distance calculations
 * - Permission management
 */
export function useLocation(options: UseLocationOptions = {}): UseLocationReturn {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null)
  const [isTracking, setIsTracking] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const locationService = LocationService.getInstance()
  const appwriteService = options.enableAppwriteSync ? AppwriteService.getInstance() : null

  // Initialize location service
  useEffect(() => {
    const initializeLocationService = async () => {
      try {
        setIsLoading(true)
        setError(null)

        await locationService.initialize({
          enableBackgroundLocation: options.enableBackgroundLocation,
          distanceInterval: options.distanceInterval,
          timeInterval: options.timeInterval,
          onLocationUpdate: handleLocationUpdate,
          onError: handleError,
        })
      } catch (err) {
        handleError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeLocationService()
  }, [])

  // Handle location updates
  const handleLocationUpdate = useCallback(
    async (location: LocationData) => {
      setCurrentLocation(location)
      options.onLocationUpdate?.(location)

      // Sync with Appwrite if enabled
      if (options.enableAppwriteSync && appwriteService) {
        try {
          await appwriteService.updateUserLocation(location)
        } catch (err) {
          console.warn('Failed to sync location with Appwrite:', err)
        }
      }
    },
    [options.onLocationUpdate, options.enableAppwriteSync, appwriteService]
  )

  // Handle errors
  const handleError = useCallback(
    (err: Error) => {
      setError(err)
      options.onError?.(err)
    },
    [options.onError]
  )

  // Start location tracking
  const startTracking = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      const success = await locationService.startTracking()
      setIsTracking(success)
      
      return success
    } catch (err) {
      handleError(err as Error)
      return false
    } finally {
      setIsLoading(false)
    }
  }, [handleError])

  // Stop location tracking
  const stopTracking = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true)
      setError(null)

      await locationService.stopTracking()
      setIsTracking(false)
    } catch (err) {
      handleError(err as Error)
    } finally {
      setIsLoading(false)
    }
  }, [handleError])

  // Get current location
  const getCurrentLocation = useCallback(async (): Promise<LocationData | null> => {
    try {
      setIsLoading(true)
      setError(null)

      const location = await locationService.getCurrentLocation()
      if (location) {
        setCurrentLocation(location)
      }
      
      return location
    } catch (err) {
      handleError(err as Error)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [handleError])

  // Calculate distance between coordinates
  const calculateDistance = useCallback(
    (coord1: LocationCoordinates, coord2: LocationCoordinates): number => {
      return locationService.calculateDistance(coord1, coord2)
    },
    []
  )

  // Geocode address to coordinates
  const geocode = useCallback(
    async (address: string): Promise<LocationCoordinates | null> => {
      try {
        setError(null)
        return await locationService.geocode(address)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  // Reverse geocode coordinates to address
  const reverseGeocode = useCallback(
    async (latitude: number, longitude: number): Promise<string | null> => {
      try {
        setError(null)
        return await locationService.reverseGeocode(latitude, longitude)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isTracking) {
        locationService.stopTracking()
      }
    }
  }, [isTracking])

  return {
    currentLocation,
    isTracking,
    isLoading,
    error,
    startTracking,
    stopTracking,
    getCurrentLocation,
    calculateDistance,
    geocode,
    reverseGeocode,
  }
}
