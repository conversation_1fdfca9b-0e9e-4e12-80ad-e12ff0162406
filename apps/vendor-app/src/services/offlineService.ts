import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'

export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiresAt?: number
}

export interface OfflineAction {
  id: string
  type: string
  data: any
  timestamp: number
  retryCount: number
  maxRetries: number
}

class VendorOfflineService {
  private isOnline = true
  private pendingActions: OfflineAction[] = []
  private cachePrefix = '@hvppyplug_vendor_cache_'
  private actionsKey = '@hvppyplug_vendor_offline_actions'

  async initialize() {
    try {
      // Monitor network status
      NetInfo.addEventListener(state => {
        const wasOffline = !this.isOnline
        this.isOnline = state.isConnected ?? false
        
        console.log('Vendor app network status:', this.isOnline ? 'Online' : 'Offline')
        
        // If we just came back online, process pending actions
        if (wasOffline && this.isOnline) {
          this.processPendingActions()
        }
      })

      // Load pending actions from storage
      await this.loadPendingActions()

      console.log('✅ Vendor offline service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize vendor offline service:', error)
    }
  }

  // Cache management
  async setCache<T>(key: string, data: T, expirationMinutes?: number): Promise<void> {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiresAt: expirationMinutes ? Date.now() + (expirationMinutes * 60 * 1000) : undefined
      }

      await AsyncStorage.setItem(this.cachePrefix + key, JSON.stringify(cacheItem))
    } catch (error) {
      console.error('Failed to set vendor cache:', error)
    }
  }

  async getCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(this.cachePrefix + key)
      if (!cached) return null

      const cacheItem: CacheItem<T> = JSON.parse(cached)
      
      // Check if expired
      if (cacheItem.expiresAt && Date.now() > cacheItem.expiresAt) {
        await this.removeCache(key)
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.error('Failed to get vendor cache:', error)
      return null
    }
  }

  async removeCache(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.cachePrefix + key)
    } catch (error) {
      console.error('Failed to remove vendor cache:', error)
    }
  }

  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys()
      const cacheKeys = keys.filter(key => key.startsWith(this.cachePrefix))
      await AsyncStorage.multiRemove(cacheKeys)
    } catch (error) {
      console.error('Failed to clear vendor cache:', error)
    }
  }

  // Offline actions management
  async addOfflineAction(type: string, data: any, maxRetries = 3): Promise<string> {
    const action: OfflineAction = {
      id: Date.now().toString(),
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries
    }

    this.pendingActions.push(action)
    await this.savePendingActions()

    // If online, try to process immediately
    if (this.isOnline) {
      this.processPendingActions()
    }

    return action.id
  }

  private async loadPendingActions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.actionsKey)
      if (stored) {
        this.pendingActions = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Failed to load pending vendor actions:', error)
    }
  }

  private async savePendingActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.actionsKey, JSON.stringify(this.pendingActions))
    } catch (error) {
      console.error('Failed to save pending vendor actions:', error)
    }
  }

  private async processPendingActions(): Promise<void> {
    if (!this.isOnline || this.pendingActions.length === 0) return

    console.log(`Processing ${this.pendingActions.length} pending vendor actions...`)

    const actionsToProcess = [...this.pendingActions]
    
    for (const action of actionsToProcess) {
      try {
        await this.executeAction(action)
        
        // Remove successful action
        this.pendingActions = this.pendingActions.filter(a => a.id !== action.id)
      } catch (error) {
        console.error(`Failed to execute vendor action ${action.type}:`, error)
        
        // Increment retry count
        const actionIndex = this.pendingActions.findIndex(a => a.id === action.id)
        if (actionIndex !== -1) {
          this.pendingActions[actionIndex].retryCount++
          
          // Remove if max retries reached
          if (this.pendingActions[actionIndex].retryCount >= action.maxRetries) {
            console.warn(`Vendor action ${action.type} failed after ${action.maxRetries} retries, removing`)
            this.pendingActions.splice(actionIndex, 1)
          }
        }
      }
    }

    await this.savePendingActions()
  }

  private async executeAction(action: OfflineAction): Promise<void> {
    // This would integrate with your actual API services
    switch (action.type) {
      case 'accept_order':
        // await ordersService.acceptOrder(action.data.orderId)
        console.log('Executing accept_order:', action.data)
        break
      
      case 'decline_order':
        // await ordersService.declineOrder(action.data.orderId, action.data.reason)
        console.log('Executing decline_order:', action.data)
        break
      
      case 'update_order_status':
        // await ordersService.updateStatus(action.data.orderId, action.data.status)
        console.log('Executing update_order_status:', action.data)
        break
      
      case 'send_message':
        // await chatService.sendMessage(action.data)
        console.log('Executing send_message:', action.data)
        break
      
      case 'update_profile':
        // await vendorService.updateProfile(action.data)
        console.log('Executing update_profile:', action.data)
        break
      
      case 'add_service':
        // await servicesService.addService(action.data)
        console.log('Executing add_service:', action.data)
        break
      
      case 'update_service':
        // await servicesService.updateService(action.data.serviceId, action.data.updates)
        console.log('Executing update_service:', action.data)
        break
      
      case 'upload_document':
        // await documentsService.uploadDocument(action.data)
        console.log('Executing upload_document:', action.data)
        break
      
      default:
        console.warn('Unknown vendor action type:', action.type)
    }
  }

  // Utility methods
  isNetworkAvailable(): boolean {
    return this.isOnline
  }

  getPendingActionsCount(): number {
    return this.pendingActions.length
  }

  getPendingActions(): OfflineAction[] {
    return [...this.pendingActions]
  }

  async clearPendingActions(): Promise<void> {
    this.pendingActions = []
    await this.savePendingActions()
  }

  // Vendor-specific cache strategies
  async cacheOrders(orders: any[]): Promise<void> {
    await this.setCache('vendor_orders', orders, 15) // 15 minutes
  }

  async getCachedOrders(): Promise<any[] | null> {
    return await this.getCache('vendor_orders')
  }

  async cacheServices(services: any[]): Promise<void> {
    await this.setCache('vendor_services', services, 60) // 1 hour
  }

  async getCachedServices(): Promise<any[] | null> {
    return await this.getCache('vendor_services')
  }

  async cacheEarnings(earnings: any): Promise<void> {
    await this.setCache('vendor_earnings', earnings, 30) // 30 minutes
  }

  async getCachedEarnings(): Promise<any | null> {
    return await this.getCache('vendor_earnings')
  }

  async cacheProfile(profile: any): Promise<void> {
    await this.setCache('vendor_profile', profile, 120) // 2 hours
  }

  async getCachedProfile(): Promise<any | null> {
    return await this.getCache('vendor_profile')
  }

  async cacheCustomers(customers: any[]): Promise<void> {
    await this.setCache('vendor_customers', customers, 60) // 1 hour
  }

  async getCachedCustomers(): Promise<any[] | null> {
    return await this.getCache('vendor_customers')
  }

  // Offline-first data fetching for vendors
  async getDataWithFallback<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    cacheMinutes?: number
  ): Promise<T | null> {
    try {
      if (this.isOnline) {
        // Try to fetch fresh data
        const freshData = await fetchFunction()
        await this.setCache(key, freshData, cacheMinutes)
        return freshData
      } else {
        // Return cached data when offline
        return await this.getCache<T>(key)
      }
    } catch (error) {
      console.error('Failed to fetch vendor data, falling back to cache:', error)
      return await this.getCache<T>(key)
    }
  }
}

export const offlineService = new VendorOfflineService()
