import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native'
import { useTheme } from '../../providers/ThemeProvider'

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  onRightIconPress?: () => void
  variant?: 'outline' | 'filled' | 'underline'
  size?: 'sm' | 'md' | 'lg'
  required?: boolean
  containerStyle?: ViewStyle
  inputStyle?: TextStyle
  labelStyle?: TextStyle
  testID?: string
}

export function Input({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outline',
  size = 'md',
  required = false,
  containerStyle,
  inputStyle,
  labelStyle,
  testID,
  ...textInputProps
}: InputProps) {
  const { theme } = useTheme()
  const [isFocused, setIsFocused] = useState(false)

  const getContainerStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      marginBottom: theme.spacing.md,
    }

    return {
      ...baseStyles,
      ...containerStyle,
    }
  }

  const getLabelStyles = (): TextStyle => {
    const baseStyles: TextStyle = {
      fontSize: theme.fontSize.sm,
      fontWeight: theme.fontWeight.medium,
      color: theme.colors.gray[700],
      marginBottom: theme.spacing.xs,
    }

    return {
      ...baseStyles,
      ...labelStyle,
    }
  }

  const getInputContainerStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: error 
        ? theme.colors.error[500] 
        : isFocused 
          ? theme.colors.primary[600] 
          : theme.colors.gray[300],
    }

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        minHeight: 36,
      },
      md: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        minHeight: 44,
      },
      lg: {
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
        minHeight: 52,
      },
    }

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      outline: {
        backgroundColor: theme.colors.white,
        borderRadius: theme.borderRadius.md,
      },
      filled: {
        backgroundColor: theme.colors.gray[50],
        borderRadius: theme.borderRadius.md,
        borderColor: theme.colors.transparent,
      },
      underline: {
        backgroundColor: theme.colors.transparent,
        borderRadius: 0,
        borderTopWidth: 0,
        borderLeftWidth: 0,
        borderRightWidth: 0,
        borderBottomWidth: 1,
        paddingHorizontal: 0,
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
    }
  }

  const getInputStyles = (): TextStyle => {
    const baseStyles: TextStyle = {
      flex: 1,
      fontSize: theme.fontSize.base,
      color: theme.colors.gray[900],
      paddingVertical: 0, // Remove default padding
    }

    return {
      ...baseStyles,
      ...inputStyle,
    }
  }

  const getHelperTextStyles = (): TextStyle => {
    return {
      fontSize: theme.fontSize.xs,
      color: error ? theme.colors.error[500] : theme.colors.gray[600],
      marginTop: theme.spacing.xs,
    }
  }

  const renderIcon = (icon: React.ReactNode, position: 'left' | 'right') => {
    if (!icon) return null

    const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20
    const iconColor = error 
      ? theme.colors.error[500] 
      : isFocused 
        ? theme.colors.primary[600] 
        : theme.colors.gray[500]

    const iconElement = React.cloneElement(icon as React.ReactElement, {
      size: iconSize,
      color: iconColor,
    })

    const iconStyle: ViewStyle = {
      marginLeft: position === 'left' ? 0 : theme.spacing.sm,
      marginRight: position === 'right' ? 0 : theme.spacing.sm,
    }

    if (position === 'right' && onRightIconPress) {
      return (
        <TouchableOpacity
          onPress={onRightIconPress}
          style={[iconStyle, { padding: theme.spacing.xs }]}
        >
          {iconElement}
        </TouchableOpacity>
      )
    }

    return (
      <View style={iconStyle}>
        {iconElement}
      </View>
    )
  }

  return (
    <View style={getContainerStyles()} testID={testID}>
      {label && (
        <Text style={getLabelStyles()}>
          {label}
          {required && (
            <Text style={{ color: theme.colors.error[500] }}> *</Text>
          )}
        </Text>
      )}
      
      <View style={getInputContainerStyles()}>
        {renderIcon(leftIcon, 'left')}
        
        <TextInput
          {...textInputProps}
          style={getInputStyles()}
          onFocus={(e) => {
            setIsFocused(true)
            textInputProps.onFocus?.(e)
          }}
          onBlur={(e) => {
            setIsFocused(false)
            textInputProps.onBlur?.(e)
          }}
          placeholderTextColor={theme.colors.gray[500]}
        />
        
        {renderIcon(rightIcon, 'right')}
      </View>
      
      {(error || helperText) && (
        <Text style={getHelperTextStyles()}>
          {error || helperText}
        </Text>
      )}
    </View>
  )
}
