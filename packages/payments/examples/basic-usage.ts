// Basic Usage Example for @hvppyplug/payments

import { 
  PaymentManager, 
  createPaymentProvider,
  type PaymentRequest,
  type PolarConfig 
} from '@hvppyplug/payments';

// Example 1: Basic Payment Setup
async function setupPayments() {
  // Configure Polar provider
  const polarConfig: PolarConfig = {
    accessToken: process.env.POLAR_ACCESS_TOKEN!,
    webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
    testMode: true,
    server: 'sandbox',
    successUrl: 'https://hvppyplug.co.za/payment/success',
    cancelUrl: 'https://hvppyplug.co.za/payment/cancel',
    merchantDisplayName: 'HVPPYPlug+'
  };

  // Create payment manager
  const paymentManager = new PaymentManager({
    providers: [
      {
        type: 'polar',
        config: polarConfig
      }
    ],
    defaultProvider: 'polar',
    retryAttempts: 3,
    retryDelay: 1000
  });

  return paymentManager;
}

// Example 2: Create a Payment Intent
async function createPayment() {
  const paymentManager = await setupPayments();

  const paymentRequest: PaymentRequest = {
    amount: {
      value: 149.99,
      currency: 'ZAR',
      formatted: 'R149.99'
    },
    paymentMethod: 'polar_checkout',
    customerId: 'customer_12345',
    orderId: 'order_67890',
    description: 'HVPPYPlug+ Snack Delivery Order',
    metadata: {
      orderType: 'delivery',
      location: 'Soweto',
      items: '3',
      vendor: 'vendor_456'
    },
    returnUrl: 'hvppyplug://payment/success?order=67890',
    cancelUrl: 'hvppyplug://payment/cancel?order=67890'
  };

  try {
    const result = await paymentManager.createPaymentIntent(paymentRequest);

    if (result.success) {
      console.log('Payment intent created successfully!');
      console.log('Payment ID:', result.paymentIntent.id);
      
      if (result.requiresAction && result.actionUrl) {
        console.log('Redirect user to:', result.actionUrl);
        // In a real app, redirect user to this URL
        return {
          status: 'redirect_required',
          url: result.actionUrl,
          paymentId: result.paymentIntent.id
        };
      }
    } else {
      console.error('Payment creation failed:', result.error);
      return {
        status: 'failed',
        error: result.error
      };
    }
  } catch (error) {
    console.error('Payment error:', error);
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Example 3: Check Payment Status
async function checkPaymentStatus(paymentIntentId: string) {
  const paymentManager = await setupPayments();

  try {
    const paymentIntent = await paymentManager.getPaymentStatus(paymentIntentId);
    
    console.log('Payment Status:', paymentIntent.status);
    console.log('Amount:', paymentIntent.amount.formatted);
    console.log('Customer:', paymentIntent.customerId);
    console.log('Order:', paymentIntent.orderId);

    return {
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      customerId: paymentIntent.customerId,
      orderId: paymentIntent.orderId,
      createdAt: paymentIntent.createdAt,
      updatedAt: paymentIntent.updatedAt
    };
  } catch (error) {
    console.error('Failed to get payment status:', error);
    throw error;
  }
}

// Example 4: Handle Webhook
async function handleWebhook(
  payload: string, 
  signature: string, 
  providerType: string = 'polar'
) {
  const paymentManager = await setupPayments();

  try {
    await paymentManager.handleWebhook(payload, signature, providerType);
    console.log('Webhook processed successfully');
    return { success: true };
  } catch (error) {
    console.error('Webhook processing failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Example 5: Process Refund
async function processRefund(paymentIntentId: string, amount?: number) {
  const paymentManager = await setupPayments();

  try {
    const refundResult = await paymentManager.refundPayment({
      paymentIntentId,
      amount: amount ? {
        value: amount,
        currency: 'ZAR',
        formatted: `R${amount.toFixed(2)}`
      } : undefined, // Full refund if no amount specified
      reason: 'Customer requested refund',
      metadata: {
        refundedBy: 'admin',
        refundDate: new Date().toISOString()
      }
    });

    if (refundResult.success) {
      console.log('Refund processed successfully!');
      console.log('Refund ID:', refundResult.refundId);
      console.log('Refund Amount:', refundResult.amount.formatted);
      return {
        success: true,
        refundId: refundResult.refundId,
        amount: refundResult.amount
      };
    } else {
      console.error('Refund failed:', refundResult.error);
      return {
        success: false,
        error: refundResult.error
      };
    }
  } catch (error) {
    console.error('Refund error:', error);
    throw error;
  }
}

// Example 6: Multiple Provider Setup (Future)
async function setupMultipleProviders() {
  const paymentManager = new PaymentManager({
    providers: [
      // Primary: Polar for international payments
      {
        type: 'polar',
        config: {
          accessToken: process.env.POLAR_ACCESS_TOKEN!,
          webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
          testMode: true,
          server: 'sandbox'
        }
      },
      // Secondary: PayFast for local South African payments (TODO)
      // {
      //   type: 'payfast',
      //   config: {
      //     merchantId: process.env.PAYFAST_MERCHANT_ID!,
      //     merchantKey: process.env.PAYFAST_MERCHANT_KEY!,
      //     testMode: true
      //   }
      // }
    ],
    defaultProvider: 'polar',
    fallbackProviders: ['payfast'], // Fallback to PayFast if Polar fails
    retryAttempts: 3
  });

  return paymentManager;
}

// Example 7: Error Handling
async function robustPaymentFlow(orderData: any) {
  try {
    const paymentManager = await setupPayments();
    
    // Create payment with comprehensive error handling
    const result = await paymentManager.createPaymentIntent({
      amount: {
        value: orderData.total,
        currency: 'ZAR',
        formatted: `R${orderData.total.toFixed(2)}`
      },
      paymentMethod: 'polar_checkout',
      customerId: orderData.customerId,
      orderId: orderData.orderId,
      description: `HVPPYPlug+ Order ${orderData.orderId}`,
      metadata: {
        items: JSON.stringify(orderData.items),
        deliveryAddress: orderData.deliveryAddress,
        vendorId: orderData.vendorId
      }
    });

    return result;

  } catch (error: any) {
    // Handle different types of errors
    switch (error.type) {
      case 'validation':
        console.error('Validation error:', error.message);
        return { 
          success: false, 
          error: 'Please check your payment details and try again.' 
        };
        
      case 'network':
        console.error('Network error:', error.message);
        return { 
          success: false, 
          error: 'Connection problem. Please check your internet and try again.' 
        };
        
      case 'security':
        console.error('Security error:', error.message);
        return { 
          success: false, 
          error: 'Security verification failed. Please contact support.' 
        };
        
      case 'business':
        console.error('Business rule error:', error.message);
        return { 
          success: false, 
          error: error.userMessage || 'Payment not allowed at this time.' 
        };
        
      default:
        console.error('Unknown error:', error);
        return { 
          success: false, 
          error: 'An unexpected error occurred. Please try again.' 
        };
    }
  }
}

// Export examples for use in other files
export {
  setupPayments,
  createPayment,
  checkPaymentStatus,
  handleWebhook,
  processRefund,
  setupMultipleProviders,
  robustPaymentFlow
};
