import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'

export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiresAt?: number
}

export interface OfflineAction {
  id: string
  type: string
  data: any
  timestamp: number
  retryCount: number
  maxRetries: number
}

class OfflineService {
  private isOnline = true
  private pendingActions: OfflineAction[] = []
  private cachePrefix = '@hvppyplug_cache_'
  private actionsKey = '@hvppyplug_offline_actions'

  async initialize() {
    try {
      // Monitor network status
      NetInfo.addEventListener(state => {
        const wasOffline = !this.isOnline
        this.isOnline = state.isConnected ?? false
        
        console.log('Network status:', this.isOnline ? 'Online' : 'Offline')
        
        // If we just came back online, process pending actions
        if (wasOffline && this.isOnline) {
          this.processPendingActions()
        }
      })

      // Load pending actions from storage
      await this.loadPendingActions()

      console.log('✅ Offline service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize offline service:', error)
    }
  }

  // Cache management
  async setCache<T>(key: string, data: T, expirationMinutes?: number): Promise<void> {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiresAt: expirationMinutes ? Date.now() + (expirationMinutes * 60 * 1000) : undefined
      }

      await AsyncStorage.setItem(this.cachePrefix + key, JSON.stringify(cacheItem))
    } catch (error) {
      console.error('Failed to set cache:', error)
    }
  }

  async getCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(this.cachePrefix + key)
      if (!cached) return null

      const cacheItem: CacheItem<T> = JSON.parse(cached)
      
      // Check if expired
      if (cacheItem.expiresAt && Date.now() > cacheItem.expiresAt) {
        await this.removeCache(key)
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.error('Failed to get cache:', error)
      return null
    }
  }

  async removeCache(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.cachePrefix + key)
    } catch (error) {
      console.error('Failed to remove cache:', error)
    }
  }

  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys()
      const cacheKeys = keys.filter(key => key.startsWith(this.cachePrefix))
      await AsyncStorage.multiRemove(cacheKeys)
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }

  // Offline actions management
  async addOfflineAction(type: string, data: any, maxRetries = 3): Promise<string> {
    const action: OfflineAction = {
      id: Date.now().toString(),
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries
    }

    this.pendingActions.push(action)
    await this.savePendingActions()

    // If online, try to process immediately
    if (this.isOnline) {
      this.processPendingActions()
    }

    return action.id
  }

  private async loadPendingActions(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.actionsKey)
      if (stored) {
        this.pendingActions = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Failed to load pending actions:', error)
    }
  }

  private async savePendingActions(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.actionsKey, JSON.stringify(this.pendingActions))
    } catch (error) {
      console.error('Failed to save pending actions:', error)
    }
  }

  private async processPendingActions(): Promise<void> {
    if (!this.isOnline || this.pendingActions.length === 0) return

    console.log(`Processing ${this.pendingActions.length} pending actions...`)

    const actionsToProcess = [...this.pendingActions]
    
    for (const action of actionsToProcess) {
      try {
        await this.executeAction(action)
        
        // Remove successful action
        this.pendingActions = this.pendingActions.filter(a => a.id !== action.id)
      } catch (error) {
        console.error(`Failed to execute action ${action.type}:`, error)
        
        // Increment retry count
        const actionIndex = this.pendingActions.findIndex(a => a.id === action.id)
        if (actionIndex !== -1) {
          this.pendingActions[actionIndex].retryCount++
          
          // Remove if max retries reached
          if (this.pendingActions[actionIndex].retryCount >= action.maxRetries) {
            console.warn(`Action ${action.type} failed after ${action.maxRetries} retries, removing`)
            this.pendingActions.splice(actionIndex, 1)
          }
        }
      }
    }

    await this.savePendingActions()
  }

  private async executeAction(action: OfflineAction): Promise<void> {
    // This would integrate with your actual API services
    switch (action.type) {
      case 'create_order':
        // await ordersService.createOrder(action.data)
        console.log('Executing create_order:', action.data)
        break
      
      case 'update_profile':
        // await userService.updateProfile(action.data)
        console.log('Executing update_profile:', action.data)
        break
      
      case 'send_message':
        // await chatService.sendMessage(action.data)
        console.log('Executing send_message:', action.data)
        break
      
      case 'rate_service':
        // await ratingsService.rateService(action.data)
        console.log('Executing rate_service:', action.data)
        break
      
      default:
        console.warn('Unknown action type:', action.type)
    }
  }

  // Utility methods
  isNetworkAvailable(): boolean {
    return this.isOnline
  }

  getPendingActionsCount(): number {
    return this.pendingActions.length
  }

  getPendingActions(): OfflineAction[] {
    return [...this.pendingActions]
  }

  async clearPendingActions(): Promise<void> {
    this.pendingActions = []
    await this.savePendingActions()
  }

  // Cache strategies for different data types
  async cacheServices(services: any[]): Promise<void> {
    await this.setCache('services', services, 30) // 30 minutes
  }

  async getCachedServices(): Promise<any[] | null> {
    return await this.getCache('services')
  }

  async cacheOrders(orders: any[]): Promise<void> {
    await this.setCache('orders', orders, 60) // 1 hour
  }

  async getCachedOrders(): Promise<any[] | null> {
    return await this.getCache('orders')
  }

  async cacheUserProfile(profile: any): Promise<void> {
    await this.setCache('user_profile', profile, 120) // 2 hours
  }

  async getCachedUserProfile(): Promise<any | null> {
    return await this.getCache('user_profile')
  }

  async cacheCategories(categories: any[]): Promise<void> {
    await this.setCache('categories', categories, 240) // 4 hours
  }

  async getCachedCategories(): Promise<any[] | null> {
    return await this.getCache('categories')
  }

  // Offline-first data fetching
  async getDataWithFallback<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    cacheMinutes?: number
  ): Promise<T | null> {
    try {
      if (this.isOnline) {
        // Try to fetch fresh data
        const freshData = await fetchFunction()
        await this.setCache(key, freshData, cacheMinutes)
        return freshData
      } else {
        // Return cached data when offline
        return await this.getCache<T>(key)
      }
    } catch (error) {
      console.error('Failed to fetch data, falling back to cache:', error)
      return await this.getCache<T>(key)
    }
  }
}

export const offlineService = new OfflineService()
