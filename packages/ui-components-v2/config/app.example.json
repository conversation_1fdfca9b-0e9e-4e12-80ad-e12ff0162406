{"expo": {"name": "ui-example-nativewind", "slug": "ui-example-nativewind", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.gluestack.uiexamplenativewind"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.gluestack.uiexamplenativewind"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "extra": {"eas": {"projectId": "42cce9b5-9fbe-4572-92ba-fc43b2437a85"}}, "owner": "gluestack", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/42cce9b5-9fbe-4572-92ba-fc43b2437a85"}}, "_comment": "Expo app configuration - Extracted from ui-example-nativewind"}