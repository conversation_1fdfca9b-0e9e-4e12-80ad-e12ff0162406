import * as Notifications from 'expo-notifications'
import * as Device from 'expo-device'
import { Platform } from 'react-native'
import Constants from 'expo-constants'

export interface NotificationData {
  title: string
  body: string
  data?: any
  categoryId?: string
  sound?: boolean
  badge?: number
}

export interface PushNotificationToken {
  token: string
  type: 'expo' | 'fcm' | 'apns'
}

class VendorNotificationService {
  private isInitialized = false
  private pushToken: string | null = null

  async initialize() {
    if (this.isInitialized) return

    try {
      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      })

      // Request permissions
      await this.requestPermissions()

      // Get push token
      await this.registerForPushNotifications()

      // Setup vendor-specific notification categories
      await this.setupNotificationCategories()

      this.isInitialized = true
      console.log('✅ Vendor notification service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize vendor notification service:', error)
    }
  }

  private async requestPermissions() {
    if (!Device.isDevice) {
      console.warn('Push notifications only work on physical devices')
      return false
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = existingStatus

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync()
      finalStatus = status
    }

    if (finalStatus !== 'granted') {
      console.warn('Push notification permissions not granted')
      return false
    }

    return true
  }

  private async registerForPushNotifications() {
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId || Constants.easConfig?.projectId

      if (!projectId) {
        console.warn('No EAS project ID found for push notifications')
        return
      }

      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId,
      })

      this.pushToken = tokenData.data
      console.log('Vendor push token:', this.pushToken)

      // In a real app, send this token to your backend
      // await this.sendTokenToBackend(this.pushToken)

      return this.pushToken
    } catch (error) {
      console.error('Failed to get vendor push token:', error)
    }
  }

  private async setupNotificationCategories() {
    // New order notifications
    await Notifications.setNotificationCategoryAsync('new_order', [
      {
        identifier: 'accept_order',
        buttonTitle: 'Accept',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'view_order',
        buttonTitle: 'View Details',
        options: {
          opensAppToForeground: true,
        },
      },
    ])

    // Customer message notifications
    await Notifications.setNotificationCategoryAsync('customer_message', [
      {
        identifier: 'reply',
        buttonTitle: 'Reply',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'view_chat',
        buttonTitle: 'View Chat',
        options: {
          opensAppToForeground: true,
        },
      },
    ])

    // Order status reminders
    await Notifications.setNotificationCategoryAsync('order_reminder', [
      {
        identifier: 'start_work',
        buttonTitle: 'Start Work',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'view_order',
        buttonTitle: 'View Order',
        options: {
          opensAppToForeground: true,
        },
      },
    ])

    // Payment notifications
    await Notifications.setNotificationCategoryAsync('payment_received', [
      {
        identifier: 'view_earnings',
        buttonTitle: 'View Earnings',
        options: {
          opensAppToForeground: true,
        },
      },
    ])
  }

  async scheduleLocalNotification(notification: NotificationData, delay?: number) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          categoryIdentifier: notification.categoryId,
          sound: notification.sound !== false,
          badge: notification.badge,
        },
        trigger: delay ? { seconds: delay } : null,
      })

      return notificationId
    } catch (error) {
      console.error('Failed to schedule vendor notification:', error)
      throw error
    }
  }

  async cancelNotification(notificationId: string) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId)
    } catch (error) {
      console.error('Failed to cancel notification:', error)
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync()
    } catch (error) {
      console.error('Failed to cancel all notifications:', error)
    }
  }

  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count)
    } catch (error) {
      console.error('Failed to set badge count:', error)
    }
  }

  async clearBadge() {
    await this.setBadgeCount(0)
  }

  // Vendor-specific notification types
  async notifyNewOrder(orderId: string, customerName: string, serviceName: string, amount: number) {
    await this.scheduleLocalNotification({
      title: 'New Order Received!',
      body: `${customerName} requested ${serviceName} for R${amount}`,
      data: { orderId, type: 'new_order' },
      categoryId: 'new_order',
    })
  }

  async notifyCustomerMessage(customerName: string, message: string, orderId: string) {
    await this.scheduleLocalNotification({
      title: `Message from ${customerName}`,
      body: message,
      data: { orderId, type: 'customer_message' },
      categoryId: 'customer_message',
    })
  }

  async notifyOrderReminder(serviceName: string, scheduledTime: string, orderId: string) {
    const reminderTime = new Date(scheduledTime)
    reminderTime.setMinutes(reminderTime.getMinutes() - 30) // 30 minutes before

    const delay = Math.max(0, reminderTime.getTime() - Date.now()) / 1000

    await this.scheduleLocalNotification({
      title: 'Upcoming Service',
      body: `${serviceName} starts in 30 minutes`,
      data: { orderId, type: 'order_reminder' },
      categoryId: 'order_reminder',
    }, delay)
  }

  async notifyPaymentReceived(amount: number, customerName: string, orderId: string) {
    await this.scheduleLocalNotification({
      title: 'Payment Received',
      body: `You received R${amount} from ${customerName}`,
      data: { orderId, type: 'payment_received' },
      categoryId: 'payment_received',
    })
  }

  async notifyOrderCancellation(customerName: string, serviceName: string, orderId: string) {
    await this.scheduleLocalNotification({
      title: 'Order Cancelled',
      body: `${customerName} cancelled ${serviceName}`,
      data: { orderId, type: 'order_cancelled' },
    })
  }

  async notifyVerificationUpdate(status: 'approved' | 'rejected', reason?: string) {
    const title = status === 'approved' ? 'Verification Approved!' : 'Verification Update'
    const body = status === 'approved' 
      ? 'Your vendor account has been verified. You can now receive orders!'
      : `Verification ${status}${reason ? `: ${reason}` : ''}`

    await this.scheduleLocalNotification({
      title,
      body,
      data: { type: 'verification_update', status },
    })
  }

  // Handle notification responses
  addNotificationResponseListener(callback: (response: Notifications.NotificationResponse) => void) {
    return Notifications.addNotificationResponseReceivedListener(callback)
  }

  addNotificationReceivedListener(callback: (notification: Notifications.Notification) => void) {
    return Notifications.addNotificationReceivedListener(callback)
  }

  getPushToken(): string | null {
    return this.pushToken
  }

  async getDeliveredNotifications() {
    return await Notifications.getPresentedNotificationsAsync()
  }

  async dismissNotification(notificationId: string) {
    await Notifications.dismissNotificationAsync(notificationId)
  }

  async dismissAllNotifications() {
    await Notifications.dismissAllNotificationsAsync()
  }
}

export const notificationService = new VendorNotificationService()
