const fs = require('fs')
const path = require('path')

const screens = [
  // Services
  { path: 'src/screens/services/ServiceDetailScreen.tsx', name: 'ServiceDetailScreen', title: 'Service Detail' },
  { path: 'src/screens/services/ServiceCategoryScreen.tsx', name: 'ServiceCategoryScreen', title: 'Service Category' },
  { path: 'src/screens/services/ServiceListScreen.tsx', name: 'ServiceListScreen', title: 'Service List' },
  
  // Providers
  { path: 'src/screens/providers/ProviderProfileScreen.tsx', name: 'ProviderProfileScreen', title: 'Provider Profile' },
  
  // Booking
  { path: 'src/screens/booking/BookingScreen.tsx', name: 'BookingScreen', title: 'Booking' },
  
  // Orders
  { path: 'src/screens/orders/OrdersScreen.tsx', name: 'OrdersScreen', title: 'Orders' },
  { path: 'src/screens/orders/OrderDetailScreen.tsx', name: 'OrderDetailScreen', title: 'Order Detail' },
  { path: 'src/screens/orders/OrderHistoryScreen.tsx', name: 'OrderHistoryScreen', title: 'Order History' },
  { path: 'src/screens/orders/ActiveOrdersScreen.tsx', name: 'ActiveOrdersScreen', title: 'Active Orders' },
  { path: 'src/screens/orders/OrderTrackingScreen.tsx', name: 'OrderTrackingScreen', title: 'Order Tracking' },
  { path: 'src/screens/orders/ReviewOrderScreen.tsx', name: 'ReviewOrderScreen', title: 'Review Order' },
  
  // Chat
  { path: 'src/screens/chat/ChatScreen.tsx', name: 'ChatScreen', title: 'Chat' },
  
  // Payment
  { path: 'src/screens/payment/PaymentScreen.tsx', name: 'PaymentScreen', title: 'Payment' },
  
  // Location
  { path: 'src/screens/location/LocationPickerScreen.tsx', name: 'LocationPickerScreen', title: 'Location Picker' },
  
  // Notifications
  { path: 'src/screens/notifications/NotificationDetailScreen.tsx', name: 'NotificationDetailScreen', title: 'Notification Detail' },
  
  // Support
  { path: 'src/screens/support/HelpScreen.tsx', name: 'HelpScreen', title: 'Help' },
  { path: 'src/screens/support/SupportScreen.tsx', name: 'SupportScreen', title: 'Support' },
  
  // Search
  { path: 'src/screens/search/SearchScreen.tsx', name: 'SearchScreen', title: 'Search' },
  { path: 'src/screens/search/SearchResultsScreen.tsx', name: 'SearchResultsScreen', title: 'Search Results' },
  { path: 'src/screens/search/FilterOptionsScreen.tsx', name: 'FilterOptionsScreen', title: 'Filter Options' },
  
  // Favorites
  { path: 'src/screens/favorites/FavoritesScreen.tsx', name: 'FavoritesScreen', title: 'Favorites' },
  
  // Profile
  { path: 'src/screens/profile/ProfileScreen.tsx', name: 'ProfileScreen', title: 'Profile' },
  { path: 'src/screens/profile/EditProfileScreen.tsx', name: 'EditProfileScreen', title: 'Edit Profile' },
  { path: 'src/screens/profile/AddressesScreen.tsx', name: 'AddressesScreen', title: 'Addresses' },
  { path: 'src/screens/profile/AddAddressScreen.tsx', name: 'AddAddressScreen', title: 'Add Address' },
  { path: 'src/screens/profile/PaymentMethodsScreen.tsx', name: 'PaymentMethodsScreen', title: 'Payment Methods' },
  { path: 'src/screens/profile/AddPaymentMethodScreen.tsx', name: 'AddPaymentMethodScreen', title: 'Add Payment Method' },
  { path: 'src/screens/profile/SettingsScreen.tsx', name: 'SettingsScreen', title: 'Settings' },
  { path: 'src/screens/profile/NotificationsScreen.tsx', name: 'NotificationsScreen', title: 'Notifications' },
  { path: 'src/screens/profile/HelpCenterScreen.tsx', name: 'HelpCenterScreen', title: 'Help Center' },
  { path: 'src/screens/profile/AboutScreen.tsx', name: 'AboutScreen', title: 'About' },
]

const template = (name, title) => `import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../../hooks/useTheme'

export function ${name}() {
  const { colors, typography } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.content}>
        <Text style={[typography.display.small, { color: colors.text.primary, textAlign: 'center' }]}>
          ${title}
        </Text>
        <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginTop: 16 }]}>
          Coming Soon
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 24 },
})
`

screens.forEach(screen => {
  const dir = path.dirname(screen.path)
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
  
  fs.writeFileSync(screen.path, template(screen.name, screen.title))
  console.log(`Created ${screen.path}`)
})

console.log('All placeholder screens created!')
