// Main Layout Components
export { default as Header } from './Header';
export type { HeaderProps } from './Header';

export { default as Sidebar } from './Sidebar';
export { default as WebSidebar } from './WebSidebar';

// Header Components
export { default as MarketplaceLogo } from './header/MarketplaceLogo';
export type { MarketplaceLogoProps } from './header/MarketplaceLogo';

export { default as ShoppingCart } from './header/ShoppingCart';
export type { ShoppingCartProps } from './header/ShoppingCart';

export { default as UserProfile } from './header/UserProfile';
export type { UserProfileProps } from './header/UserProfile';

export { default as HeaderTabs } from './header/HeaderTabs';
export { default as ToggleMode } from './header/ToggleMode';

// Sidebar Filter Components
export { default as ProductOptions } from './sidebar/BookingOptions';
export type { ProductOptionsProps, ProductOption } from './sidebar/BookingOptions';

export { default as ProductCategorySection } from './sidebar/PlaceTypeSection';
export type { ProductCategorySectionProps, CategoryItem } from './sidebar/PlaceTypeSection';

export { default as ProductRatingSection } from './sidebar/CustomerRatingSection';
export type { ProductRatingSectionProps, RatingOption } from './sidebar/CustomerRatingSection';

export { default as PriceRangeSection } from './sidebar/PriceRangeSection';
export { default as SortBySection } from './sidebar/SortBySection';
export { default as FiltersAppliedSection } from './sidebar/FiltersAppliedSection';
export { default as AmenitiesSection } from './sidebar/AmenitiesSection';

// Product Components
export { default as ProductCard } from './product/ProductCard';
export type { ProductCardProps, Product } from './product/ProductCard';

// Cart Components
export { default as ShoppingCartSidebar } from './cart/ShoppingCartSidebar';
export type { ShoppingCartSidebarProps, CartItem } from './cart/ShoppingCartSidebar';

// Admin Components
export { default as VendorManagement } from './admin/VendorManagement';
export type { VendorManagementProps, Vendor } from './admin/VendorManagement';

export { default as OrderManagement } from './admin/OrderManagement';
export type { OrderManagementProps, Order, OrderItem } from './admin/OrderManagement';

// Vendor Components
export { default as VendorDashboard } from './vendor/VendorDashboard';
export type { VendorDashboardProps, VendorStats, RecentOrder, TopProduct } from './vendor/VendorDashboard';

// Main Content Components
export { default as MainContent } from './main-content/MainContent';
export { default as MainContentHeader } from './main-content/MainContentHeader';

// Other Components
export { default as Banner } from './Banner';
export { default as ExplorePage } from './ExplorePage';
export { default as HomestayPage } from './HomestayPage';
export { default as LogoutAlertDialog } from './LogoutAlertDialog';
export { default as MobileBottomTabs } from './MobileBottomTabs';
export { default as MobileModeChangeButton } from './MobileModeChangeButton';
export { default as MobileProfilePage } from './MobileProfilePage';
export { default as MobileSidebarActionsheet } from './MobileSidebarActionsheet';