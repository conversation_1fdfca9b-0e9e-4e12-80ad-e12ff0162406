import React, { useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Package, Wifi, WifiOff, History } from 'lucide-react-native'
import { useNavigation } from '@react-navigation/native'
import type { NativeStackNavigationProp } from '@react-navigation/native-stack'

import { useTheme } from '../../providers/ThemeProvider'
import { OrderCard } from '../../components/business'
import { Button } from '../../components/ui'
import { useOrderStore } from '../../stores/orderStore'
import { useAuthStore } from '../../stores/authStore'
import type { OrdersStackParamList } from '../../types/navigation'

type NavigationProp = NativeStackNavigationProp<OrdersStackParamList, 'OrdersList'>

export function OrdersListScreen() {
  const { theme } = useTheme()
  const navigation = useNavigation<NavigationProp>()
  const { runner } = useAuthStore()

  const {
    orders,
    loading,
    error,
    isOnline,
    fetchOrders,
    acceptOrder,
    declineOrder,
    startPickup,
    completeDelivery,
    subscribeToOrders,
    unsubscribeFromOrders,
    clearError,
  } = useOrderStore()

  useEffect(() => {
    if (runner) {
      fetchOrders()
      subscribeToOrders(runner.id)
    }

    return () => {
      unsubscribeFromOrders()
    }
  }, [runner])

  const handleRefresh = async () => {
    if (runner) {
      await fetchOrders()
    }
  }

  const handleAcceptOrder = async (orderId: string) => {
    const success = await acceptOrder(orderId)
    if (success) {
      Alert.alert('Success', 'Order accepted successfully!')
    } else {
      Alert.alert('Error', 'Failed to accept order. Please try again.')
    }
  }

  const handleDeclineOrder = async (orderId: string) => {
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            const success = await declineOrder(orderId)
            if (!success) {
              Alert.alert('Error', 'Failed to decline order. Please try again.')
            }
          }
        }
      ]
    )
  }

  const handleViewDetails = (orderId: string) => {
    navigation.navigate('OrderDetails', { orderId })
  }

  const handleStartPickup = async (orderId: string) => {
    const success = await startPickup(orderId)
    if (success) {
      Alert.alert('Success', 'Pickup started!')
    } else {
      Alert.alert('Error', 'Failed to start pickup. Please try again.')
    }
  }

  const handleCompleteDelivery = async (orderId: string) => {
    const success = await completeDelivery(orderId)
    if (success) {
      Alert.alert('Success', 'Delivery completed!')
    } else {
      Alert.alert('Error', 'Failed to complete delivery. Please try again.')
    }
  }

  // Show error if there's one
  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'OK', onPress: clearError }
      ])
    }
  }, [error])





  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
              Orders
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.gray[600] }]}>
              {orders.length} orders available
            </Text>
          </View>

          <View style={styles.headerActions}>
            {/* History Button */}
            <Button
              title=""
              variant="ghost"
              size="sm"
              icon={<History size={20} color={theme.colors.gray[600]} />}
              onPress={() => navigation.navigate('OrderHistory')}
              style={styles.historyButton}
            />

            {/* Online Status Indicator */}
            <View style={[styles.statusIndicator, {
              backgroundColor: isOnline ? theme.colors.success[100] : theme.colors.gray[100]
            }]}>
              {isOnline ? (
                <Wifi size={16} color={theme.colors.success[600]} />
              ) : (
                <WifiOff size={16} color={theme.colors.gray[500]} />
              )}
              <Text style={[
                styles.statusText,
                { color: isOnline ? theme.colors.success[600] : theme.colors.gray[500] }
              ]}>
                {isOnline ? 'Online' : 'Offline'}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary[600]}
          />
        }
      >
        {orders.length > 0 ? (
          orders.map((order) => (
            <OrderCard
              key={order.id}
              order={order}
              onAccept={handleAcceptOrder}
              onDecline={handleDeclineOrder}
              onViewDetails={handleViewDetails}
              onStartPickup={handleStartPickup}
              onCompleteDelivery={handleCompleteDelivery}
            />
          ))
        ) : (
          <View style={[styles.emptyState, { backgroundColor: theme.colors.white }]}>
            <Package size={64} color={theme.colors.gray[400]} />
            <Text style={[styles.emptyTitle, { color: theme.colors.gray[900] }]}>
              No Orders Available
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.gray[600] }]}>
              Turn on your online status to start receiving orders from customers in your area.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  historyButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
