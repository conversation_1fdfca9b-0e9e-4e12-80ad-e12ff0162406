{"extends": "../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "module": "CommonJS", "target": "ES2022", "lib": ["ES2022"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "declaration": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}