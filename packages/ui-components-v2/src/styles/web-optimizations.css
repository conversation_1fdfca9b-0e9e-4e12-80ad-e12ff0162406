/**
 * Web-optimized CSS extracted from ui-example-nativewind
 * These styles improve the web experience for our compound components
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  @apply overflow-hidden h-full;
}

::-webkit-scrollbar {
  @apply w-1.5 h-2;
}

::-webkit-scrollbar-corner,
::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-secondary-200 rounded-[20px] border-[3px] border-solid border-transparent;
}

/* Additional web optimizations */
.compound-component {
  @apply transition-all duration-200 ease-in-out;
}

.compound-component:focus-visible {
  @apply outline-2 outline-primary-500 outline-offset-2;
}
