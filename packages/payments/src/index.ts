// HVPPYPlug+ Payments Library - Main Export

// Core types
export * from './types';

// Payment providers
export * from './providers';

// Hooks
export * from './hooks';

// Components
export * from './components';

// Utilities
export * from './utils';

// Security
export * from './security';

// Validation
export * from './validation';

// Main payment manager
export { PaymentManager } from './manager';

// Provider factory
export { createPaymentProvider } from './providers';

// Re-export commonly used types for convenience
export type {
  PaymentProvider,
  PaymentRequest,
  PaymentResult,
  PaymentIntent,
  PaymentMethod,
  PaymentStatus,
  Currency,
  Amount,
  PolarConfig,
  PayFastConfig,
  MPesaConfig,
  VoucherConfig,
  PaymentError,
  ValidationResult,
  WebhookEvent
} from './types';

// Constants
export const SUPPORTED_CURRENCIES = ['USD', 'EUR', 'GBP', 'ZAR', 'CAD', 'AUD'] as const;
export const SUPPORTED_PAYMENT_METHODS = [
  'polar_card',
  'polar_checkout',
  'payfast_eft',
  'payfast_card',
  'mpesa',
  'voucher_1voucher',
  'voucher_ott',
  'cash_on_delivery'
] as const;

// Version
export const VERSION = '1.0.0';
