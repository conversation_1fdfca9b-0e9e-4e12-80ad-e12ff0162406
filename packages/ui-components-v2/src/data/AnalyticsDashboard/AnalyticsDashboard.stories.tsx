import type { Meta, StoryObj } from '@storybook/react-native'
import { AnalyticsDashboard } from './AnalyticsDashboard'

const meta: Meta<typeof AnalyticsDashboard> = {
  title: 'data/AnalyticsDashboard',
  component: AnalyticsDashboard,
  parameters: {
    docs: {
      description: {
        component: 'AnalyticsDashboard component description.',
      },
    },
  },
  argTypes: {
    disabled: {
      control: 'boolean',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
}

export const WithChildren: Story = {
  args: {
    children: 'Custom content',
  },
}
