#!/usr/bin/env node

/**
 * Batch Component Generation Script
 * Generates all essential compound components without interactive prompts
 */

const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')

const ROOT_DIR = path.resolve(__dirname, '..')
const SRC_DIR = path.join(ROOT_DIR, 'src')

const log = {
  info: (msg) => console.log(chalk.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(chalk.green(`✓ ${msg}`)),
  warning: (msg) => console.log(chalk.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(chalk.red(`✗ ${msg}`)),
  title: (msg) => console.log(chalk.bold.cyan(`\n🚀 ${msg}\n`)),
}

// Component definitions
const COMPONENTS_TO_GENERATE = [
  // Business Components
  {
    name: 'ProductCatalog',
    category: 'business',
    description: 'E-commerce product catalog with filtering, search, and grid/list views',
    features: ['product grid', 'search', 'filters', 'sorting', 'pagination', 'wishlist']
  },
  {
    name: 'OrderManagement',
    category: 'business', 
    description: 'Order management system with status tracking and actions',
    features: ['order list', 'status tracking', 'actions', 'filters', 'export']
  },
  {
    name: 'CustomerProfile',
    category: 'business',
    description: 'Customer profile management with edit capabilities',
    features: ['profile display', 'edit form', 'avatar upload', 'preferences']
  },
  {
    name: 'InventoryTracker',
    category: 'business',
    description: 'Inventory tracking with stock levels and alerts',
    features: ['stock levels', 'alerts', 'reorder points', 'history']
  },

  // Form Components
  {
    name: 'ContactForm',
    category: 'forms',
    description: 'Contact form with validation and submission handling',
    features: ['validation', 'submission', 'file upload', 'captcha']
  },
  {
    name: 'CheckoutForm',
    category: 'forms',
    description: 'Multi-step checkout form with payment integration',
    features: ['multi-step', 'payment', 'shipping', 'validation']
  },
  {
    name: 'ProfileEditor',
    category: 'forms',
    description: 'Profile editing form with image upload and validation',
    features: ['image upload', 'validation', 'auto-save', 'preview']
  },
  {
    name: 'SearchForm',
    category: 'forms',
    description: 'Advanced search form with filters and suggestions',
    features: ['autocomplete', 'filters', 'suggestions', 'history']
  },

  // Layout Components
  {
    name: 'AppShell',
    category: 'layouts',
    description: 'Application shell with navigation, header, and content areas',
    features: ['navigation', 'header', 'sidebar', 'responsive']
  },
  {
    name: 'DashboardLayout',
    category: 'layouts',
    description: 'Dashboard layout with widgets and responsive grid',
    features: ['widget grid', 'responsive', 'drag-drop', 'customizable']
  },
  {
    name: 'ContentLayout',
    category: 'layouts',
    description: 'Content layout with sidebar, breadcrumbs, and actions',
    features: ['sidebar', 'breadcrumbs', 'actions', 'responsive']
  },
  {
    name: 'AuthLayout',
    category: 'layouts',
    description: 'Authentication layout for login/register pages',
    features: ['centered', 'responsive', 'branding', 'social login']
  },

  // Data Components
  {
    name: 'DataGrid',
    category: 'data',
    description: 'Advanced data grid with sorting, filtering, and pagination',
    features: ['sorting', 'filtering', 'pagination', 'selection', 'export']
  },
  {
    name: 'ReportViewer',
    category: 'data',
    description: 'Report viewer with charts, tables, and export options',
    features: ['charts', 'tables', 'export', 'filters', 'drill-down']
  },
  {
    name: 'FileManager',
    category: 'data',
    description: 'File manager with upload, preview, and organization',
    features: ['upload', 'preview', 'folders', 'search', 'permissions']
  },

  // Interactive Components
  {
    name: 'MediaGallery',
    category: 'interactive',
    description: 'Media gallery with lightbox, thumbnails, and navigation',
    features: ['lightbox', 'thumbnails', 'navigation', 'zoom', 'slideshow']
  },
  {
    name: 'ChatInterface',
    category: 'interactive',
    description: 'Chat interface with messages, typing indicators, and file sharing',
    features: ['messages', 'typing', 'file sharing', 'emoji', 'reactions']
  },
  {
    name: 'NotificationCenter',
    category: 'interactive',
    description: 'Notification center with real-time updates and actions',
    features: ['real-time', 'actions', 'grouping', 'filters', 'mark as read']
  }
]

// Template for component generation
function generateComponentTemplate(component) {
  const { name, category, description, features } = component
  
  return `"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { View, ScrollView, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button } from '@gluestack-ui/themed'
import { useBreakpointValue } from '@/hooks/useBreakpointValue'

// Types
export interface ${name}Props {
  data?: any[]
  loading?: boolean
  onAction?: (action: string, data?: any) => void
  className?: string
  variant?: 'default' | 'compact' | 'detailed'
  ${features.map(feature => `${feature.replace(/\s+/g, '')}Enabled?: boolean`).join('\n  ')}
}

// Styles
const containerStyles = tva({
  base: "flex-1 bg-background-0",
  variants: {
    variant: {
      default: "p-4",
      compact: "p-2", 
      detailed: "p-6",
    },
  },
})

const headerStyles = tva({
  base: "flex-row items-center justify-between mb-4",
})

const contentStyles = tva({
  base: "flex-1",
  variants: {
    variant: {
      compact: "space-y-2",
      detailed: "space-y-6",
    },
  },
})

/**
 * ${description}
 * 
 * Features:
${features.map(feature => ` * - ${feature}`).join('\n')}
 */
export function ${name}({
  data = [],
  loading = false,
  onAction,
  className,
  variant = 'default',
  ...props
}: ${name}Props) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<Record<string, any>>({})

  // Responsive configuration
  const columns = useBreakpointValue({
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
  })

  // Filtered and processed data
  const processedData = useMemo(() => {
    let filtered = data

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(item => 
        JSON.stringify(item).toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(item => item[key] === value)
      }
    })

    return filtered
  }, [data, searchQuery, filters])

  // Handle actions
  const handleAction = (action: string, actionData?: any) => {
    onAction?.(action, actionData)
  }

  // Render header
  const renderHeader = () => (
    <View className={headerStyles()}>
      <View>
        <Text className="text-xl font-bold text-typography-900">
          ${name.replace(/([A-Z])/g, ' $1').trim()}
        </Text>
        <Text className="text-sm text-typography-600">
          {processedData.length} items
        </Text>
      </View>
      
      <View className="flex-row items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onPress={() => handleAction('refresh')}
        >
          <Text>Refresh</Text>
        </Button>
        
        <Button
          size="sm"
          onPress={() => handleAction('create')}
        >
          <Text>Add New</Text>
        </Button>
      </View>
    </View>
  )

  // Render content
  const renderContent = () => {
    if (loading) {
      return (
        <View className="flex-1 items-center justify-center">
          <Text className="text-typography-600">Loading...</Text>
        </View>
      )
    }

    if (processedData.length === 0) {
      return (
        <View className="flex-1 items-center justify-center">
          <Text className="text-typography-600 mb-4">No items found</Text>
          <Button onPress={() => handleAction('create')}>
            <Text>Create First Item</Text>
          </Button>
        </View>
      )
    }

    return (
      <ScrollView className="flex-1">
        <View className="flex-row flex-wrap -mx-2">
          {processedData.map((item, index) => (
            <View
              key={item.id || index}
              className="bg-background-0 border border-outline-200 rounded-lg p-4 mx-2 mb-4"
              style={{ width: \`\${100 / columns}%\` }}
            >
              <Text className="font-medium text-typography-900 mb-2">
                {item.title || item.name || \`Item \${index + 1}\`}
              </Text>
              <Text className="text-sm text-typography-600 mb-3">
                {item.description || 'No description available'}
              </Text>
              
              <View className="flex-row items-center justify-between">
                <Text className="text-xs text-typography-500">
                  {item.status || 'Active'}
                </Text>
                <Pressable
                  onPress={() => handleAction('view', item)}
                  className="px-3 py-1 bg-primary-100 rounded"
                >
                  <Text className="text-xs text-primary-700">View</Text>
                </Pressable>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    )
  }

  return (
    <View className={containerStyles({ variant, class: className })}>
      {renderHeader()}
      <View className={contentStyles({ variant })}>
        {renderContent()}
      </View>
    </View>
  )
}

// Export variants
export const ${name}Compact = (props: Omit<${name}Props, 'variant'>) => (
  <${name} {...props} variant="compact" />
)

export const ${name}Detailed = (props: Omit<${name}Props, 'variant'>) => (
  <${name} {...props} variant="detailed" />
)
`
}

// Generate all components
async function generateAllComponents() {
  log.title('Batch Generating All Compound Components')
  
  let successCount = 0
  
  for (const component of COMPONENTS_TO_GENERATE) {
    try {
      log.info(`Generating ${component.name}...`)
      
      const categoryDir = path.join(SRC_DIR, component.category)
      const componentDir = path.join(categoryDir, component.name)
      
      // Ensure directories exist
      await fs.ensureDir(componentDir)
      
      // Generate main component file
      const componentContent = generateComponentTemplate(component)
      await fs.writeFile(path.join(componentDir, `${component.name}.tsx`), componentContent)
      
      // Generate index file
      const indexContent = `export * from './${component.name}'`
      await fs.writeFile(path.join(componentDir, 'index.ts'), indexContent)
      
      // Generate types file
      const typesContent = `export interface ${component.name}Data {
  id: string
  title?: string
  name?: string
  description?: string
  status?: string
  [key: string]: any
}

export interface ${component.name}Config {
  ${component.features.map(feature => `${feature.replace(/\s+/g, '')}: boolean`).join('\n  ')}
}
`
      await fs.writeFile(path.join(componentDir, 'types.ts'), typesContent)
      
      // Update category index
      const categoryIndexPath = path.join(categoryDir, 'index.ts')
      let categoryIndex = ''
      if (await fs.pathExists(categoryIndexPath)) {
        categoryIndex = await fs.readFile(categoryIndexPath, 'utf8')
      }
      
      const exportLine = `export * from './${component.name}'`
      if (!categoryIndex.includes(exportLine)) {
        categoryIndex += `\n${exportLine}`
        await fs.writeFile(categoryIndexPath, categoryIndex)
      }
      
      log.success(`✓ Generated ${component.name}`)
      successCount++
      
    } catch (error) {
      log.error(`Failed to generate ${component.name}: ${error.message}`)
    }
  }
  
  // Update main index
  await updateMainIndex()
  
  log.title(`🎉 Batch Generation Complete!`)
  log.success(`Generated ${successCount}/${COMPONENTS_TO_GENERATE.length} components`)
  
  return successCount
}

async function updateMainIndex() {
  const mainIndexPath = path.join(SRC_DIR, 'index.ts')
  let mainIndex = await fs.readFile(mainIndexPath, 'utf8')
  
  const categories = ['business', 'forms', 'layouts', 'data', 'interactive']
  
  for (const category of categories) {
    const exportLine = `export * from './${category}'`
    if (!mainIndex.includes(exportLine)) {
      mainIndex += `\n${exportLine}`
    }
  }
  
  await fs.writeFile(mainIndexPath, mainIndex)
  log.success('✓ Updated main index.ts')
}

// Run the batch generation
if (require.main === module) {
  generateAllComponents().catch(error => {
    log.error(`Batch generation failed: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { generateAllComponents, COMPONENTS_TO_GENERATE }
