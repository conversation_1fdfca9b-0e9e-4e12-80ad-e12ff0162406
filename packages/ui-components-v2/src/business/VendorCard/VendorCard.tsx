"use client"

import React from 'react'
import { View, Pressable, Image } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { StarIcon, ClockIcon, MapPinIcon } from 'lucide-react-native'

// Types
export interface Vendor {
  id: string
  name: string
  description: string
  imageUrl?: string
  rating: number
  reviewCount: number
  deliveryTime: string
  deliveryFee: number
  categories: string[]
  distance: number
  isOpen: boolean
  location: {
    address: string
    coordinates: {
      lat: number
      lng: number
    }
  }
}

export interface VendorCardProps {
  vendor: Vendor
  onPress?: (vendor: Vendor) => void
  variant?: 'default' | 'compact' | 'featured'
  showDistance?: boolean
  showDeliveryFee?: boolean
  className?: string
}

// Styles
const cardStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg overflow-hidden mb-4",
  variants: {
    variant: {
      default: "p-4",
      compact: "p-3",
      featured: "p-0",
    },
    pressable: {
      true: "active:bg-background-50",
    },
    closed: {
      true: "opacity-60",
    },
  },
})

const imageStyles = tva({
  base: "rounded-lg",
  variants: {
    variant: {
      default: "w-16 h-16",
      compact: "w-12 h-12",
      featured: "w-full h-48",
    },
  },
})

const contentStyles = tva({
  base: "flex-1",
  variants: {
    variant: {
      default: "ml-4",
      compact: "ml-3",
      featured: "p-4",
    },
  },
})

const badgeStyles = tva({
  base: "px-2 py-1 rounded-full text-xs font-medium",
  variants: {
    status: {
      open: "bg-success-100 text-success-700",
      closed: "bg-error-100 text-error-700",
      busy: "bg-warning-100 text-warning-700",
    },
  },
})

/**
 * VendorCard component for displaying vendor information
 * 
 * Features:
 * - Multiple display variants (default, compact, featured)
 * - Vendor status indication
 * - Rating and review display
 * - Delivery information
 * - Category tags
 * - Distance and location info
 */
export function VendorCard({
  vendor,
  onPress,
  variant = 'default',
  showDistance = true,
  showDeliveryFee = true,
  className,
}: VendorCardProps) {
  const handlePress = () => {
    onPress?.(vendor)
  }

  const renderImage = () => (
    <Image
      source={{ uri: vendor.imageUrl || 'https://via.placeholder.com/150' }}
      className={imageStyles({ variant })}
      resizeMode="cover"
    />
  )

  const renderRating = () => (
    <View className="flex-row items-center">
      <StarIcon size={14} className="text-warning-500" />
      <Text className="ml-1 text-sm font-medium text-typography-900">
        {vendor.rating.toFixed(1)}
      </Text>
      <Text className="ml-1 text-sm text-typography-600">
        ({vendor.reviewCount})
      </Text>
    </View>
  )

  const renderDeliveryInfo = () => (
    <View className="flex-row items-center space-x-4">
      <View className="flex-row items-center">
        <ClockIcon size={14} className="text-typography-500" />
        <Text className="ml-1 text-sm text-typography-600">
          {vendor.deliveryTime}
        </Text>
      </View>
      
      {showDeliveryFee && (
        <Text className="text-sm text-typography-600">
          R{vendor.deliveryFee.toFixed(2)} delivery
        </Text>
      )}
      
      {showDistance && (
        <View className="flex-row items-center">
          <MapPinIcon size={14} className="text-typography-500" />
          <Text className="ml-1 text-sm text-typography-600">
            {vendor.distance.toFixed(1)}km
          </Text>
        </View>
      )}
    </View>
  )

  const renderCategories = () => (
    <View className="flex-row flex-wrap mt-2">
      {vendor.categories.slice(0, 3).map((category, index) => (
        <View
          key={index}
          className="bg-primary-100 px-2 py-1 rounded mr-2 mb-1"
        >
          <Text className="text-xs text-primary-700">{category}</Text>
        </View>
      ))}
      {vendor.categories.length > 3 && (
        <View className="bg-outline-100 px-2 py-1 rounded">
          <Text className="text-xs text-outline-700">
            +{vendor.categories.length - 3}
          </Text>
        </View>
      )}
    </View>
  )

  const renderStatus = () => (
    <View className={badgeStyles({ status: vendor.isOpen ? 'open' : 'closed' })}>
      <Text>{vendor.isOpen ? 'Open' : 'Closed'}</Text>
    </View>
  )

  const renderContent = () => (
    <View className={contentStyles({ variant })}>
      <View className="flex-row items-start justify-between mb-1">
        <Text className="text-lg font-semibold text-typography-900 flex-1">
          {vendor.name}
        </Text>
        {renderStatus()}
      </View>
      
      <Text className="text-sm text-typography-600 mb-2" numberOfLines={2}>
        {vendor.description}
      </Text>
      
      <View className="flex-row items-center justify-between mb-2">
        {renderRating()}
        {variant !== 'compact' && renderDeliveryInfo()}
      </View>
      
      {variant === 'compact' && renderDeliveryInfo()}
      
      {variant !== 'compact' && renderCategories()}
    </View>
  )

  if (variant === 'featured') {
    return (
      <Pressable
        onPress={handlePress}
        className={cardStyles({ 
          variant, 
          pressable: !!onPress, 
          closed: !vendor.isOpen,
          class: className 
        })}
      >
        <View className="relative">
          {renderImage()}
          <View className="absolute top-2 right-2">
            {renderStatus()}
          </View>
        </View>
        {renderContent()}
      </Pressable>
    )
  }

  return (
    <Pressable
      onPress={handlePress}
      className={cardStyles({ 
        variant, 
        pressable: !!onPress, 
        closed: !vendor.isOpen,
        class: className 
      })}
    >
      <View className="flex-row">
        {renderImage()}
        {renderContent()}
      </View>
    </Pressable>
  )
}

// Export variants
export const VendorCardCompact = (props: Omit<VendorCardProps, 'variant'>) => (
  <VendorCard {...props} variant="compact" />
)

export const VendorCardFeatured = (props: Omit<VendorCardProps, 'variant'>) => (
  <VendorCard {...props} variant="featured" />
)
