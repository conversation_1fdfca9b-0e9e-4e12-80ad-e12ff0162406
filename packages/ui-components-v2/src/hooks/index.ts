/**
 * Custom hooks for compound components
 */

import { useState, useCallback, useEffect, useMemo } from 'react'
import { Platform } from 'react-native'
import type { 
  SearchState, 
  SortState, 
  PaginationState,
  ComponentState,
  ListItem 
} from '../types'

// Platform detection hook
export function usePlatform() {
  return {
    isWeb: Platform.OS === 'web',
    isIOS: Platform.OS === 'ios',
    isAndroid: Platform.OS === 'android',
    isMobile: Platform.OS === 'ios' || Platform.OS === 'android',
    isNative: Platform.OS !== 'web',
  }
}

// Search and filter hook
export function useSearchAndFilter<T extends ListItem>(
  data: T[],
  searchKeys: string[] = [],
  initialFilters: Record<string, any> = {}
) {
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    filters: initialFilters,
  })

  const filteredData = useMemo(() => {
    let result = [...data]

    // Apply search
    if (searchState.query && searchKeys.length > 0) {
      result = result.filter(item =>
        searchKeys.some(key => {
          const value = item[key]
          return value?.toString().toLowerCase().includes(searchState.query.toLowerCase())
        })
      )
    }

    // Apply filters
    Object.entries(searchState.filters).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') return
      
      result = result.filter(item => {
        const itemValue = item[key]
        if (typeof value === 'boolean') {
          return itemValue === value
        }
        if (Array.isArray(value)) {
          return value.includes(itemValue)
        }
        return itemValue?.toString().toLowerCase().includes(value.toString().toLowerCase())
      })
    })

    return result
  }, [data, searchState, searchKeys])

  const setSearch = useCallback((query: string) => {
    setSearchState(prev => ({ ...prev, query }))
  }, [])

  const setFilters = useCallback((filters: Record<string, any>) => {
    setSearchState(prev => ({ ...prev, filters }))
  }, [])

  const clearSearch = useCallback(() => {
    setSearchState(prev => ({ ...prev, query: '' }))
  }, [])

  const clearFilters = useCallback(() => {
    setSearchState(prev => ({ ...prev, filters: {} }))
  }, [])

  const clearAll = useCallback(() => {
    setSearchState({ query: '', filters: {} })
  }, [])

  return {
    searchState,
    filteredData,
    setSearch,
    setFilters,
    clearSearch,
    clearFilters,
    clearAll,
  }
}

// Sorting hook
export function useSort<T extends ListItem>(
  data: T[],
  initialSort?: { column: string; direction: 'asc' | 'desc' }
) {
  const [sortState, setSortState] = useState<SortState>({
    column: initialSort?.column || null,
    direction: initialSort?.direction || 'asc',
  })

  const sortedData = useMemo(() => {
    if (!sortState.column) return data

    return [...data].sort((a, b) => {
      const aValue = a[sortState.column!]
      const bValue = b[sortState.column!]
      
      if (aValue < bValue) return sortState.direction === 'asc' ? -1 : 1
      if (aValue > bValue) return sortState.direction === 'asc' ? 1 : -1
      return 0
    })
  }, [data, sortState])

  const sort = useCallback((column: string, direction?: 'asc' | 'desc') => {
    setSortState(prev => ({
      column,
      direction: direction || (prev.column === column && prev.direction === 'asc' ? 'desc' : 'asc'),
    }))
  }, [])

  const clearSort = useCallback(() => {
    setSortState({ column: null, direction: 'asc' })
  }, [])

  return {
    sortState,
    sortedData,
    sort,
    clearSort,
  }
}

// Pagination hook
export function usePagination<T>(
  data: T[],
  initialPageSize: number = 10
) {
  const [paginationState, setPaginationState] = useState<PaginationState>({
    currentPage: 1,
    pageSize: initialPageSize,
    totalItems: data.length,
    hasMore: data.length > initialPageSize,
  })

  useEffect(() => {
    setPaginationState(prev => ({
      ...prev,
      totalItems: data.length,
      hasMore: prev.currentPage * prev.pageSize < data.length,
    }))
  }, [data.length])

  const paginatedData = useMemo(() => {
    const startIndex = (paginationState.currentPage - 1) * paginationState.pageSize
    return data.slice(startIndex, startIndex + paginationState.pageSize)
  }, [data, paginationState.currentPage, paginationState.pageSize])

  const goToPage = useCallback((page: number) => {
    setPaginationState(prev => ({
      ...prev,
      currentPage: Math.max(1, Math.min(page, Math.ceil(prev.totalItems / prev.pageSize))),
    }))
  }, [])

  const nextPage = useCallback(() => {
    setPaginationState(prev => ({
      ...prev,
      currentPage: prev.hasMore ? prev.currentPage + 1 : prev.currentPage,
    }))
  }, [])

  const previousPage = useCallback(() => {
    setPaginationState(prev => ({
      ...prev,
      currentPage: Math.max(1, prev.currentPage - 1),
    }))
  }, [])

  const setPageSize = useCallback((pageSize: number) => {
    setPaginationState(prev => ({
      ...prev,
      pageSize,
      currentPage: 1,
      hasMore: pageSize < prev.totalItems,
    }))
  }, [])

  return {
    paginationState,
    paginatedData,
    goToPage,
    nextPage,
    previousPage,
    setPageSize,
  }
}

// Component state hook
export function useComponentState(initialState: ComponentState = 'idle') {
  const [state, setState] = useState<ComponentState>(initialState)

  const setLoading = useCallback(() => setState('loading'), [])
  const setSuccess = useCallback(() => setState('success'), [])
  const setError = useCallback(() => setState('error'), [])
  const setIdle = useCallback(() => setState('idle'), [])

  return {
    state,
    isLoading: state === 'loading',
    isSuccess: state === 'success',
    isError: state === 'error',
    isIdle: state === 'idle',
    setLoading,
    setSuccess,
    setError,
    setIdle,
    setState,
  }
}

// Debounced value hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Previous value hook
export function usePrevious<T>(value: T): T | undefined {
  const [current, setCurrent] = useState<T>(value)
  const [previous, setPrevious] = useState<T | undefined>(undefined)

  if (value !== current) {
    setPrevious(current)
    setCurrent(value)
  }

  return previous
}

// Local storage hook (web only)
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  const { isWeb } = usePlatform()

  const [storedValue, setStoredValue] = useState<T>(() => {
    if (!isWeb) return initialValue

    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      
      if (isWeb) {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, storedValue, isWeb])

  return [storedValue, setValue]
}

// Combined data management hook
export function useDataManagement<T extends ListItem>(
  data: T[],
  options: {
    searchKeys?: string[]
    initialSort?: { column: string; direction: 'asc' | 'desc' }
    pageSize?: number
    initialFilters?: Record<string, any>
  } = {}
) {
  const {
    searchKeys = [],
    initialSort,
    pageSize = 10,
    initialFilters = {},
  } = options

  const { searchState, filteredData, setSearch, setFilters, clearAll } = useSearchAndFilter(
    data,
    searchKeys,
    initialFilters
  )

  const { sortState, sortedData, sort, clearSort } = useSort(filteredData, initialSort)

  const { paginationState, paginatedData, goToPage, nextPage, previousPage, setPageSize } = 
    usePagination(sortedData, pageSize)

  return {
    // Data
    processedData: paginatedData,
    totalItems: sortedData.length,
    
    // Search
    searchState,
    setSearch,
    setFilters,
    
    // Sort
    sortState,
    sort,
    
    // Pagination
    paginationState,
    goToPage,
    nextPage,
    previousPage,
    setPageSize,
    
    // Reset
    clearAll: () => {
      clearAll()
      clearSort()
      goToPage(1)
    },
  }
}

// Responsive breakpoint utilities
export * from './useBreakpointValue'

// Expo plugin hooks
export * from './useLocation'
export * from './useNotifications'
export * from './useCamera'
