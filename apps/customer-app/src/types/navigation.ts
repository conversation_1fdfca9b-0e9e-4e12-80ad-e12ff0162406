import type { NavigatorScreenParams } from '@react-navigation/native'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs'

// Root Stack Navigator Types
export type RootStackParamList = {
  // Auth Flow
  Onboarding: undefined
  Login: undefined
  Register: undefined
  ForgotPassword: undefined
  OTPVerification: { email: string; type: 'register' | 'forgot-password' }
  
  // Main App
  MainTabs: NavigatorScreenParams<MainTabParamList>
  
  // Modal Screens
  ServiceDetail: { serviceId: string }
  ProviderProfile: { providerId: string }
  Booking: { serviceId: string; providerId: string }
  OrderTracking: { orderId: string }
  Chat: { orderId: string; providerId: string }
  Payment: { orderId: string }
  LocationPicker: { currentLocation?: { latitude: number; longitude: number; address: string } }
  NotificationDetail: { notificationId: string }
  ReviewOrder: { orderId: string }
  Help: undefined
  Support: undefined
}

// Main Tab Navigator Types
export type MainTabParamList = {
  Home: undefined
  Search: undefined
  Orders: undefined
  Favorites: undefined
  Profile: undefined
}

// Home Stack Navigator Types
export type HomeStackParamList = {
  HomeMain: undefined
  ServiceCategory: { categoryId: string; categoryName: string }
  ServiceList: { categoryId?: string; searchQuery?: string; location?: { latitude: number; longitude: number } }
}

// Search Stack Navigator Types
export type SearchStackParamList = {
  SearchMain: undefined
  SearchResults: { query: string; filters?: any }
  FilterOptions: { currentFilters?: any }
}

// Orders Stack Navigator Types
export type OrdersStackParamList = {
  OrdersMain: undefined
  OrderDetail: { orderId: string }
  OrderHistory: undefined
  ActiveOrders: undefined
}

// Profile Stack Navigator Types
export type ProfileStackParamList = {
  ProfileMain: undefined
  EditProfile: undefined
  Addresses: undefined
  AddAddress: { address?: any }
  PaymentMethods: undefined
  AddPaymentMethod: undefined
  Settings: undefined
  Notifications: undefined
  HelpCenter: undefined
  About: undefined
}

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<
  RootStackParamList,
  T
>

export type MainTabScreenProps<T extends keyof MainTabParamList> = BottomTabScreenProps<
  MainTabParamList,
  T
>

export type HomeStackScreenProps<T extends keyof HomeStackParamList> = NativeStackScreenProps<
  HomeStackParamList,
  T
>

export type SearchStackScreenProps<T extends keyof SearchStackParamList> = NativeStackScreenProps<
  SearchStackParamList,
  T
>

export type OrdersStackScreenProps<T extends keyof OrdersStackParamList> = NativeStackScreenProps<
  OrdersStackParamList,
  T
>

export type ProfileStackScreenProps<T extends keyof ProfileStackParamList> = NativeStackScreenProps<
  ProfileStackParamList,
  T
>

// Navigation Props for useNavigation hook
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
