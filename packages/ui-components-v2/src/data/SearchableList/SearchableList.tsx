"use client"

import React, { useState, useCallback, useMemo } from 'react'
import { FlatList, View, Pressable, RefreshControl } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { SearchIcon, FilterIcon, XIcon } from 'lucide-react-native'

// Types
export interface SearchableListItem {
  id: string | number
  [key: string]: any
}

export interface SearchFilter {
  key: string
  label: string
  type: 'text' | 'select' | 'date' | 'boolean'
  options?: { label: string; value: any }[]
}

export interface SearchableListProps<T extends SearchableListItem> {
  data: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  searchKeys?: string[]
  filters?: SearchFilter[]
  loading?: boolean
  refreshing?: boolean
  hasMore?: boolean
  onLoadMore?: () => void
  onRefresh?: () => void
  onSearch?: (query: string) => void
  onFilter?: (filters: Record<string, any>) => void
  placeholder?: string
  emptyMessage?: string
  loadingMessage?: string
  className?: string
  itemSeparator?: boolean
  keyExtractor?: (item: T, index: number) => string
}

// Styles
const containerStyles = tva({
  base: "flex-1 bg-background-0",
})

const searchBarStyles = tva({
  base: "mx-4 mt-4 mb-2 flex-row items-center bg-background-50 rounded-lg px-3 py-2 border border-outline-200",
  variants: {
    focused: {
      true: "border-primary-500",
    },
  },
})

const filterChipStyles = tva({
  base: "flex-row items-center bg-primary-100 rounded-full px-3 py-1 mr-2 mb-2",
})

const listStyles = tva({
  base: "flex-1",
})

const separatorStyles = tva({
  base: "h-px bg-outline-100 mx-4",
})

const emptyStateStyles = tva({
  base: "flex-1 items-center justify-center p-8",
})

const loadingFooterStyles = tva({
  base: "p-4 items-center",
})

export function SearchableList<T extends SearchableListItem>({
  data,
  renderItem,
  searchKeys = [],
  filters = [],
  loading = false,
  refreshing = false,
  hasMore = false,
  onLoadMore,
  onRefresh,
  onSearch,
  onFilter,
  placeholder = "Search...",
  emptyMessage = "No items found",
  loadingMessage = "Loading...",
  className,
  itemSeparator = true,
  keyExtractor,
}: SearchableListProps<T>) {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchFocused, setSearchFocused] = useState(false)
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({})
  const [showFilters, setShowFilters] = useState(false)

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
    onSearch?.(query)
  }, [onSearch])

  // Handle filter change
  const handleFilterChange = useCallback((key: string, value: any) => {
    const newFilters = { ...activeFilters }
    if (value === null || value === undefined || value === '') {
      delete newFilters[key]
    } else {
      newFilters[key] = value
    }
    setActiveFilters(newFilters)
    onFilter?.(newFilters)
  }, [activeFilters, onFilter])

  // Remove filter
  const removeFilter = useCallback((key: string) => {
    handleFilterChange(key, null)
  }, [handleFilterChange])

  // Filter data locally if no external filtering
  const filteredData = useMemo(() => {
    if (onSearch || onFilter) return data // External filtering

    let result = [...data]

    // Apply search
    if (searchQuery && searchKeys.length > 0) {
      result = result.filter(item =>
        searchKeys.some(key => {
          const value = item[key]
          return value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        })
      )
    }

    // Apply filters
    Object.entries(activeFilters).forEach(([key, value]) => {
      result = result.filter(item => {
        const itemValue = item[key]
        if (typeof value === 'boolean') {
          return itemValue === value
        }
        if (Array.isArray(value)) {
          return value.includes(itemValue)
        }
        return itemValue?.toString().toLowerCase().includes(value.toString().toLowerCase())
      })
    })

    return result
  }, [data, searchQuery, searchKeys, activeFilters, onSearch, onFilter])

  // Render search bar
  const renderSearchBar = () => (
    <View className={searchBarStyles({ focused: searchFocused })}>
      <SearchIcon size={20} className="text-typography-400 mr-2" />
      <Text
        className="flex-1 text-typography-900"
        placeholder={placeholder}
        value={searchQuery}
        onChangeText={handleSearch}
        onFocus={() => setSearchFocused(true)}
        onBlur={() => setSearchFocused(false)}
      />
      {filters.length > 0 && (
        <Pressable
          onPress={() => setShowFilters(!showFilters)}
          className="ml-2 p-1"
        >
          <FilterIcon 
            size={20} 
            className={Object.keys(activeFilters).length > 0 ? "text-primary-500" : "text-typography-400"} 
          />
        </Pressable>
      )}
    </View>
  )

  // Render active filter chips
  const renderFilterChips = () => {
    if (Object.keys(activeFilters).length === 0) return null

    return (
      <View className="flex-row flex-wrap px-4 mb-2">
        {Object.entries(activeFilters).map(([key, value]) => {
          const filter = filters.find(f => f.key === key)
          if (!filter) return null

          return (
            <View key={key} className={filterChipStyles()}>
              <Text className="text-primary-700 text-sm mr-2">
                {filter.label}: {value.toString()}
              </Text>
              <Pressable onPress={() => removeFilter(key)}>
                <XIcon size={16} className="text-primary-600" />
              </Pressable>
            </View>
          )
        })}
      </View>
    )
  }

  // Render filter panel
  const renderFilterPanel = () => {
    if (!showFilters) return null

    return (
      <View className="bg-background-50 border-t border-outline-200 p-4">
        {filters.map(filter => (
          <View key={filter.key} className="mb-4">
            <Text className="text-typography-700 font-medium mb-2">
              {filter.label}
            </Text>
            
            {filter.type === 'select' && filter.options && (
              <View className="flex-row flex-wrap">
                {filter.options.map(option => (
                  <Pressable
                    key={option.value}
                    className={`px-3 py-2 mr-2 mb-2 rounded-lg border ${
                      activeFilters[filter.key] === option.value
                        ? 'bg-primary-500 border-primary-500'
                        : 'bg-background-0 border-outline-200'
                    }`}
                    onPress={() => handleFilterChange(filter.key, 
                      activeFilters[filter.key] === option.value ? null : option.value
                    )}
                  >
                    <Text className={
                      activeFilters[filter.key] === option.value
                        ? 'text-white'
                        : 'text-typography-700'
                    }>
                      {option.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            )}
          </View>
        ))}
      </View>
    )
  }

  // Render item separator
  const renderSeparator = () => itemSeparator ? <View className={separatorStyles()} /> : null

  // Render empty state
  const renderEmptyState = () => (
    <View className={emptyStateStyles()}>
      <Text className="text-typography-500 text-center text-lg">
        {loading ? loadingMessage : emptyMessage}
      </Text>
    </View>
  )

  // Render loading footer
  const renderLoadingFooter = () => {
    if (!hasMore || !loading) return null
    
    return (
      <View className={loadingFooterStyles()}>
        <Text className="text-typography-500">Loading more...</Text>
      </View>
    )
  }

  // Default key extractor
  const defaultKeyExtractor = useCallback((item: T, index: number) => 
    keyExtractor ? keyExtractor(item, index) : item.id.toString()
  , [keyExtractor])

  return (
    <View className={containerStyles({ class: className })}>
      {renderSearchBar()}
      {renderFilterChips()}
      {renderFilterPanel()}
      
      <FlatList
        className={listStyles()}
        data={filteredData}
        renderItem={({ item, index }) => renderItem(item, index)}
        keyExtractor={defaultKeyExtractor}
        ItemSeparatorComponent={renderSeparator}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderLoadingFooter}
        refreshControl={
          onRefresh ? (
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          ) : undefined
        }
        onEndReached={hasMore ? onLoadMore : undefined}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />
    </View>
  )
}
