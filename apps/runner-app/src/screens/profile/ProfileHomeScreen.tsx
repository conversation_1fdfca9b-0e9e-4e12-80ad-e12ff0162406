import React from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { 
  User, 
  Settings, 
  FileText, 
  CreditCard, 
  HelpCircle, 
  LogOut,
  ChevronRight,
  Star,
  Truck,
  Shield
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { useAuthStore } from '../../stores/authStore'

export function ProfileHomeScreen() {
  const { theme } = useTheme()
  const { runner, logout } = useAuthStore()

  const profileStats = [
    {
      label: 'Total Deliveries',
      value: runner?.totalDeliveries || 0,
      icon: Truck,
      color: theme.colors.primary[600],
    },
    {
      label: 'Rating',
      value: runner?.rating || 0,
      icon: Star,
      color: theme.colors.warning[600],
    },
    {
      label: 'Status',
      value: runner?.status === 'approved' ? 'Verified' : 'Pending',
      icon: Shield,
      color: runner?.status === 'approved' ? theme.colors.success[600] : theme.colors.warning[600],
    },
  ]

  const menuSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'edit-profile',
          title: 'Edit Profile',
          subtitle: 'Update your personal information',
          icon: User,
          onPress: () => console.log('Edit Profile'),
        },
        {
          id: 'documents',
          title: 'Documents',
          subtitle: 'Manage your verification documents',
          icon: FileText,
          onPress: () => console.log('Documents'),
        },
        {
          id: 'vehicle-info',
          title: 'Vehicle Information',
          subtitle: 'Update your vehicle details',
          icon: Truck,
          onPress: () => console.log('Vehicle Info'),
        },
        {
          id: 'bank-details',
          title: 'Bank Details',
          subtitle: 'Manage your payout information',
          icon: CreditCard,
          onPress: () => console.log('Bank Details'),
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help Center',
          subtitle: 'Get help and support',
          icon: HelpCircle,
          onPress: () => console.log('Help'),
        },
        {
          id: 'settings',
          title: 'Settings',
          subtitle: 'App preferences and notifications',
          icon: Settings,
          onPress: () => console.log('Settings'),
        },
      ],
    },
  ]

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={[styles.profileHeader, { backgroundColor: theme.colors.white }]}>
          <View style={styles.profileInfo}>
            <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary[100] }]}>
              {runner?.avatar ? (
                <Image source={{ uri: runner.avatar }} style={styles.avatar} />
              ) : (
                <User size={40} color={theme.colors.primary[600]} />
              )}
            </View>
            
            <View style={styles.profileDetails}>
              <Text style={[styles.profileName, { color: theme.colors.gray[900] }]}>
                {runner?.name || 'Runner Name'}
              </Text>
              <Text style={[styles.profileEmail, { color: theme.colors.gray[600] }]}>
                {runner?.email || '<EMAIL>'}
              </Text>
              <Text style={[styles.profilePhone, { color: theme.colors.gray[600] }]}>
                {runner?.phone || '+27 XX XXX XXXX'}
              </Text>
            </View>
          </View>

          {/* Profile Stats */}
          <View style={styles.statsContainer}>
            {profileStats.map((stat, index) => (
              <View key={index} style={styles.statItem}>
                <View style={[styles.statIcon, { backgroundColor: `${stat.color}20` }]}>
                  <stat.icon size={20} color={stat.color} />
                </View>
                <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                  {typeof stat.value === 'number' && stat.label === 'Rating' 
                    ? stat.value.toFixed(1) 
                    : stat.value}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                  {stat.label}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Menu Sections */}
        {menuSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.menuSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              {section.title}
            </Text>
            
            <View style={[styles.menuCard, { backgroundColor: theme.colors.white }]}>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.menuItem,
                    itemIndex < section.items.length - 1 && {
                      borderBottomWidth: 1,
                      borderBottomColor: theme.colors.gray[100],
                    }
                  ]}
                  onPress={item.onPress}
                  activeOpacity={0.7}
                >
                  <View style={[styles.menuIcon, { backgroundColor: theme.colors.gray[100] }]}>
                    <item.icon size={20} color={theme.colors.gray[600]} />
                  </View>
                  
                  <View style={styles.menuContent}>
                    <Text style={[styles.menuTitle, { color: theme.colors.gray[900] }]}>
                      {item.title}
                    </Text>
                    <Text style={[styles.menuSubtitle, { color: theme.colors.gray[600] }]}>
                      {item.subtitle}
                    </Text>
                  </View>
                  
                  <ChevronRight size={20} color={theme.colors.gray[400]} />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}

        {/* Logout Button */}
        <View style={styles.logoutSection}>
          <TouchableOpacity
            style={[styles.logoutButton, { backgroundColor: theme.colors.white }]}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <View style={[styles.logoutIcon, { backgroundColor: theme.colors.error[100] }]}>
              <LogOut size={20} color={theme.colors.error[600]} />
            </View>
            
            <Text style={[styles.logoutText, { color: theme.colors.error[600] }]}>
              Sign Out
            </Text>
            
            <ChevronRight size={20} color={theme.colors.error[400]} />
          </TouchableOpacity>
        </View>

        {/* App Version */}
        <View style={styles.versionContainer}>
          <Text style={[styles.versionText, { color: theme.colors.gray[500] }]}>
            HVPPYPlug+ Runner v1.0.0
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  profileHeader: {
    padding: 20,
    marginBottom: 16,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 16,
    marginBottom: 2,
  },
  profilePhone: {
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  menuSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  menuCard: {
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  menuSubtitle: {
    fontSize: 14,
  },
  logoutSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  logoutIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  logoutText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
  },
  versionContainer: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  versionText: {
    fontSize: 14,
  },
})
