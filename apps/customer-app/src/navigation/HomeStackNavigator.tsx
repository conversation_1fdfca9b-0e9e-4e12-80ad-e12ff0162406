import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { HomeStackParamList } from '../types/navigation'

// Import Screens
import { HomeScreen } from '../screens/home/<USER>'
import { ServiceCategoryScreen } from '../screens/services/ServiceCategoryScreen'
import { ServiceListScreen } from '../screens/services/ServiceListScreen'

const Stack = createNativeStackNavigator<HomeStackParamList>()

export function HomeStackNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="HomeMain"
        component={HomeScreen}
      />
      <Stack.Screen
        name="ServiceCategory"
        component={ServiceCategoryScreen}
        options={{
          headerShown: true,
          title: 'Services',
        }}
      />
      <Stack.Screen
        name="ServiceList"
        component={ServiceListScreen}
        options={{
          headerShown: true,
          title: 'Services',
        }}
      />
    </Stack.Navigator>
  )
}
