# pnpm configuration for Expo monorepo compatibility
# This ensures packages are hoisted to work with Metro bundler
node-linker=hoisted

# Enable shameful hoisting for better compatibility with React Native
shamefully-hoist=true

# Auto install peer dependencies
auto-install-peers=true

# Strict peer dependencies (can be disabled if causing issues)
strict-peer-dependencies=false

# EAS Build optimizations
prefer-workspace-packages=true
link-workspace-packages=true

# Cache settings for faster builds
store-dir=~/.pnpm-store
cache-dir=~/.pnpm-cache
