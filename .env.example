# API Configuration
NODE_ENV=development
PORT=3001
API_URL=http://localhost:3001

# Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=hvppyplug
APPWRITE_API_KEY=your_appwrite_api_key_here
APPWRITE_DATABASE_ID=hvppyplug-main

# Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayFast Configuration
PAYFAST_MERCHANT_ID=your_payfast_merchant_id
PAYFAST_MERCHANT_KEY=your_payfast_merchant_key
PAYFAST_PASSPHRASE=your_payfast_passphrase
PAYFAST_SANDBOX=true

# 1Voucher Configuration
ONE_VOUCHER_API_KEY=your_one_voucher_api_key
ONE_VOUCHER_ENDPOINT=https://api.1voucher.co.za

# OTT Vouchers Configuration
OTT_VOUCHER_API_KEY=your_ott_voucher_api_key
OTT_VOUCHER_ENDPOINT=https://api.ottvouchers.com

# Google Maps
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Push Notifications
EXPO_ACCESS_TOKEN=your_expo_access_token

# Expo Public Variables (for frontend)
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
EXPO_PUBLIC_APPWRITE_PROJECT_ID=hvppyplug
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
EXPO_PUBLIC_PAYFAST_MERCHANT_ID=your_payfast_merchant_id
EXPO_PUBLIC_PAYFAST_MERCHANT_KEY=your_payfast_merchant_key
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
