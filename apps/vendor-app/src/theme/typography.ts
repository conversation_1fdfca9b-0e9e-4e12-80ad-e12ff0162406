export const typography = {
  // Display Text (Large headings)
  display: {
    large: {
      fontSize: 32,
      lineHeight: 40,
      fontWeight: '700' as const,
      letterSpacing: -0.5,
    },
    medium: {
      fontSize: 28,
      lineHeight: 36,
      fontWeight: '700' as const,
      letterSpacing: -0.25,
    },
    small: {
      fontSize: 24,
      lineHeight: 32,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
  },

  // Headline Text (Section headings)
  headline: {
    large: {
      fontSize: 22,
      lineHeight: 28,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 20,
      lineHeight: 26,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 18,
      lineHeight: 24,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
  },

  // Title Text (Card titles, etc.)
  title: {
    large: {
      fontSize: 16,
      lineHeight: 22,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 12,
      lineHeight: 18,
      fontWeight: '600' as const,
      letterSpacing: 0,
    },
  },

  // Body Text (Main content)
  body: {
    large: {
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 12,
      lineHeight: 18,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
  },

  // Label Text (Form labels, buttons)
  label: {
    large: {
      fontSize: 16,
      lineHeight: 20,
      fontWeight: '500' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 14,
      lineHeight: 18,
      fontWeight: '500' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 12,
      lineHeight: 16,
      fontWeight: '500' as const,
      letterSpacing: 0,
    },
  },

  // Caption Text (Small descriptive text)
  caption: {
    large: {
      fontSize: 12,
      lineHeight: 16,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    medium: {
      fontSize: 11,
      lineHeight: 14,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
    small: {
      fontSize: 10,
      lineHeight: 12,
      fontWeight: '400' as const,
      letterSpacing: 0,
    },
  },

  // Overline Text (Small caps text)
  overline: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: '600' as const,
    letterSpacing: 1,
    textTransform: 'uppercase' as const,
  },
} as const
