"use client"

import React, { useState, useCallback } from 'react'
import { View, Pressable, ScrollView, SafeAreaView } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { 
  MenuIcon, 
  XIcon, 
  ChevronDownIcon,
  ChevronRightIcon,
  HomeIcon,
  UserIcon,
  SettingsIcon 
} from 'lucide-react-native'
import { usePlatform } from '../../hooks'

// Types
export interface NavigationItem {
  id: string
  label: string
  icon?: React.ComponentType<any>
  href?: string
  onPress?: () => void
  badge?: string | number
  children?: NavigationItem[]
  disabled?: boolean
}

export interface NavigationLayoutProps {
  children: React.ReactNode
  navigation: NavigationItem[]
  activeItem?: string
  onNavigate?: (item: NavigationItem) => void
  header?: {
    title?: string
    subtitle?: string
    actions?: React.ReactNode
    logo?: React.ReactNode
  }
  sidebar?: {
    width?: number
    collapsible?: boolean
    defaultCollapsed?: boolean
    position?: 'left' | 'right'
  }
  footer?: React.ReactNode
  className?: string
  variant?: 'sidebar' | 'tabs' | 'drawer'
}

// Styles
const layoutStyles = tva({
  base: "flex-1 bg-background-0",
})

const headerStyles = tva({
  base: "bg-background-0 border-b border-outline-200 px-4 py-3 flex-row items-center justify-between",
  variants: {
    variant: {
      sidebar: "lg:pl-6",
      tabs: "",
      drawer: "",
    },
  },
})

const sidebarStyles = tva({
  base: "bg-background-50 border-r border-outline-200",
  variants: {
    collapsed: {
      true: "w-16",
      false: "w-64",
    },
    position: {
      left: "",
      right: "border-l border-r-0",
    },
    mobile: {
      true: "absolute top-0 bottom-0 z-50 shadow-lg",
      false: "",
    },
  },
})

const contentStyles = tva({
  base: "flex-1",
  variants: {
    variant: {
      sidebar: "lg:ml-64",
      tabs: "",
      drawer: "",
    },
    sidebarCollapsed: {
      true: "lg:ml-16",
    },
  },
})

const navItemStyles = tva({
  base: "flex-row items-center px-4 py-3 mx-2 rounded-lg",
  variants: {
    active: {
      true: "bg-primary-100 border-l-4 border-primary-500",
      false: "hover:bg-background-100",
    },
    disabled: {
      true: "opacity-50",
    },
    collapsed: {
      true: "justify-center px-2",
    },
  },
})

const navLabelStyles = tva({
  base: "text-typography-700 font-medium ml-3",
  variants: {
    active: {
      true: "text-primary-700",
    },
    collapsed: {
      true: "hidden",
    },
  },
})

const badgeStyles = tva({
  base: "bg-error-500 text-white text-xs rounded-full px-2 py-1 ml-auto min-w-5 text-center",
})

const overlayStyles = tva({
  base: "absolute inset-0 bg-black/50 z-40",
})

export function NavigationLayout({
  children,
  navigation,
  activeItem,
  onNavigate,
  header,
  sidebar = { width: 256, collapsible: true, defaultCollapsed: false, position: 'left' },
  footer,
  className,
  variant = 'sidebar',
}: NavigationLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(!sidebar.defaultCollapsed)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const { isMobile } = usePlatform()

  // Toggle sidebar
  const toggleSidebar = useCallback(() => {
    setSidebarOpen(prev => !prev)
  }, [])

  // Toggle expanded item
  const toggleExpanded = useCallback((itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }, [])

  // Handle navigation
  const handleNavigate = useCallback((item: NavigationItem) => {
    if (item.disabled) return
    
    if (item.children && item.children.length > 0) {
      toggleExpanded(item.id)
    } else {
      onNavigate?.(item)
      item.onPress?.()
      
      // Close sidebar on mobile after navigation
      if (isMobile) {
        setSidebarOpen(false)
      }
    }
  }, [onNavigate, toggleExpanded, isMobile])

  // Render navigation item
  const renderNavItem = (item: NavigationItem, level: number = 0) => {
    const isActive = activeItem === item.id
    const isExpanded = expandedItems.has(item.id)
    const hasChildren = item.children && item.children.length > 0
    const IconComponent = item.icon || HomeIcon

    return (
      <View key={item.id}>
        <Pressable
          className={navItemStyles({ 
            active: isActive, 
            disabled: item.disabled,
            collapsed: !sidebarOpen && sidebar.collapsible
          })}
          onPress={() => handleNavigate(item)}
          style={{ paddingLeft: 16 + (level * 16) }}
        >
          <IconComponent 
            size={20} 
            className={isActive ? "text-primary-600" : "text-typography-600"} 
          />
          
          <Text className={navLabelStyles({ 
            active: isActive,
            collapsed: !sidebarOpen && sidebar.collapsible
          })}>
            {item.label}
          </Text>

          {item.badge && (
            <View className={badgeStyles()}>
              <Text className="text-white text-xs">
                {item.badge}
              </Text>
            </View>
          )}

          {hasChildren && sidebarOpen && (
            <View className="ml-auto">
              {isExpanded ? (
                <ChevronDownIcon size={16} className="text-typography-500" />
              ) : (
                <ChevronRightIcon size={16} className="text-typography-500" />
              )}
            </View>
          )}
        </Pressable>

        {hasChildren && isExpanded && sidebarOpen && (
          <View className="ml-4">
            {item.children!.map(child => renderNavItem(child, level + 1))}
          </View>
        )}
      </View>
    )
  }

  // Render header
  const renderHeader = () => {
    if (!header) return null

    return (
      <View className={headerStyles({ variant })}>
        <View className="flex-row items-center flex-1">
          {(variant === 'sidebar' || variant === 'drawer') && (
            <Pressable onPress={toggleSidebar} className="mr-4">
              <MenuIcon size={24} className="text-typography-700" />
            </Pressable>
          )}

          {header.logo && (
            <View className="mr-4">
              {header.logo}
            </View>
          )}

          <View className="flex-1">
            {header.title && (
              <Text className="text-lg font-semibold text-typography-900">
                {header.title}
              </Text>
            )}
            {header.subtitle && (
              <Text className="text-sm text-typography-600">
                {header.subtitle}
              </Text>
            )}
          </View>
        </View>

        {header.actions && (
          <View className="flex-row items-center">
            {header.actions}
          </View>
        )}
      </View>
    )
  }

  // Render sidebar
  const renderSidebar = () => {
    if (variant === 'tabs') return null

    const isCollapsed = !sidebarOpen && sidebar.collapsible
    const isMobileDrawer = isMobile && variant === 'drawer'

    return (
      <>
        {isMobileDrawer && sidebarOpen && (
          <Pressable 
            className={overlayStyles()} 
            onPress={() => setSidebarOpen(false)} 
          />
        )}
        
        <View className={sidebarStyles({ 
          collapsed: isCollapsed,
          position: sidebar.position,
          mobile: isMobileDrawer
        })}>
          <SafeAreaView className="flex-1">
            <ScrollView className="flex-1 py-4">
              {navigation.map(item => renderNavItem(item))}
            </ScrollView>
          </SafeAreaView>
        </View>
      </>
    )
  }

  // Render tabs (mobile)
  const renderTabs = () => {
    if (variant !== 'tabs') return null

    return (
      <View className="bg-background-0 border-t border-outline-200 flex-row">
        {navigation.slice(0, 5).map(item => {
          const isActive = activeItem === item.id
          const IconComponent = item.icon || HomeIcon

          return (
            <Pressable
              key={item.id}
              className="flex-1 items-center py-2"
              onPress={() => handleNavigate(item)}
            >
              <IconComponent 
                size={24} 
                className={isActive ? "text-primary-600" : "text-typography-500"} 
              />
              <Text className={`text-xs mt-1 ${
                isActive ? "text-primary-600 font-medium" : "text-typography-500"
              }`}>
                {item.label}
              </Text>
              {item.badge && (
                <View className="absolute -top-1 -right-1 bg-error-500 rounded-full w-4 h-4 items-center justify-center">
                  <Text className="text-white text-xs">
                    {typeof item.badge === 'number' && item.badge > 9 ? '9+' : item.badge}
                  </Text>
                </View>
              )}
            </Pressable>
          )
        })}
      </View>
    )
  }

  return (
    <View className={layoutStyles({ class: className })}>
      {renderHeader()}
      
      <View className="flex-1 flex-row">
        {renderSidebar()}
        
        <View className={contentStyles({ 
          variant,
          sidebarCollapsed: !sidebarOpen && sidebar.collapsible
        })}>
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            {children}
          </ScrollView>
          
          {footer && (
            <View className="border-t border-outline-200 p-4">
              {footer}
            </View>
          )}
        </View>
      </View>

      {renderTabs()}
    </View>
  )
}

// Predefined navigation layouts
export const AdminLayout = (props: Omit<NavigationLayoutProps, 'variant'>) => (
  <NavigationLayout {...props} variant="sidebar" />
)

export const MobileLayout = (props: Omit<NavigationLayoutProps, 'variant'>) => (
  <NavigationLayout {...props} variant="tabs" />
)

export const DrawerLayout = (props: Omit<NavigationLayoutProps, 'variant'>) => (
  <NavigationLayout {...props} variant="drawer" />
)
