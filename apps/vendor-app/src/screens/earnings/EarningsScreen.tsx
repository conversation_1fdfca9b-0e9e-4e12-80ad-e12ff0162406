import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeftIcon,
  DollarSignIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarIcon,
  DownloadIcon,
  FilterIcon,
  CreditCardIcon,
  ClockIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { useOrdersStore } from '../../stores/ordersStore'
import type { DashboardStackScreenProps } from '../../types/navigation'

const { width } = Dimensions.get('window')

type Props = DashboardStackScreenProps<'Earnings'>

export function EarningsScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const { orders, fetchOrders, getOrdersStats } = useOrdersStore()

  const [refreshing, setRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('month')

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      await fetchOrders()
    } catch (error) {
      console.error('Failed to load earnings data:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadData()
    setRefreshing(false)
  }

  const getEarningsData = () => {
    const stats = getOrdersStats()
    const completedOrders = orders.filter(order => order.status === 'completed')

    // Calculate earnings for different periods
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    const yearAgo = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)

    const todayEarnings = completedOrders
      .filter(order => new Date(order.completedAt || order.createdAt) >= today)
      .reduce((sum, order) => sum + order.totalAmount, 0)

    const weekEarnings = completedOrders
      .filter(order => new Date(order.completedAt || order.createdAt) >= weekAgo)
      .reduce((sum, order) => sum + order.totalAmount, 0)

    const monthEarnings = completedOrders
      .filter(order => new Date(order.completedAt || order.createdAt) >= monthAgo)
      .reduce((sum, order) => sum + order.totalAmount, 0)

    const yearEarnings = completedOrders
      .filter(order => new Date(order.completedAt || order.createdAt) >= yearAgo)
      .reduce((sum, order) => sum + order.totalAmount, 0)

    return {
      today: { amount: todayEarnings, orders: completedOrders.filter(o => new Date(o.completedAt || o.createdAt) >= today).length },
      week: { amount: weekEarnings, orders: completedOrders.filter(o => new Date(o.completedAt || o.createdAt) >= weekAgo).length },
      month: { amount: monthEarnings, orders: completedOrders.filter(o => new Date(o.completedAt || o.createdAt) >= monthAgo).length },
      year: { amount: yearEarnings, orders: completedOrders.filter(o => new Date(o.completedAt || o.createdAt) >= yearAgo).length },
      total: user?.stats.totalEarnings || 0,
      pending: orders.filter(o => o.paymentStatus === 'pending').reduce((sum, o) => sum + o.totalAmount, 0)
    }
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            style={{ marginRight: 16 }}
            onPress={() => navigation.goBack()}
          >
            <ArrowLeftIcon size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={[typography.headline.large, { color: colors.text.primary }]}>
            Earnings & Analytics
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 20,
              padding: 8,
            }}
          >
            <FilterIcon size={20} color={colors.text.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 20,
              padding: 8,
            }}
          >
            <DownloadIcon size={20} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Period Selector */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          {[
            { key: 'today', label: 'Today' },
            { key: 'week', label: 'This Week' },
            { key: 'month', label: 'This Month' },
            { key: 'year', label: 'This Year' },
          ].map((period) => (
            <TouchableOpacity
              key={period.key}
              style={{
                backgroundColor: selectedPeriod === period.key ? colors.primary[500] : colors.background.secondary,
                borderRadius: 20,
                paddingHorizontal: 16,
                paddingVertical: 8,
              }}
              onPress={() => setSelectedPeriod(period.key as any)}
            >
              <Text style={[
                typography.body.small,
                {
                  color: selectedPeriod === period.key ? colors.white : colors.text.primary,
                  fontWeight: '600'
                }
              ]}>
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  )

  const renderEarningsOverview = () => {
    const earningsData = getEarningsData()
    const currentPeriodData = earningsData[selectedPeriod]

    return (
      <View style={{ padding: 16 }}>
        {/* Main Earnings Card */}
        <View style={{
          backgroundColor: colors.primary[500],
          borderRadius: 16,
          padding: 20,
          marginBottom: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <DollarSignIcon size={24} color={colors.white} />
            <Text style={[typography.body.medium, { color: colors.white, marginLeft: 8, opacity: 0.9 }]}>
              {selectedPeriod === 'today' ? 'Today\'s Earnings' :
               selectedPeriod === 'week' ? 'This Week\'s Earnings' :
               selectedPeriod === 'month' ? 'This Month\'s Earnings' :
               'This Year\'s Earnings'}
            </Text>
          </View>

          <Text style={[typography.display.medium, { color: colors.white, fontWeight: '700', marginBottom: 4 }]}>
            R{currentPeriodData.amount.toLocaleString()}
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TrendingUpIcon size={16} color={colors.white} />
            <Text style={[typography.body.small, { color: colors.white, marginLeft: 4, opacity: 0.9 }]}>
              {currentPeriodData.orders} completed orders
            </Text>
          </View>
        </View>

        {/* Stats Grid */}
        <View style={{ flexDirection: 'row', gap: 12, marginBottom: 16 }}>
          <View style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <CreditCardIcon size={20} color={colors.success[500]} />
              <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 8 }]}>
                Total Earned
              </Text>
            </View>
            <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
              R{earningsData.total.toLocaleString()}
            </Text>
          </View>

          <View style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderRadius: 12,
            padding: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <ClockIcon size={20} color={colors.warning[500]} />
              <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 8 }]}>
                Pending
              </Text>
            </View>
            <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
              R{earningsData.pending.toLocaleString()}
            </Text>
          </View>
        </View>

        {/* Period Comparison */}
        <View style={{
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
        }}>
          <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }]}>
            Earnings Breakdown
          </Text>

          <View style={{ gap: 12 }}>
            {[
              { label: 'Today', amount: earningsData.today.amount, orders: earningsData.today.orders },
              { label: 'This Week', amount: earningsData.week.amount, orders: earningsData.week.orders },
              { label: 'This Month', amount: earningsData.month.amount, orders: earningsData.month.orders },
              { label: 'This Year', amount: earningsData.year.amount, orders: earningsData.year.orders },
            ].map((item, index) => (
              <View key={index} style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={[typography.body.small, { color: colors.text.secondary }]}>
                  {item.label}
                </Text>
                <View style={{ alignItems: 'flex-end' }}>
                  <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                    R{item.amount.toLocaleString()}
                  </Text>
                  <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
                    {item.orders} orders
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </View>
    )
  }

  const renderRecentTransactions = () => {
    const recentOrders = orders
      .filter(order => order.status === 'completed')
      .sort((a, b) => new Date(b.completedAt || b.createdAt).getTime() - new Date(a.completedAt || a.createdAt).getTime())
      .slice(0, 10)

    return (
      <View style={{ paddingHorizontal: 16, marginBottom: 24 }}>
        <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 16 }]}>
          Recent Transactions
        </Text>

        {recentOrders.length === 0 ? (
          <View style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 12,
            padding: 24,
            alignItems: 'center',
          }}>
            <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center' }]}>
              No transactions yet
            </Text>
            <Text style={[typography.body.small, { color: colors.text.tertiary, textAlign: 'center', marginTop: 4 }]}>
              Complete orders to see your transaction history
            </Text>
          </View>
        ) : (
          <View style={{ gap: 8 }}>
            {recentOrders.map((order) => (
              <View
                key={order.id}
                style={{
                  backgroundColor: colors.background.secondary,
                  borderRadius: 12,
                  padding: 16,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <View style={{ flex: 1 }}>
                  <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                    {order.serviceName}
                  </Text>
                  <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
                    {order.customerName} • {new Date(order.completedAt || order.createdAt).toLocaleDateString()}
                  </Text>
                </View>

                <View style={{ alignItems: 'flex-end' }}>
                  <Text style={[typography.body.medium, { color: colors.success[600], fontWeight: '700' }]}>
                    +R{order.totalAmount}
                  </Text>
                  <View style={{
                    backgroundColor: order.paymentStatus === 'paid' ? colors.success[100] : colors.warning[100],
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 4,
                    marginTop: 2,
                  }}>
                    <Text style={[
                      typography.body.small,
                      {
                        color: order.paymentStatus === 'paid' ? colors.success[700] : colors.warning[700],
                        fontSize: 10,
                        fontWeight: '600'
                      }
                    ]}>
                      {order.paymentStatus.toUpperCase()}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderHeader()}
        {renderEarningsOverview()}
        {renderRecentTransactions()}
      </ScrollView>
    </SafeAreaView>
  )
}
