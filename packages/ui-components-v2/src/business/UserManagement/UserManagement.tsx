"use client"

import React, { useState, useCallback } from 'react'
import { View, Pressable, Modal } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button, Input } from '@gluestack-ui/themed'
import { 
  UserIcon, 
  MailIcon, 
  PhoneIcon, 
  CalendarIcon,
  MoreVerticalIcon,
  EditIcon,
  TrashIcon,
  UserPlusIcon,
  SearchIcon,
  FilterIcon,
  DownloadIcon,
  ShieldIcon,
  CheckCircleIcon,
  XCircleIcon
} from 'lucide-react-native'
import { DataTable } from '../../data/DataTable/DataTable'
import { SearchableList } from '../../data/SearchableList/SearchableList'
import { FormBuilder } from '../../forms/FormBuilder/FormBuilder'

// Types
export interface User {
  id: string
  name: string
  email: string
  phone?: string
  role: 'admin' | 'user' | 'moderator' | 'guest'
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  avatar?: string
  createdAt: Date
  lastLogin?: Date
  permissions?: string[]
}

export interface UserManagementProps {
  users: User[]
  loading?: boolean
  onCreateUser?: (userData: Partial<User>) => Promise<void>
  onUpdateUser?: (userId: string, userData: Partial<User>) => Promise<void>
  onDeleteUser?: (userId: string) => Promise<void>
  onBulkAction?: (userIds: string[], action: string) => Promise<void>
  onExport?: (users: User[]) => void
  permissions?: {
    canCreate?: boolean
    canEdit?: boolean
    canDelete?: boolean
    canExport?: boolean
    canManageRoles?: boolean
  }
  className?: string
  variant?: 'table' | 'cards' | 'list'
}

// Styles
const containerStyles = tva({
  base: "flex-1 bg-background-0",
})

const headerStyles = tva({
  base: "flex-row items-center justify-between p-4 border-b border-outline-200",
})

const actionBarStyles = tva({
  base: "flex-row items-center gap-2 p-4 bg-background-50 border-b border-outline-200",
})

const userCardStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg p-4 mb-3",
})

const avatarStyles = tva({
  base: "w-12 h-12 rounded-full bg-primary-100 items-center justify-center mr-3",
})

const statusBadgeStyles = tva({
  base: "px-2 py-1 rounded-full text-xs font-medium",
  variants: {
    status: {
      active: "bg-success-100 text-success-700",
      inactive: "bg-outline-100 text-outline-700",
      pending: "bg-warning-100 text-warning-700",
      suspended: "bg-error-100 text-error-700",
    },
  },
})

const roleBadgeStyles = tva({
  base: "px-2 py-1 rounded text-xs font-medium ml-2",
  variants: {
    role: {
      admin: "bg-error-100 text-error-700",
      moderator: "bg-warning-100 text-warning-700",
      user: "bg-info-100 text-info-700",
      guest: "bg-outline-100 text-outline-700",
    },
  },
})

export function UserManagement({
  users,
  loading = false,
  onCreateUser,
  onUpdateUser,
  onDeleteUser,
  onBulkAction,
  onExport,
  permissions = {
    canCreate: true,
    canEdit: true,
    canDelete: true,
    canExport: true,
    canManageRoles: true,
  },
  className,
  variant = 'table',
}: UserManagementProps) {
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set())
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [roleFilter, setRoleFilter] = useState<string>('')

  // Filter users
  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchQuery || 
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || user.status === statusFilter
    const matchesRole = !roleFilter || user.role === roleFilter

    return matchesSearch && matchesStatus && matchesRole
  })

  // Handle user selection
  const toggleUserSelection = useCallback((userId: string) => {
    setSelectedUsers(prev => {
      const newSet = new Set(prev)
      if (newSet.has(userId)) {
        newSet.delete(userId)
      } else {
        newSet.add(userId)
      }
      return newSet
    })
  }, [])

  // Handle select all
  const toggleSelectAll = useCallback(() => {
    if (selectedUsers.size === filteredUsers.length) {
      setSelectedUsers(new Set())
    } else {
      setSelectedUsers(new Set(filteredUsers.map(u => u.id)))
    }
  }, [selectedUsers.size, filteredUsers])

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action: string) => {
    if (selectedUsers.size === 0) return
    
    await onBulkAction?.(Array.from(selectedUsers), action)
    setSelectedUsers(new Set())
  }, [selectedUsers, onBulkAction])

  // User form fields
  const userFormFields = [
    {
      name: 'name',
      label: 'Full Name',
      type: 'text' as const,
      required: true,
      placeholder: 'Enter full name',
    },
    {
      name: 'email',
      label: 'Email Address',
      type: 'email' as const,
      required: true,
      placeholder: 'Enter email address',
    },
    {
      name: 'phone',
      label: 'Phone Number',
      type: 'text' as const,
      placeholder: 'Enter phone number',
    },
    {
      name: 'role',
      label: 'Role',
      type: 'select' as const,
      required: true,
      options: [
        { label: 'User', value: 'user' },
        { label: 'Moderator', value: 'moderator' },
        { label: 'Admin', value: 'admin' },
        { label: 'Guest', value: 'guest' },
      ],
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select' as const,
      required: true,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Pending', value: 'pending' },
        { label: 'Suspended', value: 'suspended' },
      ],
    },
  ]

  // Table columns
  const tableColumns = [
    {
      key: 'select',
      title: '',
      width: 50,
      render: (_: any, user: User) => (
        <Pressable onPress={() => toggleUserSelection(user.id)}>
          <View className={`w-5 h-5 border-2 rounded ${
            selectedUsers.has(user.id) ? 'bg-primary-500 border-primary-500' : 'border-outline-300'
          }`}>
            {selectedUsers.has(user.id) && (
              <CheckCircleIcon size={16} className="text-white" />
            )}
          </View>
        </Pressable>
      ),
    },
    {
      key: 'user',
      title: 'User',
      sortable: true,
      render: (_: any, user: User) => (
        <View className="flex-row items-center">
          <View className={avatarStyles()}>
            {user.avatar ? (
              <img src={user.avatar} className="w-full h-full rounded-full" />
            ) : (
              <UserIcon size={24} className="text-primary-600" />
            )}
          </View>
          <View>
            <Text className="font-medium text-typography-900">{user.name}</Text>
            <Text className="text-sm text-typography-600">{user.email}</Text>
          </View>
        </View>
      ),
    },
    {
      key: 'role',
      title: 'Role',
      sortable: true,
      render: (value: string) => (
        <View className={roleBadgeStyles({ role: value as any })}>
          <Text className="capitalize">{value}</Text>
        </View>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value: string) => (
        <View className={statusBadgeStyles({ status: value as any })}>
          <Text className="capitalize">{value}</Text>
        </View>
      ),
    },
    {
      key: 'lastLogin',
      title: 'Last Login',
      sortable: true,
      render: (value: Date) => (
        <Text className="text-sm text-typography-600">
          {value ? value.toLocaleDateString() : 'Never'}
        </Text>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      width: 100,
      render: (_: any, user: User) => (
        <View className="flex-row items-center gap-2">
          {permissions.canEdit && (
            <Pressable onPress={() => setEditingUser(user)}>
              <EditIcon size={16} className="text-info-600" />
            </Pressable>
          )}
          {permissions.canDelete && (
            <Pressable onPress={() => onDeleteUser?.(user.id)}>
              <TrashIcon size={16} className="text-error-600" />
            </Pressable>
          )}
        </View>
      ),
    },
  ]

  // Render user card
  const renderUserCard = (user: User) => (
    <View key={user.id} className={userCardStyles()}>
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-row items-center flex-1">
          <View className={avatarStyles()}>
            {user.avatar ? (
              <img src={user.avatar} className="w-full h-full rounded-full" />
            ) : (
              <UserIcon size={24} className="text-primary-600" />
            )}
          </View>
          <View className="flex-1">
            <Text className="font-medium text-typography-900">{user.name}</Text>
            <Text className="text-sm text-typography-600">{user.email}</Text>
          </View>
        </View>
        
        <View className="flex-row items-center">
          <View className={statusBadgeStyles({ status: user.status })}>
            <Text className="capitalize">{user.status}</Text>
          </View>
          <View className={roleBadgeStyles({ role: user.role })}>
            <Text className="capitalize">{user.role}</Text>
          </View>
        </View>
      </View>

      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center gap-4">
          {user.phone && (
            <View className="flex-row items-center">
              <PhoneIcon size={14} className="text-typography-500 mr-1" />
              <Text className="text-sm text-typography-600">{user.phone}</Text>
            </View>
          )}
          <View className="flex-row items-center">
            <CalendarIcon size={14} className="text-typography-500 mr-1" />
            <Text className="text-sm text-typography-600">
              {user.createdAt.toLocaleDateString()}
            </Text>
          </View>
        </View>

        <View className="flex-row items-center gap-2">
          {permissions.canEdit && (
            <Pressable onPress={() => setEditingUser(user)}>
              <EditIcon size={16} className="text-info-600" />
            </Pressable>
          )}
          {permissions.canDelete && (
            <Pressable onPress={() => onDeleteUser?.(user.id)}>
              <TrashIcon size={16} className="text-error-600" />
            </Pressable>
          )}
        </View>
      </View>
    </View>
  )

  // Render header
  const renderHeader = () => (
    <View className={headerStyles()}>
      <View>
        <Text className="text-xl font-bold text-typography-900">
          User Management
        </Text>
        <Text className="text-sm text-typography-600">
          {filteredUsers.length} users
        </Text>
      </View>

      <View className="flex-row items-center gap-2">
        {permissions.canExport && (
          <Button
            variant="outline"
            size="sm"
            onPress={() => onExport?.(filteredUsers)}
          >
            <DownloadIcon size={16} className="mr-2" />
            <Text>Export</Text>
          </Button>
        )}
        
        {permissions.canCreate && (
          <Button
            size="sm"
            onPress={() => setShowCreateModal(true)}
          >
            <UserPlusIcon size={16} className="mr-2" />
            <Text>Add User</Text>
          </Button>
        )}
      </View>
    </View>
  )

  // Render action bar
  const renderActionBar = () => (
    <View className={actionBarStyles()}>
      <View className="flex-row items-center flex-1 gap-2">
        <View className="flex-row items-center bg-background-0 rounded-lg px-3 py-2 border border-outline-200 flex-1 max-w-xs">
          <SearchIcon size={16} className="text-typography-400 mr-2" />
          <Input
            placeholder="Search users..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            className="flex-1 border-0 p-0"
          />
        </View>

        <Pressable className="flex-row items-center bg-background-0 rounded-lg px-3 py-2 border border-outline-200">
          <FilterIcon size={16} className="text-typography-400 mr-2" />
          <Text className="text-sm text-typography-600">Filter</Text>
        </Pressable>
      </View>

      {selectedUsers.size > 0 && (
        <View className="flex-row items-center gap-2">
          <Text className="text-sm text-typography-600">
            {selectedUsers.size} selected
          </Text>
          <Button
            variant="outline"
            size="sm"
            onPress={() => handleBulkAction('activate')}
          >
            <Text>Activate</Text>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onPress={() => handleBulkAction('deactivate')}
          >
            <Text>Deactivate</Text>
          </Button>
          {permissions.canDelete && (
            <Button
              variant="outline"
              size="sm"
              onPress={() => handleBulkAction('delete')}
            >
              <Text>Delete</Text>
            </Button>
          )}
        </View>
      )}
    </View>
  )

  return (
    <View className={containerStyles({ class: className })}>
      {renderHeader()}
      {renderActionBar()}

      {variant === 'table' ? (
        <DataTable
          data={filteredUsers}
          columns={tableColumns}
          loading={loading}
          onRowPress={(user) => setEditingUser(user)}
        />
      ) : variant === 'cards' ? (
        <ScrollView className="flex-1 p-4">
          {filteredUsers.map(renderUserCard)}
        </ScrollView>
      ) : (
        <SearchableList
          data={filteredUsers}
          renderItem={renderUserCard}
          searchKeys={['name', 'email']}
          loading={loading}
        />
      )}

      {/* Create User Modal */}
      <Modal visible={showCreateModal} transparent animationType="slide">
        <View className="flex-1 justify-center items-center bg-black/50">
          <View className="bg-background-0 rounded-lg p-6 m-4 w-full max-w-md">
            <Text className="text-lg font-bold text-typography-900 mb-4">
              Create New User
            </Text>
            
            <FormBuilder
              sections={[{ title: 'User Information', fields: userFormFields }]}
              onSubmit={async (data) => {
                await onCreateUser?.(data)
                setShowCreateModal(false)
              }}
              submitText="Create User"
            />
            
            <Button
              variant="outline"
              onPress={() => setShowCreateModal(false)}
              className="mt-2"
            >
              <Text>Cancel</Text>
            </Button>
          </View>
        </View>
      </Modal>

      {/* Edit User Modal */}
      <Modal visible={!!editingUser} transparent animationType="slide">
        <View className="flex-1 justify-center items-center bg-black/50">
          <View className="bg-background-0 rounded-lg p-6 m-4 w-full max-w-md">
            <Text className="text-lg font-bold text-typography-900 mb-4">
              Edit User
            </Text>
            
            {editingUser && (
              <FormBuilder
                sections={[{ title: 'User Information', fields: userFormFields }]}
                initialValues={editingUser}
                onSubmit={async (data) => {
                  await onUpdateUser?.(editingUser.id, data)
                  setEditingUser(null)
                }}
                submitText="Update User"
              />
            )}
            
            <Button
              variant="outline"
              onPress={() => setEditingUser(null)}
              className="mt-2"
            >
              <Text>Cancel</Text>
            </Button>
          </View>
        </View>
      </Modal>
    </View>
  )
}
