"use client"

import React, { useState } from 'react'
import { <PERSON>, ScrollView, Image } from 'react-native'
import { Text, Button } from '@gluestack-ui/themed'
import { StarIcon, ClockIcon, MapPinIcon, PhoneIcon } from 'lucide-react-native'

// Import components from the UI package
import { NavigationLayout } from '../../../layouts/NavigationLayout'
import { SearchableList } from '../../../data/SearchableList'
import { MenuItemCard, type MenuItem } from '../../../business/MenuItemCard'
import { type Vendor } from '../../../business/VendorCard'
import { useDataManagement } from '../../../hooks'

// Types
export interface CartItem {
  id: string
  menuItem: MenuItem
  quantity: number
  customizations?: any
}

export interface VendorDetailScreenProps {
  vendor: Vendor
  menuItems: MenuItem[]
  cartItems: CartItem[]
  onNavigate: (route: string, params?: any) => void
  onAddToCart: (item: MenuItem, quantity: number, customizations?: any) => void
  onToggleFavorite: (itemId: string) => void
  favoriteItems: string[]
  loading?: boolean
}

/**
 * Customer App Vendor Detail Screen
 * 
 * Features:
 * - Vendor information display
 * - Menu browsing with categories
 * - Add to cart functionality
 * - Favorite items toggle
 * - Cart summary with floating button
 */
export function VendorDetailScreen({
  vendor,
  menuItems,
  cartItems,
  onNavigate,
  onAddToCart,
  onToggleFavorite,
  favoriteItems,
  loading = false,
}: VendorDetailScreenProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  // Get unique categories from menu items
  const menuCategories = ['All', ...Array.from(new Set(menuItems.map(item => item.category)))]

  // Use data management hook for menu filtering
  const {
    processedData: filteredMenuItems,
    setSearch,
    setFilters,
  } = useDataManagement(menuItems, {
    searchKeys: ['name', 'description'],
    initialFilters: { category: 'All' },
  })

  // Calculate cart totals
  const cartTotal = cartItems.reduce((sum, item) => sum + (item.menuItem.price * item.quantity), 0)
  const cartItemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0)

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category)
    setFilters({ category: category === 'All' ? undefined : category })
  }

  const handleAddToCart = (item: MenuItem, quantity: number, customizations?: any) => {
    onAddToCart(item, quantity, customizations)
  }

  const handleToggleFavorite = (itemId: string) => {
    onToggleFavorite(itemId)
  }

  const renderVendorHeader = () => (
    <View className="relative">
      <Image
        source={{ uri: vendor.imageUrl || 'https://via.placeholder.com/400x200' }}
        className="w-full h-48"
        resizeMode="cover"
      />
      <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
        <Text className="text-white text-2xl font-bold mb-1">
          {vendor.name}
        </Text>
        <Text className="text-white/90 text-base">
          {vendor.description}
        </Text>
      </View>
    </View>
  )

  const renderVendorInfo = () => (
    <View className="p-4 border-b border-outline-200">
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-row items-center">
          <StarIcon size={20} className="text-warning-500" />
          <Text className="ml-2 text-lg font-semibold text-typography-900">
            {vendor.rating.toFixed(1)}
          </Text>
          <Text className="ml-1 text-typography-600">
            ({vendor.reviewCount} reviews)
          </Text>
        </View>
        <View className="flex-row items-center">
          <ClockIcon size={16} className="text-typography-500" />
          <Text className="ml-1 text-typography-600">
            {vendor.deliveryTime}
          </Text>
        </View>
      </View>

      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center">
          <MapPinIcon size={16} className="text-typography-500" />
          <Text className="ml-1 text-typography-600">
            {vendor.distance.toFixed(1)}km away
          </Text>
        </View>
        <View className="flex-row items-center">
          <Text className="text-typography-600">
            R{vendor.deliveryFee.toFixed(2)} delivery
          </Text>
        </View>
      </View>

      {!vendor.isOpen && (
        <View className="mt-3 p-3 bg-error-50 rounded-lg">
          <Text className="text-error-700 font-medium text-center">
            This vendor is currently closed
          </Text>
        </View>
      )}
    </View>
  )

  const renderCategoryTabs = () => (
    <View className="px-4 py-3 border-b border-outline-200">
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View className="flex-row space-x-2">
          {menuCategories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'solid' : 'outline'}
              size="sm"
              onPress={() => handleCategoryFilter(category)}
              className="mr-2"
            >
              <Text>{category}</Text>
            </Button>
          ))}
        </View>
      </ScrollView>
    </View>
  )

  const renderMenuItems = () => {
    if (loading) {
      return (
        <View className="p-4">
          <Text className="text-center text-typography-600">Loading menu...</Text>
        </View>
      )
    }

    return (
      <SearchableList
        data={filteredMenuItems}
        searchKeys={['name', 'description']}
        renderItem={(item) => (
          <MenuItemCard
            key={item.id}
            item={item}
            onAddToCart={handleAddToCart}
            onToggleFavorite={handleToggleFavorite}
            isFavorite={favoriteItems.includes(item.id)}
            showAddButton={vendor.isOpen}
          />
        )}
        filters={[
          {
            key: 'category',
            type: 'select',
            label: 'Category',
            options: menuCategories,
          },
          {
            key: 'price',
            type: 'range',
            label: 'Price Range (R)',
            min: 0,
            max: 200,
            step: 10,
          },
        ]}
        emptyMessage="No menu items found"
        className="px-4"
      />
    )
  }

  const renderFloatingCartButton = () => {
    if (cartItemCount === 0) return null

    return (
      <View className="absolute bottom-4 left-4 right-4">
        <Button
          onPress={() => onNavigate('/cart')}
          className="flex-row items-center justify-between shadow-lg"
          size="lg"
        >
          <View className="flex-row items-center">
            <View className="bg-white/20 rounded-full w-6 h-6 items-center justify-center mr-2">
              <Text className="text-white text-sm font-bold">
                {cartItemCount}
              </Text>
            </View>
            <Text className="text-white font-medium">View Cart</Text>
          </View>
          <Text className="text-white font-bold">
            R{cartTotal.toFixed(2)}
          </Text>
        </Button>
      </View>
    )
  }

  return (
    <NavigationLayout
      variant="stack"
      header={{
        title: vendor.name,
        showBack: true,
        actions: [
          {
            icon: PhoneIcon,
            onPress: () => {
              // Handle call vendor
            },
            label: 'Call',
          },
        ],
      }}
    >
      <View className="flex-1">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {renderVendorHeader()}
          {renderVendorInfo()}
          {renderCategoryTabs()}
          {renderMenuItems()}
          
          {/* Add some bottom padding for the floating cart button */}
          <View className="h-20" />
        </ScrollView>

        {renderFloatingCartButton()}
      </View>
    </NavigationLayout>
  )
}

// Export with default props for easier usage
export const CustomerVendorDetailScreen = (props: VendorDetailScreenProps) => (
  <VendorDetailScreen {...props} />
)
