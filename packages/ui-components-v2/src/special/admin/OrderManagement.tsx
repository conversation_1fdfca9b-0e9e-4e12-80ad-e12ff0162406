import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  H<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  <PERSON>ton,
  ButtonText,
  Card,
  CardHeader,
  CardBody,
  Badge,
  BadgeText,
  Input,
  InputField,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableData,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  CloseIcon,
  Icon,
} from "@/ui";

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  image?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerEmail: string;
  status: "pending" | "confirmed" | "processing" | "shipped" | "delivered" | "cancelled" | "refunded";
  orderDate: string;
  totalAmount: number;
  items: OrderItem[];
  shippingAddress: string;
  paymentMethod: string;
  vendorName?: string;
  trackingNumber?: string;
}

export interface OrderManagementProps {
  /** Array of orders */
  orders?: Order[];
  /** Loading state */
  loading?: boolean;
  /** Search handler */
  onSearch?: (query: string) => void;
  /** Filter handler */
  onFilter?: (filters: { status?: string; dateRange?: string }) => void;
  /** Order action handlers */
  onUpdateStatus?: (orderId: string, status: Order["status"]) => void;
  onViewDetails?: (orderId: string) => void;
  onRefund?: (orderId: string) => void;
  onPrintLabel?: (orderId: string) => void;
  /** Pagination */
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
}

const mockOrders: Order[] = [
  {
    id: "1",
    orderNumber: "ORD-2024-001",
    customerName: "John Doe",
    customerEmail: "<EMAIL>",
    status: "processing",
    orderDate: "2024-03-15",
    totalAmount: 299.99,
    items: [
      { id: "1", name: "Wireless Headphones", quantity: 1, price: 299.99 }
    ],
    shippingAddress: "123 Main St, City, State 12345",
    paymentMethod: "Credit Card",
    vendorName: "TechStore Pro"
  },
  {
    id: "2",
    orderNumber: "ORD-2024-002",
    customerName: "Jane Smith",
    customerEmail: "<EMAIL>",
    status: "shipped",
    orderDate: "2024-03-14",
    totalAmount: 149.50,
    items: [
      { id: "2", name: "Cotton T-Shirt", quantity: 2, price: 74.75 }
    ],
    shippingAddress: "456 Oak Ave, City, State 67890",
    paymentMethod: "PayPal",
    vendorName: "Fashion Hub",
    trackingNumber: "TRK123456789"
  }
];

const OrderManagement = React.memo<OrderManagementProps>(({
  orders = mockOrders,
  loading = false,
  onSearch,
  onFilter,
  onUpdateStatus,
  onViewDetails,
  onRefund,
  onPrintLabel,
  currentPage = 1,
  totalPages = 1,
  onPageChange
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showModal, setShowModal] = useState(false);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    onSearch?.(query);
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    onFilter?.({ status });
  };

  const handleViewDetails = (order: Order) => {
    setSelectedOrder(order);
    setShowModal(true);
    onViewDetails?.(order.id);
  };

  const getStatusBadge = (status: Order["status"]) => {
    const variants = {
      pending: { color: "bg-yellow-500" },
      confirmed: { color: "bg-blue-500" },
      processing: { color: "bg-purple-500" },
      shipped: { color: "bg-indigo-500" },
      delivered: { color: "bg-green-500" },
      cancelled: { color: "bg-red-500" },
      refunded: { color: "bg-gray-500" }
    };

    const config = variants[status];
    return (
      <Badge className={`${config.color} text-white`}>
        <BadgeText className="text-white capitalize">{status}</BadgeText>
      </Badge>
    );
  };

  const renderActionButtons = (order: Order) => {
    return (
      <HStack space="sm">
        <Button size="xs" variant="outline" onPress={() => handleViewDetails(order)}>
          <ButtonText>View</ButtonText>
        </Button>
        
        {order.status === "pending" && (
          <Button 
            size="xs" 
            className="bg-blue-500" 
            onPress={() => onUpdateStatus?.(order.id, "confirmed")}
          >
            <ButtonText className="text-white">Confirm</ButtonText>
          </Button>
        )}
        
        {order.status === "confirmed" && (
          <Button 
            size="xs" 
            className="bg-purple-500" 
            onPress={() => onUpdateStatus?.(order.id, "processing")}
          >
            <ButtonText className="text-white">Process</ButtonText>
          </Button>
        )}
        
        {order.status === "processing" && (
          <Button 
            size="xs" 
            className="bg-indigo-500" 
            onPress={() => onUpdateStatus?.(order.id, "shipped")}
          >
            <ButtonText className="text-white">Ship</ButtonText>
          </Button>
        )}
        
        {["pending", "confirmed"].includes(order.status) && (
          <Button 
            size="xs" 
            className="bg-red-500" 
            onPress={() => onUpdateStatus?.(order.id, "cancelled")}
          >
            <ButtonText className="text-white">Cancel</ButtonText>
          </Button>
        )}
      </HStack>
    );
  };

  return (
    <VStack space="lg" className="p-6">
      <VStack space="md">
        <Heading size="xl">Order Management</Heading>
        <Text className="text-typography-600">
          Track and manage customer orders across all vendors
        </Text>
      </VStack>

      {/* Filters */}
      <Card>
        <CardBody>
          <HStack space="md" className="flex-wrap">
            <Input className="flex-1 min-w-[200px]">
              <InputField
                placeholder="Search orders..."
                value={searchQuery}
                onChangeText={handleSearch}
              />
            </Input>

            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectInput placeholder="Status" />
                <SelectIcon as={ChevronDownIcon} />
              </SelectTrigger>
              <SelectPortal>
                <SelectBackdrop />
                <SelectContent>
                  <SelectDragIndicatorWrapper>
                    <SelectDragIndicator />
                  </SelectDragIndicatorWrapper>
                  <SelectItem label="All Status" value="" />
                  <SelectItem label="Pending" value="pending" />
                  <SelectItem label="Confirmed" value="confirmed" />
                  <SelectItem label="Processing" value="processing" />
                  <SelectItem label="Shipped" value="shipped" />
                  <SelectItem label="Delivered" value="delivered" />
                  <SelectItem label="Cancelled" value="cancelled" />
                  <SelectItem label="Refunded" value="refunded" />
                </SelectContent>
              </SelectPortal>
            </Select>
          </HStack>
        </CardBody>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardBody className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order #</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id}>
                  <TableData>
                    <Text className="font-medium">{order.orderNumber}</Text>
                  </TableData>
                  <TableData>
                    <VStack>
                      <Text className="font-medium">{order.customerName}</Text>
                      <Text className="text-xs text-typography-500">{order.customerEmail}</Text>
                    </VStack>
                  </TableData>
                  <TableData>{getStatusBadge(order.status)}</TableData>
                  <TableData>
                    <Text>{new Date(order.orderDate).toLocaleDateString()}</Text>
                  </TableData>
                  <TableData>
                    <Text className="font-medium">${order.totalAmount.toFixed(2)}</Text>
                  </TableData>
                  <TableData>
                    <Text>{order.vendorName || "N/A"}</Text>
                  </TableData>
                  <TableData>{renderActionButtons(order)}</TableData>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Order Details Modal */}
      <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
        <ModalBackdrop />
        <ModalContent className="max-w-2xl">
          <ModalHeader>
            <Heading size="lg">Order Details</Heading>
            <ModalCloseButton>
              <Icon as={CloseIcon} />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            {selectedOrder && (
              <VStack space="md">
                <HStack className="justify-between">
                  <Text className="font-medium">Order Number:</Text>
                  <Text>{selectedOrder.orderNumber}</Text>
                </HStack>
                <HStack className="justify-between">
                  <Text className="font-medium">Status:</Text>
                  {getStatusBadge(selectedOrder.status)}
                </HStack>
                <HStack className="justify-between">
                  <Text className="font-medium">Customer:</Text>
                  <VStack className="items-end">
                    <Text>{selectedOrder.customerName}</Text>
                    <Text className="text-sm text-typography-500">{selectedOrder.customerEmail}</Text>
                  </VStack>
                </HStack>
                <HStack className="justify-between">
                  <Text className="font-medium">Total Amount:</Text>
                  <Text className="font-bold">${selectedOrder.totalAmount.toFixed(2)}</Text>
                </HStack>
                <VStack space="sm">
                  <Text className="font-medium">Items:</Text>
                  {selectedOrder.items.map((item) => (
                    <HStack key={item.id} className="justify-between p-2 bg-background-50 rounded">
                      <Text>{item.name} x{item.quantity}</Text>
                      <Text>${item.price.toFixed(2)}</Text>
                    </HStack>
                  ))}
                </VStack>
                <VStack space="sm">
                  <Text className="font-medium">Shipping Address:</Text>
                  <Text className="text-sm">{selectedOrder.shippingAddress}</Text>
                </VStack>
                {selectedOrder.trackingNumber && (
                  <HStack className="justify-between">
                    <Text className="font-medium">Tracking Number:</Text>
                    <Text>{selectedOrder.trackingNumber}</Text>
                  </HStack>
                )}
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" onPress={() => setShowModal(false)}>
              <ButtonText>Close</ButtonText>
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Pagination */}
      {totalPages > 1 && (
        <HStack space="sm" className="justify-center">
          <Button
            size="sm"
            variant="outline"
            onPress={() => onPageChange?.(currentPage - 1)}
            isDisabled={currentPage === 1}
          >
            <ButtonText>Previous</ButtonText>
          </Button>
          
          <Text className="px-4 py-2">
            Page {currentPage} of {totalPages}
          </Text>
          
          <Button
            size="sm"
            variant="outline"
            onPress={() => onPageChange?.(currentPage + 1)}
            isDisabled={currentPage === totalPages}
          >
            <ButtonText>Next</ButtonText>
          </Button>
        </HStack>
      )}
    </VStack>
  );
});

OrderManagement.displayName = "OrderManagement";
export default OrderManagement;