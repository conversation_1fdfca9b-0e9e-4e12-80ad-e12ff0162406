{"name": "ui-example-nativewind", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "DARK_MODE=media expo run:android", "ios": "DARK_MODE=media expo run:ios", "web": "DARK_MODE=media expo start --web"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/config-plugins": "~9.0.0", "@expo/html-elements": "^0.4.2", "@expo/metro-runtime": "~4.0.0", "@gluestack-ui/accordion": "^1.0.8", "@gluestack-ui/actionsheet": "^0.2.46", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.32", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.8", "@gluestack-ui/checkbox": "^0.1.33", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.22", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.24-alpha.0", "@gluestack-ui/image": "^0.1.11", "@gluestack-ui/input": "^0.1.32", "@gluestack-ui/link": "^0.1.23", "@gluestack-ui/menu": "^0.2.37", "@gluestack-ui/modal": "^0.1.35", "@gluestack-ui/nativewind-utils": "^1.0.26-alpha.2", "@gluestack-ui/overlay": "^0.1.16", "@gluestack-ui/popover": "^0.1.43", "@gluestack-ui/pressable": "^0.1.17", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.34", "@gluestack-ui/select": "^0.1.30", "@gluestack-ui/slider": "^0.1.26", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.23", "@gluestack-ui/textarea": "^0.1.24", "@gluestack-ui/toast": "^1.0.8", "@gluestack-ui/tooltip": "^0.1.36-alpha.0", "@legendapp/motion": "^2.4.0", "babel-plugin-module-resolver": "^5.0.2", "expo": "52.0.11", "expo-linking": "~7.0.3", "expo-status-bar": "~2.0.0", "expo-updates": "~0.26.9", "lucide-react-native": "^0.445.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.14.0", "react-native-svg": "15.2.0", "react-native-web": "~0.19.13", "serve": "^14.2.2", "tailwindcss": "^3.4.15"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "jscodeshift": "^0.15.2", "typescript": "~5.3.3"}, "private": true}