# 🚀 Automation Guide for Compound Components Library

This guide explains how to use the CLI commands and automation scripts to efficiently set up and enhance the `@hvppyplug/compound-components` library using the official Gluestack UI v2 CLI.

## 🎯 Why Use CLI Automation Instead of Manual Copying?

### ✅ **Benefits of CLI Automation:**
- **Official Support**: Uses the official Gluestack UI CLI with guaranteed compatibility
- **Automatic Updates**: Components stay up-to-date with latest Gluestack UI versions
- **Dependency Management**: Automatically handles all required dependencies
- **Configuration**: Properly configures Tailwind, TypeScript, and build tools
- **Best Practices**: Follows official patterns and conventions
- **Time Saving**: Automates repetitive setup tasks
- **Error Prevention**: Reduces manual configuration errors

### ❌ **Problems with Manual Copying:**
- Version mismatches and compatibility issues
- Missing dependencies and configuration
- Outdated patterns and deprecated APIs
- Time-consuming and error-prone
- Difficult to maintain and update

## 🛠️ Available Automation Scripts

### 1. **Quick Setup** (Recommended for new projects)
```bash
pnpm setup:quick
```
**What it does:**
- Installs all dependencies
- Runs enhanced Gluestack UI setup
- Builds the library
- Provides next steps

### 2. **Enhanced Setup** (Full automation)
```bash
pnpm setup:enhanced
```
**What it does:**
- Initializes Gluestack UI v2 with proper configuration
- Adds essential UI components (box, text, button, etc.)
- Extracts enhanced patterns from ui-example-nativewind
- Updates package scripts
- Interactive component selection

### 3. **Basic Gluestack Setup**
```bash
pnpm setup:gluestack
```
**What it does:**
- Runs `npx gluestack-ui init` with proper path configuration
- Sets up basic Gluestack UI structure

## 📦 Component Management Commands

### Add Individual Components
```bash
# Add specific components
pnpm gluestack:add button
pnpm gluestack:add input textarea
pnpm gluestack:add modal toast alert

# Add with specific path
npx gluestack-ui add button --path src/gluestack-components/ui
```

### Add All Components
```bash
# Add all available Gluestack UI components
pnpm gluestack:add-all
```

### Generate Custom Compound Components
```bash
# Interactive component generator
pnpm generate:component

# Direct generation
node scripts/generate-component.js "UserProfile" compound-business
```

## 🔍 Pattern Extraction Commands

### Extract from Example Project
```bash
# Extract patterns from ui-example-nativewind
pnpm extract:patterns

# Direct extraction
node scripts/extract-from-example.js
```

**What gets extracted:**
- Responsive breakpoint utilities
- Enhanced theme configurations
- Web optimization styles
- Component composition patterns
- Advanced TypeScript definitions

## 📋 Step-by-Step Setup Guide

### For New Projects:
```bash
# 1. Quick setup (recommended)
pnpm setup:quick

# 2. Or manual step-by-step
pnpm install
pnpm setup:enhanced
pnpm build
```

### For Existing Projects:
```bash
# 1. Add specific components you need
pnpm gluestack:add button input modal

# 2. Extract enhanced patterns
pnpm extract:patterns

# 3. Generate custom compound components
pnpm generate:component
```

## 🎨 Customization Workflow

### 1. **Start with Gluestack UI Base**
```bash
# Add the base components you need
pnpm gluestack:add box text button input
```

### 2. **Extract Enhanced Patterns**
```bash
# Get advanced patterns and utilities
pnpm extract:patterns
```

### 3. **Generate Compound Components**
```bash
# Create business-specific compound components
pnpm generate:component
```

### 4. **Build and Test**
```bash
# Build the library
pnpm build

# Run tests
pnpm test

# Check health
pnpm health-check
```

## 📁 Generated File Structure

After running the automation scripts, your project will have:

```
src/
├── gluestack-components/ui/     # Official Gluestack UI components
│   ├── box/
│   ├── text/
│   ├── button/
│   └── ...
├── hooks/                       # Enhanced hooks
│   ├── useBreakpointValue.ts   # Responsive utilities
│   └── ...
├── theme/                       # Enhanced theme
│   ├── enhanced-config.ts      # Additional color tokens
│   └── ...
├── styles/                      # Web optimizations
│   ├── web-optimizations.css   # CSS enhancements
│   └── ...
├── examples/                    # Composition patterns
│   ├── composition-patterns.ts  # Real-world examples
│   └── ...
└── compound-components/         # Your custom components
    ├── data/
    ├── forms/
    ├── layouts/
    └── business/
```

## 🔧 Configuration Files

The automation scripts will create/update:

- `gluestack-ui.config.json` - CLI configuration
- `tailwind.config.js` - Enhanced Tailwind setup
- `tsconfig.json` - TypeScript paths and settings
- `package.json` - Scripts and dependencies

## 🚨 Troubleshooting

### Common Issues:

1. **CLI Installation Fails**
   ```bash
   # Try manual installation
   npm install -g @gluestack-ui/cli
   # Or use npx
   npx gluestack-ui init
   ```

2. **Workspace Issues**
   ```bash
   # Run from package directory
   cd packages/ui-components-v2
   pnpm setup:enhanced
   ```

3. **Permission Errors**
   ```bash
   # Use npx instead of global install
   npx gluestack-ui add button
   ```

4. **Component Conflicts**
   ```bash
   # Clean and rebuild
   pnpm clean
   pnpm setup:enhanced
   ```

### Getting Help:

- Check the [official Gluestack UI docs](https://gluestack.io/ui/docs/home/<USER>/cli)
- Run `pnpm health-check` to diagnose issues
- Check the generated `gluestack-ui.config.json` for configuration

## 🎯 Best Practices

1. **Always use the CLI** for adding Gluestack UI components
2. **Extract patterns** before creating custom components
3. **Generate compound components** using the provided templates
4. **Test thoroughly** after each automation step
5. **Keep dependencies updated** using the CLI
6. **Follow the generated structure** for consistency

## 🔄 Updating Components

```bash
# Update to latest Gluestack UI versions
pnpm update @gluestack-ui/*

# Re-run setup to get latest patterns
pnpm extract:patterns

# Rebuild
pnpm build
```

This automation approach ensures your compound components library stays current with the latest Gluestack UI patterns while maintaining the flexibility to create custom business-focused components.
