{"name": "@hvppyplug/customer-app", "version": "1.0.0", "main": "index.ts", "private": true, "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "clean": "expo r -c", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx"}, "dependencies": {"@hvppyplug/common": "workspace:*", "@hvppyplug/compound-components": "workspace:*", "@hvppyplug/mobile-services": "workspace:*", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.0.9", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.1.1", "@tanstack/react-query": "^5.28.4", "appwrite": "^16.0.2", "expo": "~53.0.20", "expo-application": "~6.1.1", "expo-background-fetch": "~13.1.1", "expo-camera": "~16.1.11", "expo-constants": "~17.1.2", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.1", "expo-file-system": "~18.1.1", "expo-haptics": "~14.1.1", "expo-image-picker": "~16.1.3", "expo-linking": "~7.1.2", "expo-local-authentication": "~16.0.5", "expo-location": "~18.1.6", "expo-network": "~7.1.1", "expo-notifications": "~0.31.4", "expo-router": "~4.0.15", "expo-secure-store": "~14.1.1", "expo-status-bar": "~2.2.3", "expo-task-manager": "~13.1.6", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.2", "react-native-maps": "^1.18.0", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "^4.13.1", "react-native-web": "^0.20.0", "zustand": "^4.5.7", "expo-updates": "~0.28.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "typescript": "~5.8.3"}}