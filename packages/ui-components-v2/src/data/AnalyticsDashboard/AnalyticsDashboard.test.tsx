import React from 'react'
import { render } from '@testing-library/react-native'
import { AnalyticsDashboard } from '../AnalyticsDashboard'

describe('AnalyticsDashboard', () => {
  it('renders correctly', () => {
    const { getByText } = render(<AnalyticsDashboard />)
    expect(getByText('AnalyticsDashboard Component')).toBeTruthy()
  })

  it('applies custom className', () => {
    const { getByTestId } = render(
      <AnalyticsDashboard className="custom-class" testID="analyticsdashboard" />
    )
    expect(getByTestId('analyticsdashboard')).toBeTruthy()
  })

  it('handles disabled state', () => {
    const { getByTestId } = render(
      <AnalyticsDashboard disabled testID="analyticsdashboard" />
    )
    expect(getByTestId('analyticsdashboard')).toBeTruthy()
  })
})
