import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView, TouchableOpacity, FlatList, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeftIcon,
  StarIcon,
  MapPinIcon,
  ClockIcon,
  CheckCircleIcon,
  MessageCircleIcon,
  CalendarIcon,
  AwardIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useServicesStore } from '../../stores/servicesStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'ProviderProfile'>

export function ProviderProfileScreen({ navigation, route }: Props) {
  const { providerId } = route.params
  const { colors, typography } = useTheme()
  const { getServices, isLoading } = useServicesStore()

  const [provider, setProvider] = useState<any>(null)
  const [services, setServices] = useState<any[]>([])
  const [reviews, setReviews] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState<'services' | 'reviews' | 'about'>('services')

  useEffect(() => {
    loadProviderData()
  }, [providerId])

  const loadProviderData = async () => {
    try {
      // In a real app, you'd have separate API calls for provider details
      // For now, we'll simulate with mock data
      const mockProvider = {
        id: providerId,
        name: "John's Home Services",
        avatar: null,
        rating: { average: 4.8, count: 127 },
        verified: true,
        responseTime: 15,
        completedJobs: 234,
        joinedDate: '2023-01-15',
        description: 'Professional home services provider with over 5 years of experience. Specializing in plumbing, electrical work, and general maintenance.',
        location: {
          address: 'Soweto, Johannesburg',
          coordinates: { lat: -26.2678, lng: 27.8546 }
        },
        badges: ['Top Rated', 'Quick Response', 'Verified'],
        availability: {
          monday: { isAvailable: true, slots: [{ start: '08:00', end: '17:00' }] },
          tuesday: { isAvailable: true, slots: [{ start: '08:00', end: '17:00' }] },
          wednesday: { isAvailable: true, slots: [{ start: '08:00', end: '17:00' }] },
          thursday: { isAvailable: true, slots: [{ start: '08:00', end: '17:00' }] },
          friday: { isAvailable: true, slots: [{ start: '08:00', end: '17:00' }] },
          saturday: { isAvailable: true, slots: [{ start: '09:00', end: '15:00' }] },
          sunday: { isAvailable: false, slots: [] }
        }
      }

      const mockServices = [
        {
          id: '1',
          name: 'Plumbing Repair',
          description: 'Fix leaks, unclog drains, and general plumbing maintenance',
          price: { amount: 150, currency: 'ZAR', type: 'fixed' },
          duration: 60,
          rating: { average: 4.9, count: 45 },
          isActive: true
        },
        {
          id: '2',
          name: 'Electrical Installation',
          description: 'Install lights, outlets, and electrical fixtures',
          price: { amount: 200, currency: 'ZAR', type: 'hourly' },
          duration: 90,
          rating: { average: 4.7, count: 32 },
          isActive: true
        }
      ]

      const mockReviews = [
        {
          id: '1',
          customerName: 'Sarah M.',
          rating: 5,
          comment: 'Excellent service! John was professional and fixed my plumbing issue quickly.',
          date: '2024-01-15',
          service: 'Plumbing Repair'
        },
        {
          id: '2',
          customerName: 'Mike T.',
          rating: 4,
          comment: 'Good work on the electrical installation. Would recommend.',
          date: '2024-01-10',
          service: 'Electrical Installation'
        }
      ]

      setProvider(mockProvider)
      setServices(mockServices)
      setReviews(mockReviews)
    } catch (error) {
      Alert.alert('Error', 'Failed to load provider information')
      navigation.goBack()
    }
  }

  const handleContactProvider = () => {
    navigation.navigate('Chat', {
      orderId: 'temp',
      providerId: provider.id
    })
  }

  const handleBookService = (serviceId: string) => {
    navigation.navigate('Booking', {
      serviceId,
      providerId: provider.id
    })
  }

  if (isLoading || !provider) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <LoadingSpinner message="Loading provider profile..." />
      </SafeAreaView>
    )
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      {/* Navigation */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          top: 50,
          left: 16,
          zIndex: 1,
        }}
        onPress={() => navigation.goBack()}
      >
        <ArrowLeftIcon size={24} color={colors.text.primary} />
      </TouchableOpacity>

      {/* Provider Info */}
      <View style={{ alignItems: 'center', marginTop: 40 }}>
        <View style={{
          width: 80,
          height: 80,
          borderRadius: 40,
          backgroundColor: colors.background.tertiary,
          marginBottom: 12,
        }} />

        <Text style={[
          typography.display.small,
          { color: colors.text.primary, textAlign: 'center', marginBottom: 4 }
        ]}>
          {provider.name}
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <StarIcon size={16} color={colors.warning[500]} fill={colors.warning[500]} />
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, marginLeft: 4, fontWeight: '600' }
          ]}>
            {provider.rating.average}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginLeft: 4 }
          ]}>
            ({provider.rating.count} reviews)
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
          <MapPinIcon size={14} color={colors.text.tertiary} />
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginLeft: 4 }
          ]}>
            {provider.location.address}
          </Text>
        </View>

        {/* Badges */}
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginBottom: 16 }}>
          {provider.verified && (
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: colors.success[100],
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 12,
            }}>
              <CheckCircleIcon size={12} color={colors.success[600]} />
              <Text style={[
                typography.label.small,
                { color: colors.success[700], marginLeft: 4 }
              ]}>
                Verified
              </Text>
            </View>
          )}

          {provider.badges.map((badge: string, index: number) => (
            <View key={index} style={{
              backgroundColor: colors.primary[100],
              paddingHorizontal: 8,
              paddingVertical: 4,
              borderRadius: 12,
            }}>
              <Text style={[
                typography.label.small,
                { color: colors.primary[700] }
              ]}>
                {badge}
              </Text>
            </View>
          ))}
        </View>

        {/* Stats */}
        <View style={{ flexDirection: 'row', gap: 24 }}>
          <View style={{ alignItems: 'center' }}>
            <Text style={[
              typography.body.large,
              { color: colors.text.primary, fontWeight: '700' }
            ]}>
              {provider.completedJobs}
            </Text>
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary }
            ]}>
              Jobs Done
            </Text>
          </View>

          <View style={{ alignItems: 'center' }}>
            <Text style={[
              typography.body.large,
              { color: colors.text.primary, fontWeight: '700' }
            ]}>
              {provider.responseTime}m
            </Text>
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary }
            ]}>
              Response Time
            </Text>
          </View>

          <View style={{ alignItems: 'center' }}>
            <Text style={[
              typography.body.large,
              { color: colors.text.primary, fontWeight: '700' }
            ]}>
              {new Date(provider.joinedDate).getFullYear()}
            </Text>
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary }
            ]}>
              Member Since
            </Text>
          </View>
        </View>
      </View>
    </View>
  )

  const renderTabs = () => (
    <View style={{
      flexDirection: 'row',
      backgroundColor: colors.background.primary,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      {[
        { key: 'services', label: 'Services' },
        { key: 'reviews', label: 'Reviews' },
        { key: 'about', label: 'About' }
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={{
            flex: 1,
            paddingVertical: 16,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: activeTab === tab.key ? colors.primary[500] : 'transparent',
          }}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Text style={[
            typography.label.medium,
            {
              color: activeTab === tab.key ? colors.primary[500] : colors.text.secondary,
              fontWeight: activeTab === tab.key ? '600' : '400'
            }
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )

  const renderServices = () => (
    <View style={{ padding: 16 }}>
      {services.map((service) => (
        <TouchableOpacity
          key={service.id}
          style={{
            backgroundColor: colors.background.primary,
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            borderWidth: 1,
            borderColor: colors.border.primary,
          }}
          onPress={() => navigation.navigate('ServiceDetail', { serviceId: service.id })}
        >
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <View style={{ flex: 1 }}>
              <Text style={[
                typography.body.large,
                { color: colors.text.primary, fontWeight: '600', marginBottom: 4 }
              ]}>
                {service.name}
              </Text>

              <Text style={[
                typography.body.medium,
                { color: colors.text.secondary, marginBottom: 8 }
              ]}>
                {service.description}
              </Text>

              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <StarIcon size={14} color={colors.warning[500]} fill={colors.warning[500]} />
                  <Text style={[
                    typography.body.small,
                    { color: colors.text.secondary, marginLeft: 4 }
                  ]}>
                    {service.rating.average}
                  </Text>
                </View>

                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <ClockIcon size={14} color={colors.text.tertiary} />
                  <Text style={[
                    typography.body.small,
                    { color: colors.text.secondary, marginLeft: 4 }
                  ]}>
                    {service.duration} min
                  </Text>
                </View>
              </View>
            </View>

            <View style={{ alignItems: 'flex-end' }}>
              <Text style={[
                typography.body.large,
                { color: colors.primary[600], fontWeight: '700', marginBottom: 8 }
              ]}>
                R{service.price.amount}
                {service.price.type === 'hourly' && (
                  <Text style={[typography.body.small, { fontWeight: '400' }]}>/hr</Text>
                )}
              </Text>

              <TouchableOpacity
                style={{
                  backgroundColor: colors.primary[500],
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderRadius: 20,
                }}
                onPress={() => handleBookService(service.id)}
              >
                <Text style={[typography.label.small, { color: colors.white }]}>
                  Book Now
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  )

  const renderReviews = () => (
    <View style={{ padding: 16 }}>
      {reviews.map((review) => (
        <View
          key={review.id}
          style={{
            backgroundColor: colors.background.primary,
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            borderWidth: 1,
            borderColor: colors.border.primary,
          }}
        >
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
            <View>
              <Text style={[
                typography.body.medium,
                { color: colors.text.primary, fontWeight: '600' }
              ]}>
                {review.customerName}
              </Text>
              <Text style={[
                typography.body.small,
                { color: colors.text.tertiary }
              ]}>
                {review.service} • {new Date(review.date).toLocaleDateString()}
              </Text>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {Array.from({ length: 5 }).map((_, index) => (
                <StarIcon
                  key={index}
                  size={14}
                  color={index < review.rating ? colors.warning[500] : colors.gray[300]}
                  fill={index < review.rating ? colors.warning[500] : colors.gray[300]}
                />
              ))}
            </View>
          </View>

          <Text style={[
            typography.body.medium,
            { color: colors.text.secondary, lineHeight: 20 }
          ]}>
            {review.comment}
          </Text>
        </View>
      ))}
    </View>
  )

  const renderAbout = () => (
    <View style={{ padding: 16 }}>
      <View style={{ marginBottom: 24 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
        ]}>
          About
        </Text>
        <Text style={[
          typography.body.medium,
          { color: colors.text.secondary, lineHeight: 22 }
        ]}>
          {provider.description}
        </Text>
      </View>

      <View style={{ marginBottom: 24 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
        ]}>
          Availability
        </Text>

        {Object.entries(provider.availability).map(([day, schedule]: [string, any]) => (
          <View key={day} style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingVertical: 8,
            borderBottomWidth: 1,
            borderBottomColor: colors.border.primary,
          }}>
            <Text style={[
              typography.body.medium,
              { color: colors.text.primary, textTransform: 'capitalize' }
            ]}>
              {day}
            </Text>

            <Text style={[
              typography.body.medium,
              { color: schedule.isAvailable ? colors.text.secondary : colors.text.tertiary }
            ]}>
              {schedule.isAvailable
                ? `${schedule.slots[0]?.start} - ${schedule.slots[0]?.end}`
                : 'Closed'
              }
            </Text>
          </View>
        ))}
      </View>
    </View>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'services':
        return renderServices()
      case 'reviews':
        return renderReviews()
      case 'about':
        return renderAbout()
      default:
        return renderServices()
    }
  }

  const renderContactButton = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: colors.border.primary,
      padding: 16,
    }}>
      <TouchableOpacity
        style={{
          backgroundColor: colors.primary[500],
          borderRadius: 12,
          paddingVertical: 16,
          alignItems: 'center',
          flexDirection: 'row',
          justifyContent: 'center',
        }}
        onPress={handleContactProvider}
      >
        <MessageCircleIcon size={20} color={colors.white} />
        <Text style={[
          typography.label.large,
          { color: colors.white, marginLeft: 8, fontWeight: '600' }
        ]}>
          Contact Provider
        </Text>
      </TouchableOpacity>
    </View>
  )

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderHeader()}
        {renderTabs()}
        {renderContent()}
      </ScrollView>
      {renderContactButton()}
    </SafeAreaView>
  )
}
