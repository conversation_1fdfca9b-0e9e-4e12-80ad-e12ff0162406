export const APP_CONFIG = {
  // Brand colors
  colors: {
    primary: '#FFB800',      // Energetic Yellow
    secondary: '#3C3C3C',    // Urban Charcoal  
    accent: '#00C2FF',       // Digital Blue Accent
    background: '#FFFFFF',   // Clean White
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
  },
  
  // Typography
  fonts: {
    heading: 'Poppins_700Bold',
    body: 'Inter_400Regular',
    mono: 'JetBrainsMono_400Regular',
  },
  
  // API Configuration
  api: {
    baseUrl: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3001',
    timeout: 10000,
  },
  
  // Payment providers
  payments: {
    stripe: {
      publishableKey: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || '',
    },
    payfast: {
      merchantId: process.env.EXPO_PUBLIC_PAYFAST_MERCHANT_ID || '',
      merchantKey: process.env.EXPO_PUBLIC_PAYFAST_MERCHANT_KEY || '',
      sandbox: process.env.NODE_ENV !== 'production',
    },
  },
  
  // Map configuration
  maps: {
    googleApiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || '',
    defaultRegion: {
      latitude: -26.2041,  // Soweto coordinates
      longitude: 27.8338,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    },
  },
  
  // Features flags
  features: {
    vouchers: true,
    liveTracking: true,
    pushNotifications: true,
    ratings: true,
  },
} as const;

export type AppConfig = typeof APP_CONFIG;
