"use client"

import React from 'react'
import { View } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { CheckIcon, ClockIcon, TruckIcon, PackageIcon } from 'lucide-react-native'
import { format } from '../../utils'

// Types
export interface ProgressStep {
  key: string
  label: string
  time?: string
  description?: string
}

export interface OrderProgressTrackerProps {
  currentStatus: string
  steps: ProgressStep[]
  variant?: 'default' | 'compact' | 'detailed'
  className?: string
}

// Styles
const containerStyles = tva({
  base: "bg-background-0",
  variants: {
    variant: {
      default: "p-4",
      compact: "p-2",
      detailed: "p-6",
    },
  },
})

const stepStyles = tva({
  base: "flex-row items-start",
  variants: {
    variant: {
      default: "mb-4",
      compact: "mb-2",
      detailed: "mb-6",
    },
  },
})

const iconContainerStyles = tva({
  base: "rounded-full items-center justify-center mr-3",
  variants: {
    status: {
      completed: "bg-success-500",
      current: "bg-primary-500",
      pending: "bg-outline-200",
    },
    size: {
      sm: "w-6 h-6",
      md: "w-8 h-8",
      lg: "w-10 h-10",
    },
  },
})

const lineStyles = tva({
  base: "absolute left-3 top-8 w-0.5 h-full",
  variants: {
    status: {
      completed: "bg-success-500",
      current: "bg-primary-500",
      pending: "bg-outline-200",
    },
  },
})

/**
 * OrderProgressTracker component for displaying order progress
 * 
 * Features:
 * - Visual progress indication with icons
 * - Timestamps for completed steps
 * - Current step highlighting
 * - Multiple display variants
 * - Customizable step descriptions
 */
export function OrderProgressTracker({
  currentStatus,
  steps,
  variant = 'default',
  className,
}: OrderProgressTrackerProps) {
  const getStepStatus = (stepKey: string, index: number) => {
    const currentIndex = steps.findIndex(step => step.key === currentStatus)
    
    if (index < currentIndex) return 'completed'
    if (index === currentIndex) return 'current'
    return 'pending'
  }

  const getStepIcon = (stepKey: string, status: 'completed' | 'current' | 'pending') => {
    const iconSize = variant === 'compact' ? 12 : variant === 'detailed' ? 20 : 16
    const iconColor = status === 'pending' ? 'text-outline-500' : 'text-white'

    if (status === 'completed') {
      return <CheckIcon size={iconSize} className={iconColor} />
    }

    // Default icons for common order statuses
    const icons = {
      pending: <ClockIcon size={iconSize} className={iconColor} />,
      accepted: <CheckIcon size={iconSize} className={iconColor} />,
      preparing: <PackageIcon size={iconSize} className={iconColor} />,
      ready: <PackageIcon size={iconSize} className={iconColor} />,
      picked_up: <TruckIcon size={iconSize} className={iconColor} />,
      en_route: <TruckIcon size={iconSize} className={iconColor} />,
      delivered: <CheckIcon size={iconSize} className={iconColor} />,
    }

    return icons[stepKey as keyof typeof icons] || <ClockIcon size={iconSize} className={iconColor} />
  }

  const renderStep = (step: ProgressStep, index: number) => {
    const status = getStepStatus(step.key, index)
    const isLast = index === steps.length - 1
    const iconSize = variant === 'compact' ? 'sm' : variant === 'detailed' ? 'lg' : 'md'

    return (
      <View key={step.key} className={stepStyles({ variant })}>
        <View className="relative">
          <View className={iconContainerStyles({ status, size: iconSize })}>
            {getStepIcon(step.key, status)}
          </View>
          {!isLast && (
            <View className={lineStyles({ status })} />
          )}
        </View>
        
        <View className="flex-1">
          <Text 
            className={`font-medium ${
              status === 'current' 
                ? 'text-primary-700' 
                : status === 'completed' 
                  ? 'text-success-700' 
                  : 'text-typography-600'
            }`}
          >
            {step.label}
          </Text>
          
          {step.time && status === 'completed' && (
            <Text className="text-sm text-typography-500 mt-1">
              {format.formatTime(new Date(step.time))}
            </Text>
          )}
          
          {step.description && variant === 'detailed' && (
            <Text className="text-sm text-typography-600 mt-1">
              {step.description}
            </Text>
          )}
          
          {status === 'current' && variant !== 'compact' && (
            <Text className="text-sm text-primary-600 mt-1">
              In progress...
            </Text>
          )}
        </View>
      </View>
    )
  }

  return (
    <View className={containerStyles({ variant, class: className })}>
      {steps.map((step, index) => renderStep(step, index))}
    </View>
  )
}

// Export variants
export const OrderProgressTrackerCompact = (props: Omit<OrderProgressTrackerProps, 'variant'>) => (
  <OrderProgressTracker {...props} variant="compact" />
)

export const OrderProgressTrackerDetailed = (props: Omit<OrderProgressTrackerProps, 'variant'>) => (
  <OrderProgressTracker {...props} variant="detailed" />
)
