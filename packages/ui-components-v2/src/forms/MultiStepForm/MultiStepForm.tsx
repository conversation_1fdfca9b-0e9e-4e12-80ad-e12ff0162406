"use client"

import React, { useState, useCallback, useMemo } from 'react'
import { View, Pressable, ScrollView } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { ChevronLeftIcon, ChevronRightIcon, CheckIcon } from 'lucide-react-native'

// Types
export interface FormStep {
  id: string
  title: string
  description?: string
  component: React.ComponentType<any>
  validation?: (data: any) => Promise<boolean> | boolean
  optional?: boolean
}

export interface MultiStepFormProps {
  steps: FormStep[]
  initialData?: Record<string, any>
  onComplete?: (data: Record<string, any>) => void
  onStepChange?: (stepIndex: number, stepId: string) => void
  onDataChange?: (data: Record<string, any>) => void
  allowSkip?: boolean
  showProgress?: boolean
  className?: string
  submitText?: string
  nextText?: string
  previousText?: string
  skipText?: string
}

// Styles
const containerStyles = tva({
  base: "flex-1 bg-background-0",
})

const headerStyles = tva({
  base: "bg-background-50 border-b border-outline-200 p-4",
})

const progressBarStyles = tva({
  base: "flex-row items-center mb-4",
})

const stepIndicatorStyles = tva({
  base: "w-8 h-8 rounded-full items-center justify-center mr-3",
  variants: {
    status: {
      completed: "bg-success-500",
      current: "bg-primary-500",
      upcoming: "bg-outline-200",
    },
  },
})

const stepConnectorStyles = tva({
  base: "flex-1 h-0.5 mx-2",
  variants: {
    completed: {
      true: "bg-success-500",
      false: "bg-outline-200",
    },
  },
})

const contentStyles = tva({
  base: "flex-1 p-4",
})

const footerStyles = tva({
  base: "border-t border-outline-200 p-4 flex-row justify-between items-center",
})

const buttonStyles = tva({
  base: "px-6 py-3 rounded-lg",
  variants: {
    variant: {
      primary: "bg-primary-500",
      secondary: "bg-background-100 border border-outline-300",
      ghost: "bg-transparent",
    },
    disabled: {
      true: "opacity-50",
    },
  },
})

export function MultiStepForm({
  steps,
  initialData = {},
  onComplete,
  onStepChange,
  onDataChange,
  allowSkip = false,
  showProgress = true,
  className,
  submitText = "Complete",
  nextText = "Next",
  previousText = "Previous",
  skipText = "Skip",
}: MultiStepFormProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [formData, setFormData] = useState<Record<string, any>>(initialData)
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set())
  const [validatingStep, setValidatingStep] = useState(false)

  const currentStep = steps[currentStepIndex]
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === steps.length - 1

  // Update form data
  const updateFormData = useCallback((stepData: Record<string, any>) => {
    const newData = { ...formData, ...stepData }
    setFormData(newData)
    onDataChange?.(newData)
  }, [formData, onDataChange])

  // Validate current step
  const validateCurrentStep = useCallback(async (): Promise<boolean> => {
    if (!currentStep.validation) return true
    
    setValidatingStep(true)
    try {
      const isValid = await currentStep.validation(formData)
      return isValid
    } catch (error) {
      console.error('Step validation error:', error)
      return false
    } finally {
      setValidatingStep(false)
    }
  }, [currentStep, formData])

  // Go to next step
  const goToNextStep = useCallback(async () => {
    const isValid = await validateCurrentStep()
    if (!isValid) return

    setCompletedSteps(prev => new Set([...prev, currentStepIndex]))
    
    if (isLastStep) {
      onComplete?.(formData)
    } else {
      const nextIndex = currentStepIndex + 1
      setCurrentStepIndex(nextIndex)
      onStepChange?.(nextIndex, steps[nextIndex].id)
    }
  }, [currentStepIndex, isLastStep, validateCurrentStep, formData, onComplete, onStepChange, steps])

  // Go to previous step
  const goToPreviousStep = useCallback(() => {
    if (!isFirstStep) {
      const prevIndex = currentStepIndex - 1
      setCurrentStepIndex(prevIndex)
      onStepChange?.(prevIndex, steps[prevIndex].id)
    }
  }, [currentStepIndex, isFirstStep, onStepChange, steps])

  // Skip current step
  const skipCurrentStep = useCallback(() => {
    if (!allowSkip || !currentStep.optional) return
    
    if (isLastStep) {
      onComplete?.(formData)
    } else {
      const nextIndex = currentStepIndex + 1
      setCurrentStepIndex(nextIndex)
      onStepChange?.(nextIndex, steps[nextIndex].id)
    }
  }, [allowSkip, currentStep.optional, isLastStep, currentStepIndex, formData, onComplete, onStepChange, steps])

  // Go to specific step
  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStepIndex(stepIndex)
      onStepChange?.(stepIndex, steps[stepIndex].id)
    }
  }, [steps, onStepChange])

  // Render progress bar
  const renderProgressBar = () => {
    if (!showProgress) return null

    return (
      <View className={progressBarStyles()}>
        {steps.map((step, index) => {
          const isCompleted = completedSteps.has(index)
          const isCurrent = index === currentStepIndex
          const status = isCompleted ? 'completed' : isCurrent ? 'current' : 'upcoming'

          return (
            <React.Fragment key={step.id}>
              <Pressable
                className={stepIndicatorStyles({ status })}
                onPress={() => goToStep(index)}
              >
                {isCompleted ? (
                  <CheckIcon size={16} className="text-white" />
                ) : (
                  <Text className={`text-sm font-medium ${
                    isCurrent ? 'text-white' : 'text-typography-600'
                  }`}>
                    {index + 1}
                  </Text>
                )}
              </Pressable>
              
              {index < steps.length - 1 && (
                <View className={stepConnectorStyles({ completed: isCompleted })} />
              )}
            </React.Fragment>
          )
        })}
      </View>
    )
  }

  // Render step header
  const renderStepHeader = () => (
    <View className="mb-4">
      <Text className="text-xl font-bold text-typography-900 mb-1">
        {currentStep.title}
      </Text>
      {currentStep.description && (
        <Text className="text-typography-600">
          {currentStep.description}
        </Text>
      )}
    </View>
  )

  // Render step content
  const renderStepContent = () => {
    const StepComponent = currentStep.component
    return (
      <StepComponent
        data={formData}
        onDataChange={updateFormData}
        isActive={true}
        stepIndex={currentStepIndex}
        stepId={currentStep.id}
      />
    )
  }

  // Render footer buttons
  const renderFooter = () => (
    <View className={footerStyles()}>
      <View className="flex-row">
        {!isFirstStep && (
          <Pressable
            className={buttonStyles({ variant: 'secondary' })}
            onPress={goToPreviousStep}
          >
            <View className="flex-row items-center">
              <ChevronLeftIcon size={16} className="text-typography-700 mr-1" />
              <Text className="text-typography-700 font-medium">
                {previousText}
              </Text>
            </View>
          </Pressable>
        )}
      </View>

      <View className="flex-row gap-3">
        {allowSkip && currentStep.optional && (
          <Pressable
            className={buttonStyles({ variant: 'ghost' })}
            onPress={skipCurrentStep}
          >
            <Text className="text-typography-600 font-medium">
              {skipText}
            </Text>
          </Pressable>
        )}
        
        <Pressable
          className={buttonStyles({ 
            variant: 'primary',
            disabled: validatingStep
          })}
          onPress={goToNextStep}
          disabled={validatingStep}
        >
          <View className="flex-row items-center">
            <Text className="text-white font-medium mr-1">
              {isLastStep ? submitText : nextText}
            </Text>
            {!isLastStep && (
              <ChevronRightIcon size={16} className="text-white" />
            )}
          </View>
        </Pressable>
      </View>
    </View>
  )

  return (
    <View className={containerStyles({ class: className })}>
      <View className={headerStyles()}>
        {renderProgressBar()}
        {renderStepHeader()}
      </View>

      <ScrollView className={contentStyles()}>
        {renderStepContent()}
      </ScrollView>

      {renderFooter()}
    </View>
  )
}
