import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserIcon,
  PhoneIcon,
  MessageSquareIcon,
  CheckCircleIcon,
  XCircleIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useOrdersStore } from '../../stores/ordersStore'
import type { VendorOrder } from '../../stores/ordersStore'

export function CalendarScreen() {
  const { colors, typography } = useTheme()
  const { orders, fetchOrders, acceptOrder, declineOrder } = useOrdersStore()

  const [refreshing, setRefreshing] = useState(false)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [currentMonth, setCurrentMonth] = useState(new Date())

  useEffect(() => {
    loadOrders()
  }, [])

  const loadOrders = async () => {
    try {
      await fetchOrders()
    } catch (error) {
      console.error('Failed to load orders:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadOrders()
    setRefreshing(false)
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }

    return days
  }

  const getOrdersForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    return orders.filter(order => order.scheduledDate === dateString)
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await acceptOrder(orderId)
      Alert.alert('Success', 'Order accepted successfully!')
    } catch (error) {
      Alert.alert('Error', 'Failed to accept order')
    }
  }

  const handleDeclineOrder = async (orderId: string) => {
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              await declineOrder(orderId, 'Unable to fulfill this order')
              Alert.alert('Order Declined', 'The order has been declined.')
            } catch (error) {
              Alert.alert('Error', 'Failed to decline order')
            }
          }
        }
      ]
    )
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <Text style={[typography.headline.large, { color: colors.text.primary, marginBottom: 16 }]}>
        Calendar
      </Text>

      {/* Month Navigation */}
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
        <TouchableOpacity
          style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 20,
            padding: 8,
          }}
          onPress={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))}
        >
          <ChevronLeftIcon size={20} color={colors.text.primary} />
        </TouchableOpacity>

        <Text style={[typography.headline.medium, { color: colors.text.primary, fontWeight: '600' }]}>
          {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </Text>

        <TouchableOpacity
          style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 20,
            padding: 8,
          }}
          onPress={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))}
        >
          <ChevronRightIcon size={20} color={colors.text.primary} />
        </TouchableOpacity>
      </View>
    </View>
  )

  const renderCalendar = () => {
    const days = getDaysInMonth(currentMonth)
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

    return (
      <View style={{ padding: 16 }}>
        {/* Day Headers */}
        <View style={{ flexDirection: 'row', marginBottom: 8 }}>
          {dayNames.map((dayName) => (
            <View key={dayName} style={{ flex: 1, alignItems: 'center', paddingVertical: 8 }}>
              <Text style={[typography.body.small, { color: colors.text.secondary, fontWeight: '600' }]}>
                {dayName}
              </Text>
            </View>
          ))}
        </View>

        {/* Calendar Grid */}
        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
          {days.map((day, index) => {
            if (!day) {
              return <View key={index} style={{ width: '14.28%', height: 50 }} />
            }

            const isSelected = day.toDateString() === selectedDate.toDateString()
            const isToday = day.toDateString() === new Date().toDateString()
            const ordersForDay = getOrdersForDate(day)
            const hasOrders = ordersForDay.length > 0

            return (
              <TouchableOpacity
                key={index}
                style={{
                  width: '14.28%',
                  height: 50,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: isSelected ? colors.primary[500] : 'transparent',
                  borderRadius: 8,
                  margin: 1,
                  borderWidth: isToday ? 2 : 0,
                  borderColor: isToday ? colors.primary[300] : 'transparent',
                }}
                onPress={() => setSelectedDate(day)}
              >
                <Text style={[
                  typography.body.medium,
                  {
                    color: isSelected ? colors.white : colors.text.primary,
                    fontWeight: isToday ? '700' : '500'
                  }
                ]}>
                  {day.getDate()}
                </Text>
                {hasOrders && (
                  <View style={{
                    width: 6,
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: isSelected ? colors.white : colors.primary[500],
                    marginTop: 2,
                  }} />
                )}
              </TouchableOpacity>
            )
          })}
        </View>
      </View>
    )
  }

  const renderOrdersForSelectedDate = () => {
    const ordersForDate = getOrdersForDate(selectedDate)

    return (
      <View style={{ paddingHorizontal: 16, marginBottom: 24 }}>
        <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 16 }]}>
          {formatDate(selectedDate)}
        </Text>

        {ordersForDate.length === 0 ? (
          <View style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 12,
            padding: 24,
            alignItems: 'center',
          }}>
            <CalendarIcon size={48} color={colors.text.tertiary} />
            <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginTop: 12 }]}>
              No appointments scheduled
            </Text>
            <Text style={[typography.body.small, { color: colors.text.tertiary, textAlign: 'center', marginTop: 4 }]}>
              Your schedule is free for this day
            </Text>
          </View>
        ) : (
          <View style={{ gap: 12 }}>
            {ordersForDate
              .sort((a, b) => a.scheduledTime.localeCompare(b.scheduledTime))
              .map((order) => (
                <View
                  key={order.id}
                  style={{
                    backgroundColor: colors.background.secondary,
                    borderRadius: 12,
                    padding: 16,
                    borderLeftWidth: 4,
                    borderLeftColor:
                      order.status === 'pending' ? colors.warning[500] :
                      order.status === 'accepted' ? colors.info[500] :
                      order.status === 'in_progress' ? colors.primary[500] :
                      order.status === 'completed' ? colors.success[500] : colors.error[500],
                  }}
                >
                  {/* Order Header */}
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
                    <View style={{ flex: 1 }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                        <ClockIcon size={16} color={colors.text.secondary} />
                        <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600', marginLeft: 6 }]}>
                          {order.scheduledTime}
                        </Text>
                        <View style={{
                          backgroundColor:
                            order.status === 'pending' ? colors.warning[100] :
                            order.status === 'accepted' ? colors.info[100] :
                            order.status === 'in_progress' ? colors.primary[100] :
                            order.status === 'completed' ? colors.success[100] : colors.error[100],
                          paddingHorizontal: 8,
                          paddingVertical: 2,
                          borderRadius: 6,
                          marginLeft: 8,
                        }}>
                          <Text style={[
                            typography.body.small,
                            {
                              color:
                                order.status === 'pending' ? colors.warning[700] :
                                order.status === 'accepted' ? colors.info[700] :
                                order.status === 'in_progress' ? colors.primary[700] :
                                order.status === 'completed' ? colors.success[700] : colors.error[700],
                              fontSize: 10,
                              fontWeight: '600'
                            }
                          ]}>
                            {order.status.replace('_', ' ').toUpperCase()}
                          </Text>
                        </View>
                      </View>

                      <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                        {order.serviceName}
                      </Text>
                    </View>

                    <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '700' }]}>
                      R{order.totalAmount}
                    </Text>
                  </View>

                  {/* Customer Info */}
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                    <UserIcon size={16} color={colors.text.secondary} />
                    <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 6 }]}>
                      {order.customerName}
                    </Text>
                  </View>

                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                    <MapPinIcon size={16} color={colors.text.secondary} />
                    <Text style={[typography.body.small, { color: colors.text.secondary, marginLeft: 6, flex: 1 }]}>
                      {order.serviceAddress.street}, {order.serviceAddress.city}
                    </Text>
                  </View>

                  {/* Action Buttons */}
                  <View style={{ flexDirection: 'row', gap: 8 }}>
                    {order.status === 'pending' && (
                      <>
                        <TouchableOpacity
                          style={{
                            flex: 1,
                            backgroundColor: colors.success[500],
                            borderRadius: 8,
                            padding: 12,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                          onPress={() => handleAcceptOrder(order.id)}
                        >
                          <CheckCircleIcon size={16} color={colors.white} />
                          <Text style={[typography.label.small, { color: colors.white, marginLeft: 6 }]}>
                            Accept
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            flex: 1,
                            backgroundColor: colors.background.primary,
                            borderRadius: 8,
                            padding: 12,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderWidth: 1,
                            borderColor: colors.border.primary,
                          }}
                          onPress={() => handleDeclineOrder(order.id)}
                        >
                          <XCircleIcon size={16} color={colors.text.primary} />
                          <Text style={[typography.label.small, { color: colors.text.primary, marginLeft: 6 }]}>
                            Decline
                          </Text>
                        </TouchableOpacity>
                      </>
                    )}

                    {(order.status === 'accepted' || order.status === 'in_progress') && (
                      <>
                        <TouchableOpacity
                          style={{
                            backgroundColor: colors.background.primary,
                            borderRadius: 8,
                            padding: 12,
                            borderWidth: 1,
                            borderColor: colors.border.primary,
                          }}
                        >
                          <PhoneIcon size={16} color={colors.text.primary} />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            backgroundColor: colors.background.primary,
                            borderRadius: 8,
                            padding: 12,
                            borderWidth: 1,
                            borderColor: colors.border.primary,
                          }}
                        >
                          <MessageSquareIcon size={16} color={colors.text.primary} />
                        </TouchableOpacity>
                      </>
                    )}
                  </View>
                </View>
              ))}
          </View>
        )}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderHeader()}
        {renderCalendar()}
        {renderOrdersForSelectedDate()}
      </ScrollView>
    </SafeAreaView>
  )
}
