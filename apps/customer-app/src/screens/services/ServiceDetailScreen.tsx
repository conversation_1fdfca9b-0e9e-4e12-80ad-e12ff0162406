import React, { useState, useEffect } from 'react'
import { View, Text, ScrollView, TouchableOpacity, Image, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  ArrowLeftIcon,
  StarIcon,
  ClockIcon,
  MapPinIcon,
  HeartIcon,
  ShareIcon,
  CheckCircleIcon,
  CalendarIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useServicesStore } from '../../stores/servicesStore'
import { useAuthStore } from '../../stores/authStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'ServiceDetail'>

export function ServiceDetailScreen({ navigation, route }: Props) {
  const { serviceId } = route.params
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const {
    getService,
    toggleFavorite,
    favorites,
    isLoading
  } = useServicesStore()

  const [service, setService] = useState<any>(null)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0])

  useEffect(() => {
    loadService()
  }, [serviceId])

  const loadService = async () => {
    try {
      const serviceData = await getService(serviceId)
      setService(serviceData)
    } catch (error) {
      Alert.alert('Error', 'Failed to load service details')
      navigation.goBack()
    }
  }

  const handleBookService = () => {
    if (!user) {
      Alert.alert('Sign In Required', 'Please sign in to book this service')
      return
    }

    navigation.navigate('Booking', {
      serviceId: service.id,
      providerId: service.providerId
    })
  }

  const handleToggleFavorite = () => {
    if (!user) {
      Alert.alert('Sign In Required', 'Please sign in to save favorites')
      return
    }
    toggleFavorite(serviceId)
  }

  const handleContactProvider = () => {
    navigation.navigate('Chat', {
      orderId: 'temp', // This would be handled differently in real app
      providerId: service.providerId
    })
  }

  const isFavorite = favorites.includes(serviceId)

  if (isLoading || !service) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <LoadingSpinner message="Loading service details..." />
      </SafeAreaView>
    )
  }

  const renderHeader = () => (
    <View style={{
      position: 'relative',
      height: 250,
      backgroundColor: colors.background.tertiary,
    }}>
      {service.images?.[0] && (
        <Image
          source={{ uri: service.images[0] }}
          style={{ width: '100%', height: '100%' }}
          resizeMode="cover"
        />
      )}

      {/* Header Actions */}
      <View style={{
        position: 'absolute',
        top: 50,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
      }}>
        <TouchableOpacity
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            borderRadius: 20,
            padding: 8,
          }}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeftIcon size={24} color={colors.white} />
        </TouchableOpacity>

        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              borderRadius: 20,
              padding: 8,
            }}
            onPress={handleToggleFavorite}
          >
            <HeartIcon
              size={24}
              color={isFavorite ? colors.error[500] : colors.white}
              fill={isFavorite ? colors.error[500] : 'transparent'}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              borderRadius: 20,
              padding: 8,
            }}
          >
            <ShareIcon size={24} color={colors.white} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Service Status Badge */}
      <View style={{
        position: 'absolute',
        bottom: 16,
        right: 16,
        backgroundColor: service.isActive ? colors.success[500] : colors.error[500],
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
      }}>
        <Text style={[typography.label.small, { color: colors.white }]}>
          {service.isActive ? 'Available' : 'Unavailable'}
        </Text>
      </View>
    </View>
  )

  const renderServiceInfo = () => (
    <View style={{ padding: 16 }}>
      {/* Service Title & Rating */}
      <View style={{ marginBottom: 16 }}>
        <Text style={[
          typography.display.small,
          { color: colors.text.primary, marginBottom: 8 }
        ]}>
          {service.name}
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <StarIcon size={16} color={colors.warning[500]} fill={colors.warning[500]} />
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, marginLeft: 4, fontWeight: '600' }
          ]}>
            {service.rating.average}
          </Text>
          <Text style={[
            typography.body.small,
            { color: colors.text.secondary, marginLeft: 4 }
          ]}>
            ({service.rating.count} reviews)
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <ClockIcon size={16} color={colors.text.tertiary} />
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary, marginLeft: 4 }
            ]}>
              {service.duration} minutes
            </Text>
          </View>

          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <MapPinIcon size={16} color={colors.text.tertiary} />
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary, marginLeft: 4 }
            ]}>
              {service.location.type === 'at_customer' ? 'At your location' : 'At provider location'}
            </Text>
          </View>
        </View>
      </View>

      {/* Price */}
      <View style={{
        backgroundColor: colors.primary[50],
        padding: 16,
        borderRadius: 12,
        marginBottom: 16,
      }}>
        <Text style={[
          typography.display.small,
          { color: colors.primary[700], fontWeight: '700' }
        ]}>
          R{service.price.amount}
          <Text style={[
            typography.body.medium,
            { color: colors.primary[600], fontWeight: '400' }
          ]}>
            {service.price.type === 'hourly' ? '/hour' : service.price.type === 'per_item' ? '/item' : ''}
          </Text>
        </Text>
      </View>

      {/* Description */}
      <View style={{ marginBottom: 16 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
        ]}>
          Description
        </Text>
        <Text style={[
          typography.body.medium,
          { color: colors.text.secondary, lineHeight: 22 }
        ]}>
          {service.description}
        </Text>
      </View>

      {/* Features */}
      {service.features && service.features.length > 0 && (
        <View style={{ marginBottom: 16 }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
          ]}>
            What's Included
          </Text>
          {service.features.map((feature: string, index: number) => (
            <View key={index} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
              <CheckCircleIcon size={16} color={colors.success[500]} />
              <Text style={[
                typography.body.medium,
                { color: colors.text.secondary, marginLeft: 8 }
              ]}>
                {feature}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Requirements */}
      {service.requirements && service.requirements.length > 0 && (
        <View style={{ marginBottom: 16 }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '600', marginBottom: 8 }
          ]}>
            Requirements
          </Text>
          {service.requirements.map((requirement: string, index: number) => (
            <View key={index} style={{ flexDirection: 'row', alignItems: 'flex-start', marginBottom: 6 }}>
              <Text style={[
                typography.body.medium,
                { color: colors.text.secondary, marginRight: 8 }
              ]}>
                •
              </Text>
              <Text style={[
                typography.body.medium,
                { color: colors.text.secondary, flex: 1 }
              ]}>
                {requirement}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  )

  const renderProviderInfo = () => (
    <View style={{
      margin: 16,
      backgroundColor: colors.background.primary,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.border.primary,
    }}>
      <Text style={[
        typography.body.large,
        { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
      ]}>
        Service Provider
      </Text>

      <TouchableOpacity
        style={{ flexDirection: 'row', alignItems: 'center' }}
        onPress={() => navigation.navigate('ProviderProfile', { providerId: service.providerId })}
      >
        <View style={{
          width: 50,
          height: 50,
          borderRadius: 25,
          backgroundColor: colors.background.tertiary,
          marginRight: 12,
        }} />

        <View style={{ flex: 1 }}>
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, fontWeight: '600' }
          ]}>
            {service.provider.name}
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 2 }}>
            <StarIcon size={14} color={colors.warning[500]} fill={colors.warning[500]} />
            <Text style={[
              typography.body.small,
              { color: colors.text.secondary, marginLeft: 4 }
            ]}>
              {service.provider.rating.average} • {service.provider.completedJobs} jobs completed
            </Text>
          </View>

          {service.provider.verified && (
            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 2 }}>
              <CheckCircleIcon size={14} color={colors.success[500]} />
              <Text style={[
                typography.body.small,
                { color: colors.success[600], marginLeft: 4 }
              ]}>
                Verified Provider
              </Text>
            </View>
          )}
        </View>

        <TouchableOpacity
          style={{
            backgroundColor: colors.primary[500],
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 16,
          }}
          onPress={handleContactProvider}
        >
          <Text style={[typography.label.small, { color: colors.white }]}>
            Message
          </Text>
        </TouchableOpacity>
      </TouchableOpacity>
    </View>
  )

  const renderBookingSection = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      borderTopWidth: 1,
      borderTopColor: colors.border.primary,
      padding: 16,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderWidth: 1,
            borderColor: colors.border.primary,
            borderRadius: 12,
            paddingVertical: 16,
            alignItems: 'center',
          }}
          onPress={handleContactProvider}
        >
          <Text style={[
            typography.label.medium,
            { color: colors.text.primary }
          ]}>
            Contact Provider
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 2,
            backgroundColor: service.isActive ? colors.primary[500] : colors.gray[400],
            borderRadius: 12,
            paddingVertical: 16,
            alignItems: 'center',
          }}
          onPress={handleBookService}
          disabled={!service.isActive}
        >
          <Text style={[
            typography.label.medium,
            { color: colors.white, fontWeight: '600' }
          ]}>
            {service.isActive ? 'Book Now' : 'Unavailable'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderHeader()}
        {renderServiceInfo()}
        {renderProviderInfo()}
      </ScrollView>
      {renderBookingSection()}
    </SafeAreaView>
  )
}
