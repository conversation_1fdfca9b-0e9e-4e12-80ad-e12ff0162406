import React, { useState } from 'react'
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'ForgotPassword'>

export function ForgotPasswordScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { forgotPassword, isLoading } = useAuthStore()
  
  const [email, setEmail] = useState('')

  const handleForgotPassword = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email address')
      return
    }

    try {
      await forgotPassword(email)
      Alert.alert('Success', 'Password reset email sent. Please check your inbox.')
      navigation.goBack()
    } catch (error: any) {
      Alert.alert('Error', error.message)
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.content}>
        <Text style={[typography.display.small, { color: colors.text.primary, textAlign: 'center' }]}>
          Forgot Password
        </Text>
        <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginTop: 8 }]}>
          Enter your email to receive a password reset link
        </Text>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, { borderColor: colors.border.primary, color: colors.text.primary }]}
            placeholder="Email"
            placeholderTextColor={colors.text.tertiary}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary[500] }]}
            onPress={handleForgotPassword}
            disabled={isLoading}
          >
            <Text style={[typography.label.large, { color: colors.white }]}>
              {isLoading ? 'Sending...' : 'Send Reset Link'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={[typography.body.medium, { color: colors.primary[500] }]}>
              Back to Sign In
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  form: {
    marginTop: 32,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  button: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 16,
  },
})
