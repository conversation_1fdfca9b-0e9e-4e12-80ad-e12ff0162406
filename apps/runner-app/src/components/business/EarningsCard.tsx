import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { DollarSign, TrendingUp, TrendingDown, Clock, Package } from 'lucide-react-native'

import { Card } from '../ui'
import { useTheme } from '../../providers/ThemeProvider'

export interface EarningsData {
  total: number
  deliveries: number
  hours: number
  avgPerDelivery: number
  change?: {
    amount: number
    percentage: number
    direction: 'up' | 'down'
  }
}

export interface EarningsCardProps {
  title: string
  data: EarningsData
  period: 'today' | 'week' | 'month'
  onPress?: () => void
  showTrend?: boolean
  compact?: boolean
}

export function EarningsCard({
  title,
  data,
  period,
  onPress,
  showTrend = true,
  compact = false,
}: EarningsCardProps) {
  const { theme } = useTheme()

  const formatCurrency = (amount: number) => {
    return `R${amount.toFixed(2)}`
  }

  const formatHours = (hours: number) => {
    return `${hours.toFixed(1)}h`
  }

  const getTrendColor = () => {
    if (!data.change) return theme.colors.gray[500]
    return data.change.direction === 'up' 
      ? theme.colors.success[600] 
      : theme.colors.error[600]
  }

  const getTrendIcon = () => {
    if (!data.change) return null
    return data.change.direction === 'up' ? TrendingUp : TrendingDown
  }

  const renderTrend = () => {
    if (!showTrend || !data.change) return null

    const TrendIcon = getTrendIcon()
    const trendColor = getTrendColor()

    return (
      <View style={styles.trendContainer}>
        {TrendIcon && <TrendIcon size={16} color={trendColor} />}
        <Text style={[styles.trendText, { color: trendColor }]}>
          {data.change.direction === 'up' ? '+' : ''}
          {formatCurrency(data.change.amount)} ({data.change.percentage.toFixed(1)}%)
        </Text>
      </View>
    )
  }

  const renderStats = () => {
    if (compact) {
      return (
        <View style={styles.compactStats}>
          <View style={styles.statItem}>
            <Package size={16} color={theme.colors.gray[500]} />
            <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
              {data.deliveries}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Clock size={16} color={theme.colors.gray[500]} />
            <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
              {formatHours(data.hours)}
            </Text>
          </View>
        </View>
      )
    }

    return (
      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <Package size={20} color={theme.colors.primary[600]} />
          <Text style={[styles.statNumber, { color: theme.colors.gray[900] }]}>
            {data.deliveries}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
            Deliveries
          </Text>
        </View>

        <View style={styles.statCard}>
          <Clock size={20} color={theme.colors.secondary[600]} />
          <Text style={[styles.statNumber, { color: theme.colors.gray[900] }]}>
            {formatHours(data.hours)}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
            Hours
          </Text>
        </View>

        <View style={styles.statCard}>
          <DollarSign size={20} color={theme.colors.success[600]} />
          <Text style={[styles.statNumber, { color: theme.colors.gray[900] }]}>
            {formatCurrency(data.avgPerDelivery)}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
            Avg/Delivery
          </Text>
        </View>
      </View>
    )
  }

  return (
    <Card
      variant="elevated"
      size={compact ? 'sm' : 'md'}
      onPress={onPress}
      style={styles.card}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
            {title}
          </Text>
          <Text style={[styles.period, { color: theme.colors.gray[600] }]}>
            {period === 'today' ? 'Today' : period === 'week' ? 'This Week' : 'This Month'}
          </Text>
        </View>
        
        <View style={styles.amountContainer}>
          <DollarSign size={24} color={theme.colors.success[600]} />
        </View>
      </View>

      {/* Main Amount */}
      <View style={styles.mainAmount}>
        <Text style={[styles.amount, { color: theme.colors.gray[900] }]}>
          {formatCurrency(data.total)}
        </Text>
        {renderTrend()}
      </View>

      {/* Stats */}
      {renderStats()}
    </Card>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  period: {
    fontSize: 14,
  },
  amountContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainAmount: {
    marginBottom: 20,
  },
  amount: {
    fontSize: 32,
    fontWeight: '700',
    marginBottom: 4,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  trendText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  compactStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
})

// Quick earnings summary component
export interface EarningsSummaryProps {
  todayEarnings: number
  weekEarnings: number
  monthEarnings: number
  onViewDetails?: () => void
}

export function EarningsSummary({
  todayEarnings,
  weekEarnings,
  monthEarnings,
  onViewDetails,
}: EarningsSummaryProps) {
  const { theme } = useTheme()

  return (
    <Card variant="filled" size="md" onPress={onViewDetails}>
      <View style={styles.summaryHeader}>
        <Text style={[styles.summaryTitle, { color: theme.colors.gray[900] }]}>
          Earnings Overview
        </Text>
        <DollarSign size={20} color={theme.colors.success[600]} />
      </View>

      <View style={styles.summaryGrid}>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
            Today
          </Text>
          <Text style={[styles.summaryValue, { color: theme.colors.gray[900] }]}>
            R{todayEarnings.toFixed(2)}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
            This Week
          </Text>
          <Text style={[styles.summaryValue, { color: theme.colors.gray[900] }]}>
            R{weekEarnings.toFixed(2)}
          </Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.colors.gray[600] }]}>
            This Month
          </Text>
          <Text style={[styles.summaryValue, { color: theme.colors.gray[900] }]}>
            R{monthEarnings.toFixed(2)}
          </Text>
        </View>
      </View>
    </Card>
  )
}

const summaryStyles = StyleSheet.create({
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '700',
  },
})

// Merge styles
Object.assign(styles, summaryStyles)
