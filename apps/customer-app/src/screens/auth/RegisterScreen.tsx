import React, { useState } from 'react'
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ScrollView, KeyboardAvoidingView, Platform } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { EyeIcon, EyeOffIcon, CheckCircleIcon, XCircleIcon } from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Register'>

interface ValidationErrors {
  name?: string
  email?: string
  password?: string
  confirmPassword?: string
}

export function RegisterScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { register, isLoading } = useAuthStore()

  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  // Validation functions
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string) => {
    return password.length >= 8
  }

  const validateName = (name: string) => {
    return name.trim().length >= 2
  }

  const validateForm = () => {
    const newErrors: ValidationErrors = {}

    if (!validateName(name)) {
      newErrors.name = 'Name must be at least 2 characters long'
    }

    if (!validateEmail(email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!validatePassword(password)) {
      newErrors.password = 'Password must be at least 8 characters long'
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleFieldBlur = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }))
    validateForm()
  }

  const handleRegister = async () => {
    // Mark all fields as touched
    setTouched({ name: true, email: true, password: true, confirmPassword: true })

    if (!validateForm()) {
      return
    }

    try {
      await register(email, password, name)
      // Navigation will be handled by the auth state change
    } catch (error: any) {
      Alert.alert('Registration Failed', error.message)
    }
  }

  const renderInput = (
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    field: string,
    options?: {
      secureTextEntry?: boolean
      keyboardType?: 'default' | 'email-address'
      autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
      showToggle?: boolean
      showState?: boolean
      onToggle?: () => void
    }
  ) => {
    const hasError = touched[field] && errors[field as keyof ValidationErrors]
    const isValid = touched[field] && !errors[field as keyof ValidationErrors] && value.length > 0

    return (
      <View style={{ marginBottom: 16 }}>
        <View style={[
          styles.inputContainer,
          {
            borderColor: hasError ? colors.error[500] : isValid ? colors.success[500] : colors.border.primary,
            backgroundColor: colors.background.primary,
          }
        ]}>
          <TextInput
            style={[styles.input, { color: colors.text.primary, flex: 1 }]}
            placeholder={placeholder}
            placeholderTextColor={colors.text.tertiary}
            value={value}
            onChangeText={onChangeText}
            onBlur={() => handleFieldBlur(field)}
            secureTextEntry={options?.secureTextEntry}
            keyboardType={options?.keyboardType}
            autoCapitalize={options?.autoCapitalize}
          />
          {options?.showToggle && (
            <TouchableOpacity onPress={options.onToggle} style={{ padding: 4 }}>
              {options.showState ? (
                <EyeOffIcon size={20} color={colors.text.tertiary} />
              ) : (
                <EyeIcon size={20} color={colors.text.tertiary} />
              )}
            </TouchableOpacity>
          )}
          {isValid && (
            <CheckCircleIcon size={20} color={colors.success[500]} />
          )}
          {hasError && (
            <XCircleIcon size={20} color={colors.error[500]} />
          )}
        </View>
        {hasError && (
          <Text style={[typography.body.small, { color: colors.error[500], marginTop: 4 }]}>
            {errors[field as keyof ValidationErrors]}
          </Text>
        )}
      </View>
    )
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            {/* Header */}
            <View style={{ alignItems: 'center', marginBottom: 32 }}>
              <Text style={[typography.display.small, { color: colors.text.primary, textAlign: 'center' }]}>
                Create Account
              </Text>
              <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginTop: 8 }]}>
                Join HVPPYPlug+ and discover amazing services in Soweto
              </Text>
            </View>

            {/* Form */}
            <View style={styles.form}>
              {renderInput(name, setName, 'Full Name', 'name')}

              {renderInput(email, setEmail, 'Email Address', 'email', {
                keyboardType: 'email-address',
                autoCapitalize: 'none'
              })}

              {renderInput(password, setPassword, 'Password', 'password', {
                secureTextEntry: !showPassword,
                showToggle: true,
                showState: showPassword,
                onToggle: () => setShowPassword(!showPassword)
              })}

              {renderInput(confirmPassword, setConfirmPassword, 'Confirm Password', 'confirmPassword', {
                secureTextEntry: !showConfirmPassword,
                showToggle: true,
                showState: showConfirmPassword,
                onToggle: () => setShowConfirmPassword(!showConfirmPassword)
              })}

              {/* Password Requirements */}
              <View style={{ marginBottom: 24 }}>
                <Text style={[typography.body.small, { color: colors.text.secondary, marginBottom: 8 }]}>
                  Password must contain:
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                  {password.length >= 8 ? (
                    <CheckCircleIcon size={16} color={colors.success[500]} />
                  ) : (
                    <XCircleIcon size={16} color={colors.text.tertiary} />
                  )}
                  <Text style={[
                    typography.body.small,
                    {
                      color: password.length >= 8 ? colors.success[500] : colors.text.tertiary,
                      marginLeft: 8
                    }
                  ]}>
                    At least 8 characters
                  </Text>
                </View>
              </View>

              {/* Register Button */}
              <TouchableOpacity
                style={[
                  styles.button,
                  {
                    backgroundColor: colors.primary[500],
                    opacity: isLoading ? 0.7 : 1
                  }
                ]}
                onPress={handleRegister}
                disabled={isLoading}
              >
                {isLoading ? (
                  <LoadingSpinner size="small" color={colors.white} />
                ) : (
                  <Text style={[typography.label.large, { color: colors.white }]}>
                    Create Account
                  </Text>
                )}
              </TouchableOpacity>

              {/* Sign In Link */}
              <TouchableOpacity
                style={styles.linkButton}
                onPress={() => navigation.goBack()}
              >
                <Text style={[typography.body.medium, { color: colors.text.secondary }]}>
                  Already have an account?{' '}
                  <Text style={{ color: colors.primary[500], fontWeight: '600' }}>
                    Sign In
                  </Text>
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  input: {
    fontSize: 16,
    paddingVertical: 4,
  },
  button: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    minHeight: 52,
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 24,
    paddingVertical: 8,
  },
})
