// Payment Providers Export

export * from './polar';
export * from './payfast';
export * from './mpesa';
export * from './voucher';

// Provider factory
import type { PaymentProvider, ProviderConfig } from '../types';
import { PolarPaymentProvider } from './polar';

export function createPaymentProvider(config: ProviderConfig): PaymentProvider {
  switch (config.type) {
    case 'polar':
      return new PolarPaymentProvider(config.config);
    
    case 'payfast':
      // TODO: Implement PayFast provider
      throw new Error('PayFast provider not yet implemented');
    
    case 'mpesa':
      // TODO: Implement M-Pesa provider
      throw new Error('M-Pesa provider not yet implemented');
    
    case 'voucher':
      // TODO: Implement Voucher provider
      throw new Error('Voucher provider not yet implemented');
    
    default:
      throw new Error(`Unknown payment provider type: ${(config as any).type}`);
  }
}

// Provider registry
export const PAYMENT_PROVIDERS = {
  polar: PolarPaymentProvider,
  // payfast: PayFastPaymentProvider,
  // mpesa: MPesaPaymentProvider,
  // voucher: VoucherPaymentProvider,
} as const;

// Helper to get supported providers
export function getSupportedProviders(): string[] {
  return Object.keys(PAYMENT_PROVIDERS);
}

// Helper to check if provider is supported
export function isProviderSupported(providerType: string): boolean {
  return providerType in PAYMENT_PROVIDERS;
}
