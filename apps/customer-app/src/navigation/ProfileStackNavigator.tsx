import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { ProfileStackParamList } from '../types/navigation'

// Import Screens
import { ProfileScreen } from '../screens/profile/ProfileScreen'
import { EditProfileScreen } from '../screens/profile/EditProfileScreen'
import { AddressesScreen } from '../screens/profile/AddressesScreen'
import { AddAddressScreen } from '../screens/profile/AddAddressScreen'
import { PaymentMethodsScreen } from '../screens/profile/PaymentMethodsScreen'
import { AddPaymentMethodScreen } from '../screens/profile/AddPaymentMethodScreen'
import { SettingsScreen } from '../screens/profile/SettingsScreen'
import { NotificationsScreen } from '../screens/profile/NotificationsScreen'
import { HelpCenterScreen } from '../screens/profile/HelpCenterScreen'
import { AboutScreen } from '../screens/profile/AboutScreen'

const Stack = createNativeStackNavigator<ProfileStackParamList>()

export function ProfileStackNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="ProfileMain"
        component={ProfileScreen}
      />
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          headerShown: true,
          title: 'Edit Profile',
        }}
      />
      <Stack.Screen
        name="Addresses"
        component={AddressesScreen}
        options={{
          headerShown: true,
          title: 'Addresses',
        }}
      />
      <Stack.Screen
        name="AddAddress"
        component={AddAddressScreen}
        options={{
          headerShown: true,
          title: 'Add Address',
        }}
      />
      <Stack.Screen
        name="PaymentMethods"
        component={PaymentMethodsScreen}
        options={{
          headerShown: true,
          title: 'Payment Methods',
        }}
      />
      <Stack.Screen
        name="AddPaymentMethod"
        component={AddPaymentMethodScreen}
        options={{
          headerShown: true,
          title: 'Add Payment Method',
        }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          headerShown: true,
          title: 'Settings',
        }}
      />
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          headerShown: true,
          title: 'Notifications',
        }}
      />
      <Stack.Screen
        name="HelpCenter"
        component={HelpCenterScreen}
        options={{
          headerShown: true,
          title: 'Help Center',
        }}
      />
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          headerShown: true,
          title: 'About',
        }}
      />
    </Stack.Navigator>
  )
}
