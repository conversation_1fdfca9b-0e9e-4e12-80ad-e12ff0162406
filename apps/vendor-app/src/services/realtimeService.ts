import { AppwriteService } from '@hvppyplug/mobile-services'
import { client, DATABASE_ID, COLLECTIONS } from '@hvppyplug/common'
import { notificationService } from './notificationService'

interface RealtimeSubscription {
  unsubscribe: () => void
}

interface OrderUpdate {
  orderId: string
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled'
  customerId?: string
  customerName?: string
  customerPhone?: string
  serviceName?: string
  totalAmount?: number
  scheduledDate?: string
  scheduledTime?: string
  serviceAddress?: {
    street: string
    city: string
    coordinates: { lat: number; lng: number }
  }
  estimatedTime?: string
  location?: {
    latitude: number
    longitude: number
  }
  message?: string
  timestamp: string
  paymentStatus?: 'pending' | 'paid' | 'failed'
}

interface MessageUpdate {
  chatId: string
  orderId: string
  message: {
    id: string
    text: string
    senderId: string
    senderName: string
    timestamp: string
    type: 'text' | 'image' | 'location' | 'system'
    metadata?: any
  }
}

interface ServiceUpdate {
  serviceId: string
  vendorId: string
  isActive: boolean
  price?: number
  availability?: {
    nextAvailable: string
    isAvailable: boolean
  }
  bookingCount?: number
}

interface VendorStatusUpdate {
  vendorId: string
  isOnline: boolean
  isAcceptingOrders: boolean
  currentLocation?: {
    latitude: number
    longitude: number
  }
  lastSeen: string
}

interface EarningsUpdate {
  vendorId: string
  totalEarnings: number
  todayEarnings: number
  pendingPayments: number
  completedOrders: number
  timestamp: string
}

class VendorRealtimeService {
  private subscriptions = new Map<string, RealtimeSubscription>()
  private appwriteService: AppwriteService | null = null
  private vendorId: string | null = null
  private isInitialized = false

  async initialize(vendorId?: string) {
    if (this.isInitialized) return

    try {
      // Initialize Appwrite service
      this.appwriteService = AppwriteService.getInstance()
      this.vendorId = vendorId || null
      
      // Setup global error handler for subscriptions
      this.setupErrorHandling()
      
      this.isInitialized = true
      console.log('✅ Vendor realtime service initialized for vendor:', vendorId)
    } catch (error) {
      console.error('❌ Failed to initialize vendor realtime service:', error)
      throw error
    }
  }

  private setupErrorHandling() {
    // Handle connection errors and reconnection
    client.subscribe('connection', (response) => {
      if (response.type === 'connected') {
        console.log('✅ Realtime connection established')
      } else if (response.type === 'error') {
        console.error('❌ Realtime connection error:', response.payload)
        // Attempt to reconnect after a delay
        setTimeout(() => this.reconnectSubscriptions(), 5000)
      }
    })
  }

  private async reconnectSubscriptions() {
    console.log('🔄 Attempting to reconnect realtime subscriptions...')
    // Re-establish all active subscriptions
    for (const [key, subscription] of this.subscriptions) {
      try {
        subscription.unsubscribe()
        // Re-create subscription based on key pattern
        if (key.startsWith('orders_')) {
          const vendorId = key.replace('orders_', '')
          // Re-subscribe to orders (implementation would depend on stored callback)
        }
      } catch (error) {
        console.error('Failed to reconnect subscription:', key, error)
      }
    }
  }

  // Subscribe to vendor-specific order updates
  subscribeToOrders(
    vendorId: string,
    callback: (update: OrderUpdate) => void
  ): RealtimeSubscription {
    try {
      const subscriptionKey = `orders_${vendorId}`
      
      // Subscribe to order collection changes for this vendor
      const unsubscribe = client.subscribe(
        `databases.${DATABASE_ID}.collections.${COLLECTIONS.ORDERS}.documents`,
        (response) => {
          try {
            const { events, payload } = response
            
            // Filter for orders belonging to this vendor
            if (payload.vendorId === vendorId) {
              const orderUpdate: OrderUpdate = {
                orderId: payload.$id,
                status: payload.status,
                customerId: payload.customerId,
                customerName: payload.customerName,
                customerPhone: payload.customerPhone,
                serviceName: payload.serviceName,
                totalAmount: payload.totalAmount,
                scheduledDate: payload.scheduledDate,
                scheduledTime: payload.scheduledTime,
                serviceAddress: payload.serviceAddress,
                paymentStatus: payload.paymentStatus,
                timestamp: payload.$updatedAt || payload.$createdAt,
                message: this.getStatusMessage(payload.status)
              }

              // Send local notification for important updates
              this.handleOrderNotification(orderUpdate, events)
              
              callback(orderUpdate)
            }
          } catch (error) {
            console.error('Error processing order update:', error)
          }
        }
      )

      const subscription: RealtimeSubscription = {
        unsubscribe: () => {
          unsubscribe()
          this.subscriptions.delete(subscriptionKey)
        }
      }

      this.subscriptions.set(subscriptionKey, subscription)
      return subscription

    } catch (error) {
      console.error('Failed to subscribe to orders:', error)
      return {
        unsubscribe: () => {}
      }
    }
  }

  private getStatusMessage(status: string): string {
    const messages = {
      pending: 'New order received',
      accepted: 'Order accepted',
      in_progress: 'Order in progress',
      completed: 'Order completed',
      cancelled: 'Order cancelled'
    }
    return messages[status as keyof typeof messages] || 'Order updated'
  }

  private async handleOrderNotification(update: OrderUpdate, events: string[]) {
    try {
      // Only send notifications for important events
      if (events.includes('databases.*.collections.*.documents.*.create') && update.status === 'pending') {
        await notificationService.sendLocalNotification({
          title: '🔔 New Order Received',
          body: `${update.customerName} ordered ${update.serviceName}`,
          data: { orderId: update.orderId, type: 'new_order' }
        })
      } else if (events.includes('databases.*.collections.*.documents.*.update')) {
        if (update.status === 'completed') {
          await notificationService.sendLocalNotification({
            title: '✅ Order Completed',
            body: `Order for ${update.customerName} has been completed`,
            data: { orderId: update.orderId, type: 'order_completed' }
          })
        }
      }
    } catch (error) {
      console.error('Failed to send order notification:', error)
    }
  }

  // Subscribe to chat messages for vendor
  subscribeToChatMessages(
    vendorId: string,
    callback: (update: MessageUpdate) => void
  ): RealtimeSubscription {
    try {
      const subscriptionKey = `messages_${vendorId}`
      
      // Subscribe to messages collection
      const unsubscribe = client.subscribe(
        `databases.${DATABASE_ID}.collections.messages.documents`,
        (response) => {
          try {
            const { payload } = response
            
            // Filter for messages involving this vendor
            if (payload.vendorId === vendorId && payload.senderId !== vendorId) {
              const messageUpdate: MessageUpdate = {
                chatId: payload.chatId,
                orderId: payload.orderId,
                message: {
                  id: payload.$id,
                  text: payload.text,
                  senderId: payload.senderId,
                  senderName: payload.senderName,
                  timestamp: payload.$createdAt,
                  type: payload.type || 'text',
                  metadata: payload.metadata
                }
              }

              // Send notification for new messages
              this.handleMessageNotification(messageUpdate)
              
              callback(messageUpdate)
            }
          } catch (error) {
            console.error('Error processing message update:', error)
          }
        }
      )

      const subscription: RealtimeSubscription = {
        unsubscribe: () => {
          unsubscribe()
          this.subscriptions.delete(subscriptionKey)
        }
      }

      this.subscriptions.set(subscriptionKey, subscription)
      return subscription

    } catch (error) {
      console.error('Failed to subscribe to chat messages:', error)
      return {
        unsubscribe: () => {}
      }
    }
  }

  private async handleMessageNotification(update: MessageUpdate) {
    try {
      await notificationService.sendLocalNotification({
        title: `💬 Message from ${update.message.senderName}`,
        body: update.message.text,
        data: {
          orderId: update.orderId,
          chatId: update.chatId,
          type: 'new_message'
        }
      })
    } catch (error) {
      console.error('Failed to send message notification:', error)
    }
  }

  // Subscribe to service updates for vendor
  subscribeToServiceUpdates(
    vendorId: string,
    callback: (update: ServiceUpdate) => void
  ): RealtimeSubscription {
    try {
      const subscriptionKey = `services_${vendorId}`

      const unsubscribe = client.subscribe(
        `databases.${DATABASE_ID}.collections.services.documents`,
        (response) => {
          try {
            const { payload } = response

            if (payload.vendorId === vendorId) {
              const serviceUpdate: ServiceUpdate = {
                serviceId: payload.$id,
                vendorId: payload.vendorId,
                isActive: payload.isActive,
                price: payload.price,
                availability: payload.availability,
                bookingCount: payload.bookingCount
              }

              callback(serviceUpdate)
            }
          } catch (error) {
            console.error('Error processing service update:', error)
          }
        }
      )

      const subscription: RealtimeSubscription = {
        unsubscribe: () => {
          unsubscribe()
          this.subscriptions.delete(subscriptionKey)
        }
      }

      this.subscriptions.set(subscriptionKey, subscription)
      return subscription

    } catch (error) {
      console.error('Failed to subscribe to service updates:', error)
      return {
        unsubscribe: () => {}
      }
    }
  }

  // Subscribe to vendor status updates
  subscribeToVendorStatus(
    vendorId: string,
    callback: (update: VendorStatusUpdate) => void
  ): RealtimeSubscription {
    try {
      const subscriptionKey = `vendor_status_${vendorId}`

      const unsubscribe = client.subscribe(
        `databases.${DATABASE_ID}.collections.${COLLECTIONS.VENDORS}.documents.${vendorId}`,
        (response) => {
          try {
            const { payload } = response

            const statusUpdate: VendorStatusUpdate = {
              vendorId: payload.$id,
              isOnline: payload.isOnline,
              isAcceptingOrders: payload.isAcceptingOrders,
              currentLocation: payload.currentLocation,
              lastSeen: payload.$updatedAt
            }

            callback(statusUpdate)
          } catch (error) {
            console.error('Error processing vendor status update:', error)
          }
        }
      )

      const subscription: RealtimeSubscription = {
        unsubscribe: () => {
          unsubscribe()
          this.subscriptions.delete(subscriptionKey)
        }
      }

      this.subscriptions.set(subscriptionKey, subscription)
      return subscription

    } catch (error) {
      console.error('Failed to subscribe to vendor status:', error)
      return {
        unsubscribe: () => {}
      }
    }
  }

  // Subscribe to earnings updates
  subscribeToEarningsUpdates(
    vendorId: string,
    callback: (update: EarningsUpdate) => void
  ): RealtimeSubscription {
    try {
      const subscriptionKey = `earnings_${vendorId}`

      const unsubscribe = client.subscribe(
        `databases.${DATABASE_ID}.collections.vendor_earnings.documents`,
        (response) => {
          try {
            const { payload } = response

            if (payload.vendorId === vendorId) {
              const earningsUpdate: EarningsUpdate = {
                vendorId: payload.vendorId,
                totalEarnings: payload.totalEarnings,
                todayEarnings: payload.todayEarnings,
                pendingPayments: payload.pendingPayments,
                completedOrders: payload.completedOrders,
                timestamp: payload.$updatedAt
              }

              callback(earningsUpdate)
            }
          } catch (error) {
            console.error('Error processing earnings update:', error)
          }
        }
      )

      const subscription: RealtimeSubscription = {
        unsubscribe: () => {
          unsubscribe()
          this.subscriptions.delete(subscriptionKey)
        }
      }

      this.subscriptions.set(subscriptionKey, subscription)
      return subscription

    } catch (error) {
      console.error('Failed to subscribe to earnings updates:', error)
      return {
        unsubscribe: () => {}
      }
    }
  }

  // Send real-time message
  async sendMessage(chatId: string, orderId: string, message: {
    text: string
    type?: 'text' | 'image' | 'location'
    metadata?: any
  }) {
    try {
      if (!this.appwriteService || !this.vendorId) {
        throw new Error('Realtime service not initialized')
      }

      const messageData = {
        chatId,
        orderId,
        senderId: this.vendorId,
        senderName: 'Vendor', // This should come from vendor profile
        text: message.text,
        type: message.type || 'text',
        metadata: message.metadata,
        timestamp: new Date().toISOString()
      }

      // Create message document in Appwrite
      await this.appwriteService.databases.createDocument(
        DATABASE_ID,
        'messages',
        'unique()',
        messageData
      )

      return { success: true }
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }

  // Update vendor online status
  async updateVendorStatus(status: {
    isOnline?: boolean
    isAcceptingOrders?: boolean
    currentLocation?: { latitude: number; longitude: number }
  }) {
    try {
      if (!this.appwriteService || !this.vendorId) {
        throw new Error('Realtime service not initialized')
      }

      await this.appwriteService.databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.VENDORS,
        this.vendorId,
        {
          ...status,
          lastSeen: new Date().toISOString()
        }
      )

      return { success: true }
    } catch (error) {
      console.error('Failed to update vendor status:', error)
      throw error
    }
  }

  // Unsubscribe from all subscriptions
  unsubscribeAll() {
    console.log(`🔄 Unsubscribing from ${this.subscriptions.size} realtime subscriptions`)
    this.subscriptions.forEach(subscription => {
      try {
        subscription.unsubscribe()
      } catch (error) {
        console.error('Error unsubscribing:', error)
      }
    })
    this.subscriptions.clear()
  }

  // Get subscription count for debugging
  getActiveSubscriptionsCount(): number {
    return this.subscriptions.size
  }

  // Check if service is initialized
  isServiceInitialized(): boolean {
    return this.isInitialized
  }
}

export const realtimeService = new VendorRealtimeService()
export type { OrderUpdate, MessageUpdate, ServiceUpdate, VendorStatusUpdate, EarningsUpdate }
