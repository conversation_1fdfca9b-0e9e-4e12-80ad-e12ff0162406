import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../../hooks/useTheme'
import type { AuthStackScreenProps } from '../../types/navigation'

type Props = AuthStackScreenProps<'Onboarding'>

export function OnboardingScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.content}>
        {/* Logo/Hero Section */}
        <View style={styles.heroSection}>
          <View style={[styles.logoPlaceholder, { backgroundColor: colors.primary[100] }]}>
            <Text style={[typography.display.medium, { color: colors.primary[600] }]}>
              HVPPYPlug+
            </Text>
          </View>
          <Text style={[typography.display.small, { color: colors.text.primary, textAlign: 'center', marginTop: 24 }]}>
            Vendor App
          </Text>
          <Text style={[typography.body.large, { color: colors.text.secondary, textAlign: 'center', marginTop: 12 }]}>
            Grow your business with HVPPYPlug+
          </Text>
        </View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <View style={styles.feature}>
            <View style={[styles.featureIcon, { backgroundColor: colors.success[100] }]}>
              <Text style={[typography.title.large, { color: colors.success[600] }]}>💼</Text>
            </View>
            <Text style={[typography.body.medium, { color: colors.text.primary, marginTop: 8 }]}>
              Manage your services and bookings
            </Text>
          </View>

          <View style={styles.feature}>
            <View style={[styles.featureIcon, { backgroundColor: colors.info[100] }]}>
              <Text style={[typography.title.large, { color: colors.info[600] }]}>💬</Text>
            </View>
            <Text style={[typography.body.medium, { color: colors.text.primary, marginTop: 8 }]}>
              Chat with customers in real-time
            </Text>
          </View>

          <View style={styles.feature}>
            <View style={[styles.featureIcon, { backgroundColor: colors.warning[100] }]}>
              <Text style={[typography.title.large, { color: colors.warning[600] }]}>📊</Text>
            </View>
            <Text style={[typography.body.medium, { color: colors.text.primary, marginTop: 8 }]}>
              Track earnings and performance
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.primary[500] }]}
            onPress={() => navigation.navigate('Register')}
          >
            <Text style={[typography.label.large, { color: colors.white }]}>
              Start Selling
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: colors.border.primary }]}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={[typography.label.large, { color: colors.text.primary }]}>
              I Already Have an Account
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  heroSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuresSection: {
    paddingVertical: 32,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionSection: {
    paddingBottom: 32,
  },
  primaryButton: {
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  secondaryButton: {
    height: 56,
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
