import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Eye, EyeOff, Mail, Lock, Truck } from 'lucide-react-native'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'

import type { RootStackParamList } from '../../types/navigation'
import { useAuthStore } from '../../stores/authStore'
import { useTheme } from '../../providers/ThemeProvider'

type Props = NativeStackScreenProps<RootStackParamList, 'Login'>

export function LoginScreen({ navigation }: Props) {
  const { theme } = useTheme()
  const { login, isLoading, error, clearError } = useAuthStore()
  
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [emailError, setEmailError] = useState('')
  const [passwordError, setPasswordError] = useState('')

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleLogin = async () => {
    // Clear previous errors
    clearError()
    setEmailError('')
    setPasswordError('')

    // Validation
    let hasErrors = false

    if (!email.trim()) {
      setEmailError('Email is required')
      hasErrors = true
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address')
      hasErrors = true
    }

    if (!password.trim()) {
      setPasswordError('Password is required')
      hasErrors = true
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters')
      hasErrors = true
    }

    if (hasErrors) return

    try {
      await login(email.trim().toLowerCase(), password)
      // Navigation will be handled by RootNavigator based on auth state
    } catch (error: any) {
      Alert.alert('Login Failed', error.message || 'Please check your credentials and try again.')
    }
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.white }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={[styles.logoContainer, { backgroundColor: theme.colors.primary[600] }]}>
              <Truck size={40} color={theme.colors.white} />
            </View>
            <Text style={[styles.title, { color: theme.colors.gray[900] }]}>
              Welcome Back, Runner!
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.gray[600] }]}>
              Sign in to start delivering and earning
            </Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            {/* Email Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Email Address
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: emailError ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <Mail size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="Enter your email"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text)
                    if (emailError) setEmailError('')
                  }}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                />
              </View>
              {emailError ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {emailError}
                </Text>
              ) : null}
            </View>

            {/* Password Input */}
            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: theme.colors.gray[700] }]}>
                Password
              </Text>
              <View style={[
                styles.inputContainer,
                { 
                  borderColor: passwordError ? theme.colors.error[500] : theme.colors.gray[300],
                  backgroundColor: theme.colors.gray[50]
                }
              ]}>
                <Lock size={20} color={theme.colors.gray[500]} style={styles.inputIcon} />
                <TextInput
                  style={[styles.input, { color: theme.colors.gray[900] }]}
                  placeholder="Enter your password"
                  placeholderTextColor={theme.colors.gray[500]}
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text)
                    if (passwordError) setPasswordError('')
                  }}
                  secureTextEntry={!showPassword}
                  editable={!isLoading}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff size={20} color={theme.colors.gray[500]} />
                  ) : (
                    <Eye size={20} color={theme.colors.gray[500]} />
                  )}
                </TouchableOpacity>
              </View>
              {passwordError ? (
                <Text style={[styles.errorText, { color: theme.colors.error[500] }]}>
                  {passwordError}
                </Text>
              ) : null}
            </View>

            {/* Forgot Password */}
            <TouchableOpacity
              onPress={() => navigation.navigate('ForgotPassword')}
              style={styles.forgotPassword}
              disabled={isLoading}
            >
              <Text style={[styles.forgotPasswordText, { color: theme.colors.primary[600] }]}>
                Forgot your password?
              </Text>
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              style={[
                styles.loginButton,
                { 
                  backgroundColor: isLoading ? theme.colors.gray[400] : theme.colors.primary[600],
                  opacity: isLoading ? 0.7 : 1
                }
              ]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              <Text style={[styles.loginButtonText, { color: theme.colors.white }]}>
                {isLoading ? 'Signing In...' : 'Sign In'}
              </Text>
            </TouchableOpacity>

            {/* Error Message */}
            {error && (
              <View style={[styles.errorContainer, { backgroundColor: theme.colors.error[50] }]}>
                <Text style={[styles.errorMessage, { color: theme.colors.error[700] }]}>
                  {error}
                </Text>
              </View>
            )}
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.gray[600] }]}>
              Don't have an account?{' '}
            </Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('Register')}
              disabled={isLoading}
            >
              <Text style={[styles.footerLink, { color: theme.colors.primary[600] }]}>
                Sign Up
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    fontSize: 14,
    marginTop: 6,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 32,
  },
  forgotPasswordText: {
    fontSize: 16,
    fontWeight: '600',
  },
  loginButton: {
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  errorContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorMessage: {
    fontSize: 14,
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    fontSize: 16,
  },
  footerLink: {
    fontSize: 16,
    fontWeight: '600',
  },
})
