#!/bin/bash

# HVPPYPlug+ Mobile Services Migration Script
# This script automates the migration of Expo services from ui-components-v2 to a new mobile-services package

set -e

echo "🚀 Starting Mobile Services Migration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Root directory
ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
MOBILE_SERVICES_DIR="$ROOT_DIR/packages/mobile-services"
UI_COMPONENTS_DIR="$ROOT_DIR/packages/ui-components-v2"

echo -e "${BLUE}Root directory: $ROOT_DIR${NC}"

# Step 1: Create mobile-services package structure
echo -e "\n${YELLOW}📁 Step 1: Creating mobile-services package structure...${NC}"

mkdir -p "$MOBILE_SERVICES_DIR/src/services"
mkdir -p "$MOBILE_SERVICES_DIR/src/hooks"
mkdir -p "$MOBILE_SERVICES_DIR/src/types"
mkdir -p "$MOBILE_SERVICES_DIR/src/utils"

echo -e "${GREEN}✅ Package structure created${NC}"

# Step 2: Create package.json
echo -e "\n${YELLOW}📦 Step 2: Creating package.json...${NC}"

cat > "$MOBILE_SERVICES_DIR/package.json" << 'EOF'
{
  "name": "@hvppyplug/mobile-services",
  "version": "1.0.0",
  "description": "Mobile services and hooks for HVPPYPlug+ Expo applications",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "private": true,
  "scripts": {
    "build": "tsup",
    "dev": "tsup --watch",
    "type-check": "tsc --noEmit",
    "lint": "eslint . --ext .ts,.tsx",
    "clean": "rm -rf dist"
  },
  "dependencies": {
    "expo-location": "~18.1.6",
    "expo-notifications": "~0.31.4",
    "expo-camera": "~16.1.11",
    "expo-image-picker": "~16.1.3",
    "expo-secure-store": "~14.1.1",
    "expo-local-authentication": "~16.0.5",
    "expo-task-manager": "~13.1.6",
    "expo-background-fetch": "~13.1.1",
    "expo-device": "~7.1.1",
    "expo-application": "~6.1.1",
    "expo-constants": "~17.1.2",
    "expo-network": "~7.1.1",
    "expo-file-system": "~18.1.1",
    "expo-haptics": "~14.1.1",
    "expo-linking": "~7.1.2",
    "@react-native-async-storage/async-storage": "^2.1.0",
    "appwrite": "^16.0.2",
    "react": "19.0.0",
    "react-native": "0.79.5"
  },
  "devDependencies": {
    "@babel/core": "^7.25.2",
    "@types/react": "~19.0.10",
    "typescript": "~5.8.3",
    "eslint": "^8.57.0",
    "@typescript-eslint/eslint-plugin": "^7.1.1",
    "@typescript-eslint/parser": "^7.1.1",
    "tsup": "^8.0.1"
  },
  "peerDependencies": {
    "react": ">=18.0.0",
    "react-native": ">=0.70.0"
  },
  "keywords": [
    "expo",
    "react-native",
    "mobile-services",
    "location",
    "notifications",
    "camera",
    "appwrite",
    "hvppyplug"
  ],
  "author": "HVPPYPlug Team",
  "license": "MIT",
  "files": [
    "dist",
    "src",
    "README.md"
  ],
  "exports": {
    ".": {
      "import": "./dist/index.esm.js",
      "require": "./dist/index.js",
      "types": "./dist/index.d.ts"
    }
  }
}
EOF

echo -e "${GREEN}✅ package.json created${NC}"

# Step 3: Create tsconfig.json
echo -e "\n${YELLOW}⚙️ Step 3: Creating tsconfig.json...${NC}"

cat > "$MOBILE_SERVICES_DIR/tsconfig.json" << 'EOF'
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "composite": true,
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"]
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "dist",
    "node_modules",
    "**/*.test.ts",
    "**/*.test.tsx"
  ]
}
EOF

echo -e "${GREEN}✅ tsconfig.json created${NC}"

# Step 4: Create tsup config
echo -e "\n${YELLOW}🔧 Step 4: Creating build configuration...${NC}"

cat > "$MOBILE_SERVICES_DIR/tsup.config.ts" << 'EOF'
import { defineConfig } from 'tsup'

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  external: [
    'react',
    'react-native',
    'expo-location',
    'expo-notifications',
    'expo-camera',
    'expo-image-picker',
    'expo-secure-store',
    'expo-local-authentication',
    'expo-task-manager',
    'expo-background-fetch',
    'expo-device',
    'expo-application',
    'expo-constants',
    'expo-network',
    'expo-file-system',
    'expo-haptics',
    'expo-linking',
    '@react-native-async-storage/async-storage',
    'appwrite'
  ],
})
EOF

echo -e "${GREEN}✅ Build configuration created${NC}"

# Step 5: Move services
echo -e "\n${YELLOW}📦 Step 5: Moving services...${NC}"

if [ -d "$UI_COMPONENTS_DIR/src/services" ]; then
  cp -r "$UI_COMPONENTS_DIR/src/services"/* "$MOBILE_SERVICES_DIR/src/services/"
  echo -e "${GREEN}✅ Services moved${NC}"
else
  echo -e "${RED}❌ Services directory not found${NC}"
fi

# Step 6: Move hooks
echo -e "\n${YELLOW}🪝 Step 6: Moving hooks...${NC}"

# Move specific hooks
HOOKS_TO_MOVE=("useLocation.ts" "useNotifications.ts" "useCamera.ts")

for hook in "${HOOKS_TO_MOVE[@]}"; do
  if [ -f "$UI_COMPONENTS_DIR/src/hooks/$hook" ]; then
    cp "$UI_COMPONENTS_DIR/src/hooks/$hook" "$MOBILE_SERVICES_DIR/src/hooks/"
    echo -e "${GREEN}✅ Moved $hook${NC}"
  else
    echo -e "${YELLOW}⚠️ $hook not found${NC}"
  fi
done

# Step 7: Create main index file
echo -e "\n${YELLOW}📄 Step 7: Creating main index file...${NC}"

cat > "$MOBILE_SERVICES_DIR/src/index.ts" << 'EOF'
// Services
export * from './services/LocationService'
export * from './services/NotificationService'
export * from './services/CameraService'
export * from './services/AppwriteService'

// Hooks
export * from './hooks/useLocation'
export * from './hooks/useNotifications'
export * from './hooks/useCamera'

// Types (re-export from services)
export type {
  LocationCoordinates,
  LocationData,
  LocationServiceConfig
} from './services/LocationService'

export type {
  NotificationData,
  NotificationServiceConfig
} from './services/NotificationService'

export type {
  ImageResult,
  CameraOptions,
  CameraServiceConfig
} from './services/CameraService'

export type {
  AppwriteConfig,
  UserLocation,
  NotificationToken
} from './services/AppwriteService'
EOF

echo -e "${GREEN}✅ Main index file created${NC}"

# Step 8: Create README
echo -e "\n${YELLOW}📚 Step 8: Creating README...${NC}"

cat > "$MOBILE_SERVICES_DIR/README.md" << 'EOF'
# @hvppyplug/mobile-services

Mobile services and React hooks for HVPPYPlug+ Expo applications.

## Features

- 📍 **Location Services** - Real-time GPS tracking with background support
- 🔔 **Notification Services** - Push and local notifications with deep linking
- 📷 **Camera Services** - Photo capture and gallery selection with compression
- 🔗 **Appwrite Integration** - Full BaaS integration for all services

## Installation

```bash
pnpm add @hvppyplug/mobile-services
```

## Usage

### Location Services

```typescript
import { useLocation } from '@hvppyplug/mobile-services'

const { currentLocation, startTracking, stopTracking } = useLocation({
  enableBackgroundLocation: true,
  enableAppwriteSync: true,
})
```

### Notification Services

```typescript
import { useNotifications } from '@hvppyplug/mobile-services'

const { sendOrderNotification, pushToken } = useNotifications({
  enableAppwriteSync: true,
})
```

### Camera Services

```typescript
import { useCamera } from '@hvppyplug/mobile-services'

const { takePhoto, uploadImage } = useCamera({
  enableAppwriteUpload: true,
})
```

## Documentation

See the main documentation for detailed usage instructions and examples.
EOF

echo -e "${GREEN}✅ README created${NC}"

# Step 9: Update workspace package.json
echo -e "\n${YELLOW}🔧 Step 9: Updating workspace configuration...${NC}"

# Add to workspace packages if using pnpm workspaces
if [ -f "$ROOT_DIR/pnpm-workspace.yaml" ]; then
  if ! grep -q "packages/mobile-services" "$ROOT_DIR/pnpm-workspace.yaml"; then
    echo "  - 'packages/mobile-services'" >> "$ROOT_DIR/pnpm-workspace.yaml"
    echo -e "${GREEN}✅ Added to pnpm workspace${NC}"
  fi
fi

# Step 10: Install dependencies
echo -e "\n${YELLOW}📦 Step 10: Installing dependencies...${NC}"

cd "$MOBILE_SERVICES_DIR"
pnpm install

echo -e "${GREEN}✅ Dependencies installed${NC}"

# Step 11: Build the package
echo -e "\n${YELLOW}🔨 Step 11: Building package...${NC}"

pnpm build

echo -e "${GREEN}✅ Package built successfully${NC}"

# Summary
echo -e "\n${GREEN}🎉 Migration completed successfully!${NC}"
echo -e "\n${BLUE}Next steps:${NC}"
echo -e "1. Update import statements in your apps to use '@hvppyplug/mobile-services'"
echo -e "2. Remove old service files from ui-components-v2 if no longer needed"
echo -e "3. Update app package.json files to include the new dependency"
echo -e "4. Test the migration by running your apps"

echo -e "\n${YELLOW}Files created:${NC}"
echo -e "- packages/mobile-services/package.json"
echo -e "- packages/mobile-services/tsconfig.json"
echo -e "- packages/mobile-services/tsup.config.ts"
echo -e "- packages/mobile-services/src/index.ts"
echo -e "- packages/mobile-services/README.md"
echo -e "- All service and hook files moved"

echo -e "\n${GREEN}✨ Mobile services package is ready to use!${NC}"
EOF
