// Security Types and Encryption

export interface EncryptedData {
  data: string;
  iv: string;
  algorithm: string;
  timestamp: Date;
}

export interface SecurityToken {
  token: string;
  expiresAt: Date;
  scope: string[];
  userId?: string;
}

export interface PaymentCredentials {
  apiKey: string;
  secretKey: string;
  webhookSecret?: string;
  environment: 'test' | 'live';
}

export interface SecureStorage {
  store(key: string, value: any, options?: StorageOptions): Promise<void>;
  retrieve(key: string): Promise<any>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  exists(key: string): Promise<boolean>;
}

export interface StorageOptions {
  encrypt?: boolean;
  expiresAt?: Date;
  accessGroup?: string;
  biometricPrompt?: string;
}

export interface EncryptionConfig {
  algorithm: 'AES-256-GCM' | 'AES-256-CBC';
  keyDerivation: 'PBKDF2' | 'scrypt';
  iterations?: number;
  saltLength?: number;
  ivLength?: number;
}

export interface BiometricConfig {
  enabled: boolean;
  fallbackToPasscode: boolean;
  promptMessage: string;
  cancelButtonText?: string;
  fallbackButtonText?: string;
}

export interface SecurityAudit {
  timestamp: Date;
  action: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  details?: Record<string, any>;
}

export interface SecurityPolicy {
  requireBiometric: boolean;
  sessionTimeout: number;
  maxFailedAttempts: number;
  lockoutDuration: number;
  requireStrongPasswords: boolean;
  allowRememberDevice: boolean;
  encryptSensitiveData: boolean;
}

// PCI DSS Compliance Types
export interface PCICompliance {
  tokenizeCardData: boolean;
  encryptTransmission: boolean;
  restrictDataAccess: boolean;
  monitorAccess: boolean;
  regularSecurityTesting: boolean;
  maintainSecurityPolicy: boolean;
}

export interface CardDataSecurity {
  maskCardNumber(cardNumber: string): string;
  tokenizeCard(cardData: any): Promise<string>;
  validatePCICompliance(): boolean;
  sanitizeCardData(data: any): any;
}

// Fraud Detection
export interface FraudDetection {
  riskScore: number; // 0-100
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: FraudFactor[];
  recommendation: 'approve' | 'review' | 'decline';
  confidence: number; // 0-1
}

export interface FraudFactor {
  type: string;
  description: string;
  weight: number;
  value: any;
}

// Security Headers
export interface SecurityHeaders {
  'Content-Security-Policy'?: string;
  'X-Frame-Options'?: string;
  'X-Content-Type-Options'?: string;
  'Strict-Transport-Security'?: string;
  'X-XSS-Protection'?: string;
  'Referrer-Policy'?: string;
}

// API Security
export interface APISecurityConfig {
  rateLimiting: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests?: boolean;
  };
  cors: {
    origin: string | string[];
    credentials: boolean;
    methods: string[];
  };
  headers: SecurityHeaders;
  authentication: {
    required: boolean;
    methods: ('api-key' | 'jwt' | 'oauth')[];
  };
}
