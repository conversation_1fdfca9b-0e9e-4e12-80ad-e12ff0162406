{"name": "@hvppyplug/common", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./hooks": "./src/hooks/index.ts", "./types": "./src/types/index.ts", "./utils": "./src/utils/index.ts", "./config": "./src/config/index.ts"}, "scripts": {"build": "tsc --build", "dev": "tsc --build --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"react": "18.2.0", "react-native": "0.73.6", "zustand": "^4.5.2", "@tanstack/react-query": "^5.28.4", "expo": "~50.0.14", "appwrite": "^14.0.1", "@hvppyplug/mobile-services": "workspace:*"}, "devDependencies": {"@types/react": "~18.2.79", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.73.0"}}