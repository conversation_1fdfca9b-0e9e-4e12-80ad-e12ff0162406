import React from "react";
import {
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Image,
  Button,
  ButtonText,
  Badge,
  BadgeText,
  Icon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
} from "@/ui";
import { StarIcon, HeartIcon, ShoppingCartIcon } from "@/ui";

export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  rating: number;
  reviewCount: number;
  image: string;
  images?: string[];
  category: string;
  vendor: {
    id: string;
    name: string;
    avatar?: string;
    rating: number;
  };
  inStock: boolean;
  stockCount?: number;
  freeShipping?: boolean;
  fastDelivery?: boolean;
  tags?: string[];
}

export interface ProductCardProps {
  /** Product data */
  product: Product;
  /** Card layout variant */
  variant?: "grid" | "list" | "compact";
  /** Show vendor information */
  showVendor?: boolean;
  /** Show add to cart button */
  showAddToCart?: boolean;
  /** Show wishlist button */
  showWishlist?: boolean;
  /** Wishlist state */
  isWishlisted?: boolean;
  /** Click handlers */
  onProductClick?: (product: Product) => void;
  onAddToCart?: (product: Product) => void;
  onWishlistToggle?: (product: Product, isWishlisted: boolean) => void;
  onVendorClick?: (vendorId: string) => void;
  /** Custom styling */
  className?: string;
}

const ProductCard = React.memo<ProductCardProps>(({
  product,
  variant = "grid",
  showVendor = true,
  showAddToCart = true,
  showWishlist = true,
  isWishlisted = false,
  onProductClick,
  onAddToCart,
  onWishlistToggle,
  onVendorClick,
  className = ""
}) => {
  // Theme support can be added later
  const colorMode = 'light';

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Icon
        key={index}
        as={StarIcon}
        size="2xs"
        className={index < Math.floor(rating) ? "fill-current" : "fill-gray-300"}
        color={index < Math.floor(rating) ? "#FCD34D" : "#D1D5DB"}
      />
    ));
  };

  const renderPrice = () => (
    <HStack space="sm" className="items-center">
      <Text className="text-lg font-bold text-typography-900">
        ${product.price.toFixed(2)}
      </Text>
      {product.originalPrice && product.originalPrice > product.price && (
        <>
          <Text className="text-sm text-typography-500 line-through">
            ${product.originalPrice.toFixed(2)}
          </Text>
          {product.discount && (
            <Badge className="bg-red-500">
              <BadgeText className="text-white text-xs">
                -{product.discount}%
              </BadgeText>
            </Badge>
          )}
        </>
      )}
    </HStack>
  );

  const renderTags = () => (
    <HStack space="xs" className="flex-wrap">
      {product.freeShipping && (
        <Badge className="bg-green-100">
          <BadgeText className="text-green-800 text-xs">Free Shipping</BadgeText>
        </Badge>
      )}
      {product.fastDelivery && (
        <Badge className="bg-blue-100">
          <BadgeText className="text-blue-800 text-xs">Fast Delivery</BadgeText>
        </Badge>
      )}
      {!product.inStock && (
        <Badge className="bg-red-100">
          <BadgeText className="text-red-800 text-xs">Out of Stock</BadgeText>
        </Badge>
      )}
    </HStack>
  );

  const renderVendorInfo = () => (
    <Pressable onPress={() => onVendorClick?.(product.vendor.id)}>
      <HStack space="xs" className="items-center">
        <Avatar size="2xs">
          <AvatarFallbackText>{product.vendor.name}</AvatarFallbackText>
          {product.vendor.avatar && (
            <AvatarImage source={{ uri: product.vendor.avatar }} />
          )}
        </Avatar>
        <VStack>
          <Text className="text-xs text-typography-700">{product.vendor.name}</Text>
          <HStack space="xs" className="items-center">
            {renderStars(product.vendor.rating)}
            <Text className="text-xs text-typography-500">
              ({product.vendor.rating.toFixed(1)})
            </Text>
          </HStack>
        </VStack>
      </HStack>
    </Pressable>
  );

  if (variant === "list") {
    return (
      <Card className={`mb-4 ${className}`}>
        <CardBody>
          <HStack space="md">
            <Pressable onPress={() => onProductClick?.(product)}>
              <Image
                source={{ uri: product.image }}
                alt={product.name}
                className="w-32 h-32 rounded-lg"
              />
            </Pressable>
            
            <VStack className="flex-1" space="sm">
              <Pressable onPress={() => onProductClick?.(product)}>
                <Text className="text-lg font-semibold text-typography-900" numberOfLines={2}>
                  {product.name}
                </Text>
              </Pressable>
              
              {product.description && (
                <Text className="text-sm text-typography-600" numberOfLines={2}>
                  {product.description}
                </Text>
              )}
              
              <HStack space="xs" className="items-center">
                {renderStars(product.rating)}
                <Text className="text-sm text-typography-500">
                  ({product.reviewCount})
                </Text>
              </HStack>
              
              {renderTags()}
              
              {showVendor && renderVendorInfo()}
              
              <HStack className="justify-between items-center">
                {renderPrice()}
                
                <HStack space="sm">
                  {showWishlist && (
                    <Button
                      size="sm"
                      variant="outline"
                      onPress={() => onWishlistToggle?.(product, !isWishlisted)}
                    >
                      <Icon
                        as={HeartIcon}
                        className={isWishlisted ? "fill-red-500" : "fill-none"}
                        color={isWishlisted ? "#EF4444" : colorMode === "light" ? "#374151" : "#D1D5DB"}
                      />
                    </Button>
                  )}
                  
                  {showAddToCart && (
                    <Button
                      size="sm"
                      onPress={() => onAddToCart?.(product)}
                      isDisabled={!product.inStock}
                    >
                      <Icon as={ShoppingCartIcon} className="mr-2" />
                      <ButtonText>Add to Cart</ButtonText>
                    </Button>
                  )}
                </HStack>
              </HStack>
            </VStack>
          </HStack>
        </CardBody>
      </Card>
    );
  }

  // Grid variant (default)
  return (
    <Card className={`${className}`}>
      <CardBody>
        <VStack space="sm">
          <VStack className="relative">
            <Pressable onPress={() => onProductClick?.(product)}>
              <Image
                source={{ uri: product.image }}
                alt={product.name}
                className="w-full h-48 rounded-lg"
              />
            </Pressable>
            
            {showWishlist && (
              <Button
                size="sm"
                variant="outline"
                className="absolute top-2 right-2 bg-white/80"
                onPress={() => onWishlistToggle?.(product, !isWishlisted)}
              >
                <Icon
                  as={HeartIcon}
                  size="sm"
                  className={isWishlisted ? "fill-red-500" : "fill-none"}
                  color={isWishlisted ? "#EF4444" : "#6B7280"}
                />
              </Button>
            )}
          </VStack>
          
          <Pressable onPress={() => onProductClick?.(product)}>
            <Text className="text-base font-semibold text-typography-900" numberOfLines={2}>
              {product.name}
            </Text>
          </Pressable>
          
          <HStack space="xs" className="items-center">
            {renderStars(product.rating)}
            <Text className="text-sm text-typography-500">
              ({product.reviewCount})
            </Text>
          </HStack>
          
          {renderTags()}
          
          {renderPrice()}
          
          {showVendor && renderVendorInfo()}
          
          {showAddToCart && (
            <Button
              size="sm"
              onPress={() => onAddToCart?.(product)}
              isDisabled={!product.inStock}
              className="w-full"
            >
              <Icon as={ShoppingCartIcon} className="mr-2" />
              <ButtonText>{product.inStock ? "Add to Cart" : "Out of Stock"}</ButtonText>
            </Button>
          )}
        </VStack>
      </CardBody>
    </Card>
  );
});

ProductCard.displayName = "ProductCard";
export default ProductCard;