// Validation Types and Schemas

import { z } from 'zod';

// Base validation schemas
export const CurrencySchema = z.enum(['ZAR', 'USD', 'EUR', 'GBP']);

export const AmountSchema = z.object({
  value: z.number().positive('Amount must be positive'),
  currency: CurrencySchema,
  formatted: z.string()
});

export const PaymentMethodSchema = z.enum([
  'stripe_card',
  'payfast_eft',
  'payfast_card',
  'mpesa',
  'voucher_1voucher',
  'voucher_ott',
  'cash_on_delivery'
]);

// Card validation
export const CardNumberSchema = z.string()
  .min(13, 'Card number must be at least 13 digits')
  .max(19, 'Card number must be at most 19 digits')
  .regex(/^\d+$/, 'Card number must contain only digits');

export const CardExpirySchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(new Date().getFullYear())
});

export const CardCVCSchema = z.string()
  .min(3, 'CVC must be at least 3 digits')
  .max(4, 'CVC must be at most 4 digits')
  .regex(/^\d+$/, 'CVC must contain only digits');

export const CardValidationSchema = z.object({
  number: CardNumberSchema,
  expiry: CardExpirySchema,
  cvc: CardCVCSchema,
  holderName: z.string().min(2, 'Cardholder name is required')
});

// Phone number validation (South African format)
export const PhoneNumberSchema = z.string()
  .regex(/^(\+27|0)[6-8][0-9]{8}$/, 'Invalid South African phone number format');

// Voucher validation
export const VoucherCodeSchema = z.string()
  .min(8, 'Voucher code must be at least 8 characters')
  .max(20, 'Voucher code must be at most 20 characters')
  .regex(/^[A-Z0-9]+$/, 'Voucher code must contain only uppercase letters and numbers');

export const VoucherValidationSchema = z.object({
  code: VoucherCodeSchema,
  type: z.enum(['ott', '1voucher']),
  amount: AmountSchema.optional()
});

// Payment request validation
export const PaymentRequestSchema = z.object({
  amount: AmountSchema,
  paymentMethod: PaymentMethodSchema,
  customerId: z.string().uuid('Invalid customer ID'),
  orderId: z.string().uuid('Invalid order ID'),
  description: z.string().optional(),
  metadata: z.record(z.string()).optional(),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional()
});

// M-Pesa validation
export const MPesaRequestSchema = z.object({
  phoneNumber: PhoneNumberSchema,
  amount: AmountSchema,
  accountReference: z.string().min(1).max(12).optional(),
  transactionDesc: z.string().min(1).max(13).optional()
});

// PayFast validation
export const PayFastRequestSchema = z.object({
  amount: AmountSchema,
  itemName: z.string().min(1, 'Item name is required'),
  itemDescription: z.string().optional(),
  returnUrl: z.string().url('Invalid return URL'),
  cancelUrl: z.string().url('Invalid cancel URL'),
  notifyUrl: z.string().url('Invalid notify URL'),
  nameFirst: z.string().optional(),
  nameLast: z.string().optional(),
  emailAddress: z.string().email().optional()
});

// Stripe validation
export const StripePaymentMethodSchema = z.object({
  type: z.literal('card'),
  card: z.object({
    number: CardNumberSchema,
    expMonth: z.number().min(1).max(12),
    expYear: z.number().min(new Date().getFullYear()),
    cvc: CardCVCSchema
  }),
  billingDetails: z.object({
    name: z.string().optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    address: z.object({
      line1: z.string().optional(),
      line2: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postalCode: z.string().optional(),
      country: z.string().length(2).optional()
    }).optional()
  }).optional()
});

// Validation interfaces
export interface PaymentValidationSchema {
  validatePaymentRequest(data: any): ValidationResult;
  validateCardData(data: any): ValidationResult;
  validatePhoneNumber(phone: string): ValidationResult;
  validateVoucherCode(code: string, type: 'ott' | '1voucher'): ValidationResult;
  validateAmount(amount: any, currency: string): ValidationResult;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
  sanitizedData?: any;
}

export interface ValidationError {
  field: string;
  code: string;
  message: string;
  value?: any;
}

export interface ValidationWarning {
  field: string;
  code: string;
  message: string;
  value?: any;
}

// Card validation utilities
export interface CardValidation {
  validateCardNumber(number: string): CardValidationResult;
  validateExpiryDate(month: number, year: number): ValidationResult;
  validateCVC(cvc: string, cardType?: string): ValidationResult;
  getCardType(number: string): CardType;
  formatCardNumber(number: string): string;
  maskCardNumber(number: string): string;
}

export interface CardValidationResult extends ValidationResult {
  cardType?: CardType;
  formatted?: string;
  masked?: string;
}

export type CardType = 
  | 'visa'
  | 'mastercard'
  | 'american-express'
  | 'diners-club'
  | 'discover'
  | 'jcb'
  | 'unknown';

// Phone validation utilities
export interface PhoneValidation {
  validateSouthAfricanNumber(phone: string): ValidationResult;
  formatPhoneNumber(phone: string): string;
  normalizePhoneNumber(phone: string): string;
  getCarrier(phone: string): string | null;
}

// Voucher validation utilities
export interface VoucherValidation {
  validateVoucherFormat(code: string, type: 'ott' | '1voucher'): ValidationResult;
  checkVoucherChecksum(code: string): boolean;
  normalizeVoucherCode(code: string): string;
}

// Custom validation rules
export interface CustomValidationRule {
  name: string;
  validator: (value: any, context?: any) => ValidationResult;
  message: string;
  async?: boolean;
}

// Validation context
export interface ValidationContext {
  userId?: string;
  paymentMethod?: string;
  currency?: string;
  amount?: number;
  country?: string;
  locale?: string;
  testMode?: boolean;
}
