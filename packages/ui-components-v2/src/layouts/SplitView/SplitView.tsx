"use client"

import React, { useState, useCallback, useRef } from 'react'
import { View, Pressable, PanGestureHandler, State } from 'react-native'
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  runOnJS,
  withSpring,
} from 'react-native-reanimated'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { GripVerticalIcon, GripHorizontalIcon, MaximizeIcon, MinimizeIcon } from 'lucide-react-native'
import { usePlatform } from '../../hooks'

// Types
export interface SplitViewProps {
  children: [React.ReactNode, React.ReactNode]
  direction?: 'horizontal' | 'vertical'
  initialSplit?: number // 0-1 (percentage)
  minSize?: number // minimum size in pixels
  maxSize?: number // maximum size in pixels
  resizable?: boolean
  collapsible?: boolean
  defaultCollapsed?: 'first' | 'second' | 'none'
  onSplitChange?: (split: number) => void
  onCollapse?: (collapsed: 'first' | 'second' | 'none') => void
  className?: string
  resizerClassName?: string
  firstPaneClassName?: string
  secondPaneClassName?: string
}

// Styles
const containerStyles = tva({
  base: "flex-1",
  variants: {
    direction: {
      horizontal: "flex-row",
      vertical: "flex-col",
    },
  },
})

const paneStyles = tva({
  base: "bg-background-0 overflow-hidden",
  variants: {
    collapsed: {
      true: "hidden",
    },
  },
})

const resizerStyles = tva({
  base: "bg-outline-200 hover:bg-outline-300 active:bg-primary-300 flex items-center justify-center",
  variants: {
    direction: {
      horizontal: "w-1 cursor-col-resize",
      vertical: "h-1 cursor-row-resize",
    },
    resizable: {
      false: "pointer-events-none",
    },
  },
})

const resizerHandleStyles = tva({
  base: "bg-outline-400 rounded-full",
  variants: {
    direction: {
      horizontal: "w-1 h-8",
      vertical: "h-1 w-8",
    },
  },
})

const collapseButtonStyles = tva({
  base: "absolute z-10 bg-background-0 border border-outline-300 rounded p-1 shadow-sm",
  variants: {
    direction: {
      horizontal: "top-2 -translate-x-1/2",
      vertical: "left-2 -translate-y-1/2",
    },
    position: {
      start: "",
      end: "",
    },
  },
})

export function SplitView({
  children,
  direction = 'horizontal',
  initialSplit = 0.5,
  minSize = 100,
  maxSize,
  resizable = true,
  collapsible = false,
  defaultCollapsed = 'none',
  onSplitChange,
  onCollapse,
  className,
  resizerClassName,
  firstPaneClassName,
  secondPaneClassName,
}: SplitViewProps) {
  const [split, setSplit] = useState(initialSplit)
  const [collapsed, setCollapsed] = useState(defaultCollapsed)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<View>(null)
  const { isWeb } = usePlatform()

  // Animated values
  const translateX = useSharedValue(0)
  const translateY = useSharedValue(0)

  // Calculate sizes
  const calculateSizes = useCallback((containerSize: number, currentSplit: number) => {
    if (collapsed === 'first') {
      return { firstSize: 0, secondSize: containerSize }
    }
    if (collapsed === 'second') {
      return { firstSize: containerSize, secondSize: 0 }
    }

    let firstSize = containerSize * currentSplit
    let secondSize = containerSize * (1 - currentSplit)

    // Apply min/max constraints
    if (firstSize < minSize) {
      firstSize = minSize
      secondSize = containerSize - firstSize
    }
    if (maxSize && firstSize > maxSize) {
      firstSize = maxSize
      secondSize = containerSize - firstSize
    }
    if (secondSize < minSize) {
      secondSize = minSize
      firstSize = containerSize - secondSize
    }
    if (maxSize && secondSize > maxSize) {
      secondSize = maxSize
      firstSize = containerSize - secondSize
    }

    return { firstSize, secondSize }
  }, [collapsed, minSize, maxSize])

  // Handle split change
  const handleSplitChange = useCallback((newSplit: number) => {
    setSplit(newSplit)
    onSplitChange?.(newSplit)
  }, [onSplitChange])

  // Handle collapse
  const handleCollapse = useCallback((newCollapsed: 'first' | 'second' | 'none') => {
    setCollapsed(newCollapsed)
    onCollapse?.(newCollapsed)
  }, [onCollapse])

  // Toggle collapse
  const toggleCollapse = useCallback((pane: 'first' | 'second') => {
    const newCollapsed = collapsed === pane ? 'none' : pane
    handleCollapse(newCollapsed)
  }, [collapsed, handleCollapse])

  // Gesture handler for resizing
  const gestureHandler = useAnimatedGestureHandler({
    onStart: () => {
      runOnJS(setIsDragging)(true)
    },
    onActive: (event) => {
      if (direction === 'horizontal') {
        translateX.value = event.translationX
      } else {
        translateY.value = event.translationY
      }
    },
    onEnd: (event) => {
      runOnJS(setIsDragging)(false)
      
      // Calculate new split based on translation
      const translation = direction === 'horizontal' ? event.translationX : event.translationY
      
      // Get container size (this would need to be measured in real implementation)
      const containerSize = direction === 'horizontal' ? 800 : 600 // placeholder
      const currentFirstSize = containerSize * split
      const newFirstSize = currentFirstSize + translation
      const newSplit = Math.max(0, Math.min(1, newFirstSize / containerSize))
      
      runOnJS(handleSplitChange)(newSplit)
      
      // Reset animation values
      translateX.value = withSpring(0)
      translateY.value = withSpring(0)
    },
  })

  // Animated style for resizer
  const resizerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: direction === 'horizontal' ? translateX.value : 0 },
      { translateY: direction === 'vertical' ? translateY.value : 0 },
    ],
  }))

  // Render collapse button
  const renderCollapseButton = (pane: 'first' | 'second') => {
    if (!collapsible) return null

    const isCollapsed = collapsed === pane
    const IconComponent = isCollapsed ? MaximizeIcon : MinimizeIcon

    return (
      <Pressable
        className={collapseButtonStyles({ 
          direction,
          position: pane === 'first' ? 'start' : 'end'
        })}
        onPress={() => toggleCollapse(pane)}
        style={{
          [direction === 'horizontal' ? 'left' : 'top']: 
            pane === 'first' ? '100%' : 0,
        }}
      >
        <IconComponent size={16} className="text-typography-600" />
      </Pressable>
    )
  }

  // Render resizer
  const renderResizer = () => {
    if (!resizable || collapsed !== 'none') return null

    const GripIcon = direction === 'horizontal' ? GripVerticalIcon : GripHorizontalIcon

    if (isWeb) {
      // Web implementation with mouse events
      return (
        <div
          className={`${resizerStyles({ direction, resizable })} ${resizerClassName || ''}`}
          onMouseDown={(e) => {
            // Web drag implementation would go here
            e.preventDefault()
          }}
        >
          <div className={resizerHandleStyles({ direction })} />
        </div>
      )
    }

    // React Native implementation with PanGestureHandler
    return (
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View
          className={`${resizerStyles({ direction, resizable })} ${resizerClassName || ''}`}
          style={resizerAnimatedStyle}
        >
          <View className={resizerHandleStyles({ direction })} />
          <GripIcon 
            size={16} 
            className={`text-typography-500 absolute ${isDragging ? 'text-primary-600' : ''}`} 
          />
        </Animated.View>
      </PanGestureHandler>
    )
  }

  // Calculate current sizes (placeholder - would need actual measurement)
  const containerSize = direction === 'horizontal' ? 800 : 600
  const { firstSize, secondSize } = calculateSizes(containerSize, split)

  return (
    <View 
      ref={containerRef}
      className={containerStyles({ direction, class: className })}
    >
      {/* First Pane */}
      <View
        className={`${paneStyles({ collapsed: collapsed === 'first' })} ${firstPaneClassName || ''}`}
        style={{
          [direction === 'horizontal' ? 'width' : 'height']: 
            collapsed === 'first' ? 0 : 
            collapsed === 'second' ? '100%' : 
            `${split * 100}%`,
        }}
      >
        {children[0]}
        {renderCollapseButton('first')}
      </View>

      {/* Resizer */}
      {renderResizer()}

      {/* Second Pane */}
      <View
        className={`${paneStyles({ collapsed: collapsed === 'second' })} ${secondPaneClassName || ''}`}
        style={{
          [direction === 'horizontal' ? 'width' : 'height']: 
            collapsed === 'second' ? 0 : 
            collapsed === 'first' ? '100%' : 
            `${(1 - split) * 100}%`,
        }}
      >
        {children[1]}
        {renderCollapseButton('second')}
      </View>
    </View>
  )
}

// Predefined split view layouts
export const HorizontalSplit = (props: Omit<SplitViewProps, 'direction'>) => (
  <SplitView {...props} direction="horizontal" />
)

export const VerticalSplit = (props: Omit<SplitViewProps, 'direction'>) => (
  <SplitView {...props} direction="vertical" />
)

export const MasterDetail = (props: Omit<SplitViewProps, 'direction' | 'initialSplit'>) => (
  <SplitView {...props} direction="horizontal" initialSplit={0.3} />
)

export const CodeEditor = (props: Omit<SplitViewProps, 'direction' | 'initialSplit'>) => (
  <SplitView {...props} direction="vertical" initialSplit={0.7} />
)

// Utility functions
export const splitViewUtils = {
  // Calculate split percentage from pixel values
  calculateSplit: (firstSize: number, totalSize: number): number => {
    return Math.max(0, Math.min(1, firstSize / totalSize))
  },

  // Calculate pixel size from split percentage
  calculateSize: (split: number, totalSize: number): number => {
    return totalSize * split
  },

  // Constrain split within min/max bounds
  constrainSplit: (
    split: number, 
    totalSize: number, 
    minSize: number, 
    maxSize?: number
  ): number => {
    const minSplit = minSize / totalSize
    const maxSplit = maxSize ? maxSize / totalSize : 1

    return Math.max(minSplit, Math.min(maxSplit, split))
  },
}
