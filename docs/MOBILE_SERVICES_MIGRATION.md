# Mobile Services Migration Guide

This guide explains the automated migration of Expo services from the UI components library to a dedicated mobile services package.

## 🎯 Migration Overview

The migration restructures the codebase by:

1. **Creating** a new `@hvppyplug/mobile-services` package
2. **Moving** all Expo-related services and hooks from `ui-components-v2`
3. **Updating** import statements across the entire codebase
4. **Configuring** proper build and TypeScript settings
5. **Validating** the migration for correctness

## 🚀 Quick Migration

### Automated Migration (Recommended)

Run the complete migration with a single command:

```bash
# From the root directory
./scripts/run-migration.sh
```

This script will:
- ✅ Create the mobile-services package
- ✅ Move all service files and hooks
- ✅ Update import statements
- ✅ Install dependencies
- ✅ Build the package
- ✅ Validate the migration
- ✅ Update app dependencies

### Manual Migration (Advanced)

If you prefer to run steps individually:

```bash
# Step 1: Create package and move files
./scripts/migrate-mobile-services.sh

# Step 2: Update import statements
node scripts/update-imports.js

# Step 3: Install dependencies
pnpm install

# Step 4: Build the package
cd packages/mobile-services && pnpm build

# Step 5: Validate migration
node scripts/validate-migration.js
```

## 📦 What Gets Migrated

### Services Moved
- `LocationService` - GPS tracking and geolocation
- `NotificationService` - Push and local notifications
- `CameraService` - Photo capture and gallery access
- `AppwriteService` - Backend integration

### Hooks Moved
- `useLocation` - Location tracking hook
- `useNotifications` - Notification management hook
- `useCamera` - Camera operations hook

### Dependencies Moved
All Expo plugin dependencies are moved to the mobile-services package:
- expo-location
- expo-notifications
- expo-camera
- expo-image-picker
- expo-secure-store
- expo-local-authentication
- expo-task-manager
- expo-background-fetch
- And more...

## 🔄 Import Changes

### Before Migration
```typescript
// Old imports from ui-components-v2
import { LocationService, useLocation } from '@hvppyplug/compound-components'
import { NotificationService } from '../services/NotificationService'
import { useCamera } from '../hooks/useCamera'
```

### After Migration
```typescript
// New imports from mobile-services
import { 
  LocationService, 
  NotificationService,
  useLocation, 
  useCamera 
} from '@hvppyplug/mobile-services'
```

## 📁 New Package Structure

```
packages/mobile-services/
├── package.json              # Package configuration
├── tsconfig.json             # TypeScript configuration
├── tsup.config.ts            # Build configuration
├── README.md                 # Package documentation
└── src/
    ├── index.ts              # Main exports
    ├── services/             # Service implementations
    │   ├── LocationService/
    │   ├── NotificationService/
    │   ├── CameraService/
    │   └── AppwriteService/
    └── hooks/                # React hooks
        ├── useLocation.ts
        ├── useNotifications.ts
        └── useCamera.ts
```

## 🔧 Configuration Files

### package.json
- Includes all Expo plugin dependencies
- Configured for both CJS and ESM builds
- Proper peer dependencies for React/React Native

### tsconfig.json
- Extends root TypeScript configuration
- Configured for declaration file generation
- Optimized for mobile development

### tsup.config.ts
- Builds both CommonJS and ESM formats
- Generates TypeScript declarations
- Excludes external dependencies

## ✅ Validation

The migration includes comprehensive validation:

### Automated Checks
- ✅ Package structure validation
- ✅ TypeScript configuration check
- ✅ Export validation
- ✅ Build verification
- ✅ Import statement validation
- ✅ Cleanup verification

### Manual Testing
After migration, test your applications:

```bash
# Test customer app
cd apps/customer-app
pnpm dev

# Test vendor app
cd apps/vendor-app
pnpm dev

# Test runner app
cd apps/runner-app
pnpm dev
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Import Errors
**Problem**: `Cannot resolve module '@hvppyplug/mobile-services'`

**Solution**:
```bash
# Ensure dependencies are installed
pnpm install

# Rebuild the mobile-services package
cd packages/mobile-services && pnpm build
```

#### 2. TypeScript Errors
**Problem**: Type definitions not found

**Solution**:
```bash
# Regenerate declaration files
cd packages/mobile-services
pnpm build
```

#### 3. Build Failures
**Problem**: Package fails to build

**Solution**:
```bash
# Clean and rebuild
cd packages/mobile-services
pnpm clean
pnpm install
pnpm build
```

#### 4. Missing Dependencies
**Problem**: Expo plugins not found

**Solution**:
```bash
# Install missing dependencies
cd packages/mobile-services
pnpm install

# Or reinstall all dependencies
pnpm install --force
```

### Rollback Procedure

If you need to rollback the migration:

1. **Restore from Git**:
   ```bash
   git checkout HEAD~1 -- packages/ui-components-v2/src/services
   git checkout HEAD~1 -- packages/ui-components-v2/src/hooks
   ```

2. **Remove mobile-services package**:
   ```bash
   rm -rf packages/mobile-services
   ```

3. **Restore old imports**:
   ```bash
   # Manually update import statements back to old paths
   ```

## 📊 Migration Benefits

### Before Migration
- ❌ Services mixed with UI components
- ❌ Platform-specific dependencies in UI library
- ❌ Tight coupling between UI and services
- ❌ Difficult to version services independently

### After Migration
- ✅ Clear separation of concerns
- ✅ Platform-specific dependencies isolated
- ✅ Loose coupling between UI and services
- ✅ Independent versioning and maintenance
- ✅ Reusable across different projects
- ✅ Better tree-shaking and bundle optimization

## 🔮 Future Enhancements

The new architecture enables:

1. **Independent Versioning**: Services can be versioned separately from UI components
2. **Cross-Project Reuse**: Services can be used in other React Native projects
3. **Better Testing**: Services can be tested in isolation
4. **Plugin Architecture**: Easy to add new mobile services
5. **Performance**: Better tree-shaking and code splitting

## 📚 Additional Resources

- [Expo Plugins Guide](./EXPO_PLUGINS_GUIDE.md)
- [Mobile Services Package README](../packages/mobile-services/README.md)
- [Appwrite Integration Guide](./APPWRITE_INTEGRATION.md)

## 🤝 Contributing

When adding new mobile services:

1. Add service to `packages/mobile-services/src/services/`
2. Create corresponding React hook in `packages/mobile-services/src/hooks/`
3. Export from main index file
4. Update documentation
5. Add tests

## 📝 Changelog

### v1.0.0 - Initial Migration
- Created mobile-services package
- Migrated all Expo services and hooks
- Updated import statements across codebase
- Added comprehensive build and validation scripts
