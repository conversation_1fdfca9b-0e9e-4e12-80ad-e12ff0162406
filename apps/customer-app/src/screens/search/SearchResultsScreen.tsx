import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useTheme } from '../../hooks/useTheme'

export function SearchResultsScreen() {
  const { colors, typography } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.content}>
        <Text style={[typography.display.small, { color: colors.text.primary, textAlign: 'center' }]}>
          Search Results
        </Text>
        <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginTop: 16 }]}>
          Coming Soon
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 24 },
})
