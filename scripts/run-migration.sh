#!/bin/bash

# HVP<PERSON><PERSON>lug+ Complete Migration Script
# This script orchestrates the complete migration of mobile services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

echo -e "${CYAN}🚀 HVPPYPlug+ Mobile Services Migration${NC}"
echo -e "${CYAN}=====================================${NC}"
echo -e "${BLUE}Root directory: $ROOT_DIR${NC}\n"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run with error handling
run_step() {
    local step_name="$1"
    local command="$2"
    local success_message="$3"
    local error_message="$4"
    
    echo -e "${YELLOW}📋 $step_name${NC}"
    
    if eval "$command"; then
        echo -e "${GREEN}✅ $success_message${NC}\n"
        return 0
    else
        echo -e "${RED}❌ $error_message${NC}\n"
        return 1
    fi
}

# Pre-flight checks
echo -e "${YELLOW}🔍 Pre-flight checks...${NC}"

# Check if pnpm is installed
if ! command_exists pnpm; then
    echo -e "${RED}❌ pnpm is not installed. Please install pnpm first.${NC}"
    echo -e "${BLUE}Install with: npm install -g pnpm${NC}"
    exit 1
fi

# Check if Node.js is installed
if ! command_exists node; then
    echo -e "${RED}❌ Node.js is not installed. Please install Node.js first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites are installed${NC}\n"

# Step 1: Run the migration script
run_step \
    "Step 1: Creating mobile-services package and migrating files" \
    "bash '$SCRIPT_DIR/migrate-mobile-services.sh'" \
    "Mobile services package created and files migrated successfully" \
    "Failed to create mobile services package"

# Step 2: Update import statements
run_step \
    "Step 2: Updating import statements across the codebase" \
    "node '$SCRIPT_DIR/update-imports.js'" \
    "Import statements updated successfully" \
    "Failed to update import statements"

# Step 3: Install dependencies in root
run_step \
    "Step 3: Installing dependencies in workspace" \
    "cd '$ROOT_DIR' && pnpm install" \
    "Dependencies installed successfully" \
    "Failed to install dependencies"

# Step 4: Build the mobile-services package
run_step \
    "Step 4: Building mobile-services package" \
    "cd '$ROOT_DIR/packages/mobile-services' && pnpm build" \
    "Mobile services package built successfully" \
    "Failed to build mobile services package"

# Step 5: Validate the migration
run_step \
    "Step 5: Validating migration" \
    "node '$SCRIPT_DIR/validate-migration.js'" \
    "Migration validation passed" \
    "Migration validation failed"

# Step 6: Update app dependencies
echo -e "${YELLOW}📱 Step 6: Updating app dependencies...${NC}"

APPS=("customer-app" "vendor-app" "runner-app")

for app in "${APPS[@]}"; do
    APP_DIR="$ROOT_DIR/apps/$app"
    if [ -d "$APP_DIR" ]; then
        echo -e "${BLUE}  Updating $app...${NC}"
        
        # Add mobile-services dependency if not already present
        cd "$APP_DIR"
        
        # Check if dependency already exists
        if ! grep -q "@hvppyplug/mobile-services" package.json; then
            # Add the dependency
            if command_exists jq; then
                # Use jq if available for clean JSON manipulation
                jq '.dependencies["@hvppyplug/mobile-services"] = "workspace:*"' package.json > package.json.tmp && mv package.json.tmp package.json
            else
                # Fallback to sed (less clean but works)
                sed -i.bak 's/"dependencies": {/"dependencies": {\n    "@hvppyplug\/mobile-services": "workspace:*",/' package.json && rm package.json.bak
            fi
            echo -e "${GREEN}    ✅ Added mobile-services dependency to $app${NC}"
        else
            echo -e "${BLUE}    ℹ️ Mobile-services dependency already exists in $app${NC}"
        fi
        
        # Install dependencies
        pnpm install
        echo -e "${GREEN}    ✅ Dependencies installed for $app${NC}"
    else
        echo -e "${YELLOW}    ⚠️ $app directory not found${NC}"
    fi
done

echo -e "${GREEN}✅ App dependencies updated${NC}\n"

# Step 7: Clean up old Expo dependencies from ui-components-v2 if they're no longer needed
echo -e "${YELLOW}🧹 Step 7: Cleaning up ui-components-v2 dependencies...${NC}"

UI_COMPONENTS_DIR="$ROOT_DIR/packages/ui-components-v2"
if [ -f "$UI_COMPONENTS_DIR/package.json" ]; then
    cd "$UI_COMPONENTS_DIR"
    
    # List of Expo dependencies that might need to be removed
    EXPO_DEPS=(
        "expo-location"
        "expo-notifications"
        "expo-camera"
        "expo-image-picker"
        "expo-secure-store"
        "expo-local-authentication"
        "expo-task-manager"
        "expo-background-fetch"
        "expo-device"
        "expo-application"
        "expo-constants"
        "expo-network"
        "expo-file-system"
        "expo-haptics"
        "expo-linking"
        "appwrite"
    )
    
    # Check which dependencies exist and might need removal
    DEPS_TO_REMOVE=()
    for dep in "${EXPO_DEPS[@]}"; do
        if grep -q "\"$dep\"" package.json; then
            DEPS_TO_REMOVE+=("$dep")
        fi
    done
    
    if [ ${#DEPS_TO_REMOVE[@]} -gt 0 ]; then
        echo -e "${BLUE}  Found Expo dependencies in ui-components-v2:${NC}"
        for dep in "${DEPS_TO_REMOVE[@]}"; do
            echo -e "${BLUE}    - $dep${NC}"
        done
        
        echo -e "${YELLOW}  These dependencies are now in mobile-services package.${NC}"
        echo -e "${YELLOW}  You may want to remove them from ui-components-v2 if they're no longer needed.${NC}"
        echo -e "${YELLOW}  (Skipping automatic removal to avoid breaking existing code)${NC}"
    else
        echo -e "${GREEN}  ✅ No Expo dependencies found in ui-components-v2${NC}"
    fi
else
    echo -e "${YELLOW}  ⚠️ ui-components-v2 package.json not found${NC}"
fi

echo -e "${GREEN}✅ Cleanup completed${NC}\n"

# Final summary
echo -e "${CYAN}🎉 Migration Completed Successfully!${NC}"
echo -e "${CYAN}===================================${NC}"

echo -e "\n${GREEN}✨ What was accomplished:${NC}"
echo -e "${BLUE}  📦 Created packages/mobile-services package${NC}"
echo -e "${BLUE}  🔄 Moved all Expo services and hooks to the new package${NC}"
echo -e "${BLUE}  📝 Updated import statements across the codebase${NC}"
echo -e "${BLUE}  🔧 Configured build system and TypeScript${NC}"
echo -e "${BLUE}  📱 Updated app dependencies${NC}"
echo -e "${BLUE}  ✅ Validated the migration${NC}"

echo -e "\n${YELLOW}📋 Next Steps:${NC}"
echo -e "${BLUE}  1. Test your applications to ensure everything works:${NC}"
echo -e "${BLUE}     cd apps/customer-app && pnpm dev${NC}"
echo -e "${BLUE}  2. Review and test the mobile services functionality${NC}"
echo -e "${BLUE}  3. Update any remaining manual import statements if needed${NC}"
echo -e "${BLUE}  4. Consider removing unused Expo dependencies from ui-components-v2${NC}"
echo -e "${BLUE}  5. Commit your changes:${NC}"
echo -e "${BLUE}     git add .${NC}"
echo -e "${BLUE}     git commit -m \"feat: migrate mobile services to dedicated package\"${NC}"

echo -e "\n${GREEN}📚 Documentation:${NC}"
echo -e "${BLUE}  • Mobile services package: packages/mobile-services/README.md${NC}"
echo -e "${BLUE}  • Migration guide: docs/EXPO_PLUGINS_GUIDE.md${NC}"

echo -e "\n${MAGENTA}🔗 Package Usage:${NC}"
echo -e "${BLUE}  import { useLocation, LocationService } from '@hvppyplug/mobile-services'${NC}"

echo -e "\n${GREEN}🎊 Happy coding with your new mobile services architecture!${NC}"
