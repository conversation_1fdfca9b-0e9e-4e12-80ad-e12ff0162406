"use client"

import React, { useState, useCallback, useMemo } from 'react'
import { View, ScrollView, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Input, Textarea, Button } from '@gluestack-ui/themed'
import { use<PERSON><PERSON>, Controller, FieldError } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { DateTimePickerComponent } from '../DateTimePicker/DateTimePicker'
import { FileUpload } from '../FileUpload/FileUpload'

// Types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'date' | 'time' | 'datetime' | 'file' | 'switch'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  options?: Array<{ label: string; value: any }>
  validation?: {
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: any) => boolean | string
  }
  defaultValue?: any
  description?: string
  grid?: {
    span?: number // 1-12
    offset?: number
  }
  conditional?: {
    field: string
    value: any
    operator?: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
  }
}

export interface FormSection {
  title: string
  description?: string
  fields: FormField[]
  collapsible?: boolean
  defaultCollapsed?: boolean
}

export interface FormBuilderProps {
  sections: FormSection[]
  onSubmit: (data: Record<string, any>) => void | Promise<void>
  onFieldChange?: (name: string, value: any, allValues: Record<string, any>) => void
  initialValues?: Record<string, any>
  submitText?: string
  resetText?: string
  loading?: boolean
  disabled?: boolean
  className?: string
  showReset?: boolean
  validateOnChange?: boolean
}

// Styles
const containerStyles = tva({
  base: "flex-1",
})

const sectionStyles = tva({
  base: "mb-6",
})

const sectionHeaderStyles = tva({
  base: "mb-4",
  variants: {
    collapsible: {
      true: "cursor-pointer",
    },
  },
})

const fieldContainerStyles = tva({
  base: "mb-4",
  variants: {
    span: {
      1: "w-1/12",
      2: "w-2/12", 
      3: "w-3/12",
      4: "w-4/12",
      5: "w-5/12",
      6: "w-6/12",
      7: "w-7/12",
      8: "w-8/12",
      9: "w-9/12",
      10: "w-10/12",
      11: "w-11/12",
      12: "w-full",
    },
  },
})

const labelStyles = tva({
  base: "text-sm font-medium text-typography-700 mb-2",
  variants: {
    required: {
      true: "after:content-['*'] after:text-error-500 after:ml-1",
    },
  },
})

const errorStyles = tva({
  base: "text-sm text-error-600 mt-1",
})

const descriptionStyles = tva({
  base: "text-sm text-typography-600 mt-1",
})

const actionsStyles = tva({
  base: "flex-row gap-3 pt-6 border-t border-outline-200",
})

export function FormBuilder({
  sections,
  onSubmit,
  onFieldChange,
  initialValues = {},
  submitText = 'Submit',
  resetText = 'Reset',
  loading = false,
  disabled = false,
  className,
  showReset = true,
  validateOnChange = false,
}: FormBuilderProps) {
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set(sections.filter(s => s.defaultCollapsed).map(s => s.title))
  )

  // Generate Zod schema from form fields
  const schema = useMemo(() => {
    const schemaFields: Record<string, z.ZodTypeAny> = {}

    sections.forEach(section => {
      section.fields.forEach(field => {
        let fieldSchema: z.ZodTypeAny

        switch (field.type) {
          case 'email':
            fieldSchema = z.string().email('Invalid email address')
            break
          case 'number':
            fieldSchema = z.number()
            if (field.validation?.min !== undefined) {
              fieldSchema = fieldSchema.min(field.validation.min)
            }
            if (field.validation?.max !== undefined) {
              fieldSchema = fieldSchema.max(field.validation.max)
            }
            break
          case 'date':
          case 'time':
          case 'datetime':
            fieldSchema = z.date()
            break
          case 'checkbox':
          case 'switch':
            fieldSchema = z.boolean()
            break
          case 'multiselect':
            fieldSchema = z.array(z.any())
            break
          case 'file':
            fieldSchema = z.array(z.any())
            break
          default:
            fieldSchema = z.string()
            if (field.validation?.minLength) {
              fieldSchema = fieldSchema.min(field.validation.minLength)
            }
            if (field.validation?.maxLength) {
              fieldSchema = fieldSchema.max(field.validation.maxLength)
            }
            if (field.validation?.pattern) {
              fieldSchema = fieldSchema.regex(field.validation.pattern)
            }
        }

        if (!field.required) {
          fieldSchema = fieldSchema.optional()
        }

        schemaFields[field.name] = fieldSchema
      })
    })

    return z.object(schemaFields)
  }, [sections])

  // Form hook
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: initialValues,
    mode: validateOnChange ? 'onChange' : 'onSubmit',
  })

  const watchedValues = watch()

  // Check if field should be visible based on conditional logic
  const isFieldVisible = useCallback((field: FormField): boolean => {
    if (!field.conditional) return true

    const { field: conditionField, value: conditionValue, operator = 'equals' } = field.conditional
    const fieldValue = watchedValues[conditionField]

    switch (operator) {
      case 'equals':
        return fieldValue === conditionValue
      case 'not_equals':
        return fieldValue !== conditionValue
      case 'contains':
        return Array.isArray(fieldValue) ? fieldValue.includes(conditionValue) : false
      case 'greater_than':
        return typeof fieldValue === 'number' && fieldValue > conditionValue
      case 'less_than':
        return typeof fieldValue === 'number' && fieldValue < conditionValue
      default:
        return true
    }
  }, [watchedValues])

  // Toggle section collapse
  const toggleSection = (sectionTitle: string) => {
    setCollapsedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionTitle)) {
        newSet.delete(sectionTitle)
      } else {
        newSet.add(sectionTitle)
      }
      return newSet
    })
  }

  // Handle form submission
  const handleFormSubmit = async (data: Record<string, any>) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  // Render field based on type
  const renderField = (field: FormField) => {
    if (!isFieldVisible(field)) return null

    const fieldError = errors[field.name] as FieldError | undefined

    return (
      <View 
        key={field.name}
        className={fieldContainerStyles({ 
          span: field.grid?.span || 12 
        })}
      >
        <Text className={labelStyles({ required: field.required })}>
          {field.label}
        </Text>

        <Controller
          name={field.name}
          control={control}
          render={({ field: { onChange, value, onBlur } }) => {
            const handleChange = (newValue: any) => {
              onChange(newValue)
              onFieldChange?.(field.name, newValue, watchedValues)
            }

            switch (field.type) {
              case 'textarea':
                return (
                  <Textarea
                    placeholder={field.placeholder}
                    value={value || ''}
                    onChangeText={handleChange}
                    onBlur={onBlur}
                    disabled={disabled || field.disabled}
                    className={fieldError ? 'border-error-500' : ''}
                  />
                )

              case 'select':
                return (
                  <View className="border border-outline-300 rounded-lg">
                    {field.options?.map(option => (
                      <Pressable
                        key={option.value}
                        className={`p-3 border-b border-outline-200 ${
                          value === option.value ? 'bg-primary-50' : ''
                        }`}
                        onPress={() => handleChange(option.value)}
                      >
                        <Text className={
                          value === option.value ? 'text-primary-700 font-medium' : 'text-typography-700'
                        }>
                          {option.label}
                        </Text>
                      </Pressable>
                    ))}
                  </View>
                )

              case 'checkbox':
              case 'switch':
                return (
                  <Pressable
                    className="flex-row items-center"
                    onPress={() => handleChange(!value)}
                  >
                    <View className={`w-5 h-5 border-2 rounded mr-3 ${
                      value ? 'bg-primary-500 border-primary-500' : 'border-outline-300'
                    }`}>
                      {value && (
                        <Text className="text-white text-center text-xs">✓</Text>
                      )}
                    </View>
                    <Text className="text-typography-700">{field.placeholder}</Text>
                  </Pressable>
                )

              case 'date':
              case 'time':
              case 'datetime':
                return (
                  <DateTimePickerComponent
                    value={value}
                    onChange={handleChange}
                    mode={field.type}
                    placeholder={field.placeholder}
                    disabled={disabled || field.disabled}
                    error={fieldError?.message}
                  />
                )

              case 'file':
                return (
                  <FileUpload
                    value={value || []}
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    disabled={disabled || field.disabled}
                    multiple={field.type === 'file'}
                  />
                )

              default:
                return (
                  <Input
                    placeholder={field.placeholder}
                    value={value || ''}
                    onChangeText={handleChange}
                    onBlur={onBlur}
                    secureTextEntry={field.type === 'password'}
                    keyboardType={
                      field.type === 'email' ? 'email-address' :
                      field.type === 'number' ? 'numeric' : 'default'
                    }
                    disabled={disabled || field.disabled}
                    className={fieldError ? 'border-error-500' : ''}
                  />
                )
            }
          }}
        />

        {fieldError && (
          <Text className={errorStyles()}>
            {fieldError.message}
          </Text>
        )}

        {field.description && (
          <Text className={descriptionStyles()}>
            {field.description}
          </Text>
        )}
      </View>
    )
  }

  // Render section
  const renderSection = (section: FormSection) => {
    const isCollapsed = collapsedSections.has(section.title)

    return (
      <View key={section.title} className={sectionStyles()}>
        <Pressable
          className={sectionHeaderStyles({ collapsible: section.collapsible })}
          onPress={section.collapsible ? () => toggleSection(section.title) : undefined}
        >
          <Text className="text-lg font-semibold text-typography-900">
            {section.title}
          </Text>
          {section.description && (
            <Text className="text-typography-600 mt-1">
              {section.description}
            </Text>
          )}
        </Pressable>

        {(!section.collapsible || !isCollapsed) && (
          <View className="flex-row flex-wrap -mx-2">
            {section.fields.map(field => (
              <View key={field.name} className="px-2" style={{ width: `${(field.grid?.span || 12) * 8.333}%` }}>
                {renderField(field)}
              </View>
            ))}
          </View>
        )}
      </View>
    )
  }

  return (
    <View className={containerStyles({ class: className })}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {sections.map(renderSection)}

        <View className={actionsStyles()}>
          {showReset && (
            <Button
              variant="outline"
              onPress={() => reset()}
              disabled={loading || disabled || isSubmitting}
            >
              <Text>{resetText}</Text>
            </Button>
          )}
          
          <Button
            onPress={handleSubmit(handleFormSubmit)}
            disabled={loading || disabled || isSubmitting}
            className="flex-1"
          >
            <Text>{loading || isSubmitting ? 'Submitting...' : submitText}</Text>
          </Button>
        </View>
      </ScrollView>
    </View>
  )
}
