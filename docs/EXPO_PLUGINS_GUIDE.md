# HVPPYPlug+ Expo Plugins Integration Guide

This guide covers the implementation and usage of Expo plugins for the HVPPYPlug+ mobile applications, with full Appwrite integration.

## 🚀 Quick Start

### Installation Commands

For each app (customer-app, vendor-app, runner-app), run:

```bash
# Navigate to the app directory
cd apps/customer-app  # or vendor-app, runner-app

# Install the plugins
pnpm add expo-location@~18.1.6 \
         expo-notifications@~0.31.4 \
         expo-camera@~16.1.11 \
         expo-image-picker@~16.1.3 \
         expo-secure-store@~14.1.1 \
         expo-local-authentication@~15.1.1 \
         expo-task-manager@~12.1.1 \
         expo-background-fetch@~13.1.1 \
         expo-device@~7.1.1 \
         expo-application@~6.1.1 \
         expo-constants@~17.1.2 \
         expo-network@~7.1.1 \
         expo-file-system@~18.1.1 \
         expo-haptics@~14.1.1 \
         expo-linking@~7.1.2 \
         @react-native-async-storage/async-storage@^2.1.0 \
         appwrite@^16.0.2

# Prebuild the app to apply plugin configurations
npx expo prebuild --clean
```

## 📱 Plugin Configuration

### Customer App Configuration

The customer app is configured with all essential plugins for:
- Location tracking for delivery
- Push notifications for order updates
- Camera for profile photos and order verification
- Secure storage for authentication tokens

### Vendor App Configuration

Similar to customer app but optimized for:
- Menu item photo capture
- Order management notifications
- Location services for delivery zones

### Runner App Configuration

Focused on:
- Real-time location tracking
- Background location updates
- Delivery notifications
- Route optimization

## 🔧 Service Integration

### LocationService with Appwrite

```typescript
import { useLocation, AppwriteService } from '@hvppyplug/compound-components'

// Initialize Appwrite
const appwriteService = AppwriteService.getInstance({
  endpoint: 'https://your-appwrite-endpoint',
  projectId: 'your-project-id',
  databaseId: 'your-database-id',
  storageId: 'your-storage-id',
})

// Use location hook with Appwrite sync
const {
  currentLocation,
  isTracking,
  startTracking,
  stopTracking,
} = useLocation({
  enableBackgroundLocation: true,
  enableAppwriteSync: true,
  onLocationUpdate: (location) => {
    console.log('Location updated:', location)
  },
})

// Start tracking for runners
await startTracking()
```

### NotificationService with Appwrite

```typescript
import { useNotifications } from '@hvppyplug/compound-components'

const {
  pushToken,
  sendOrderNotification,
  sendDeliveryNotification,
} = useNotifications({
  enableAppwriteSync: true,
  onNotificationReceived: (notification) => {
    console.log('Notification received:', notification)
  },
  onNotificationResponse: (response) => {
    // Handle notification tap
    const data = response.notification.request.content.data
    if (data.type === 'order_update') {
      // Navigate to order details
      navigation.navigate('OrderDetails', { orderId: data.orderId })
    }
  },
})

// Send order update notification
await sendOrderNotification(
  'order-123',
  'preparing',
  'Your order is being prepared!',
  { estimatedTime: '15 minutes' }
)
```

### CameraService with Appwrite Storage

```typescript
import { useCamera } from '@hvppyplug/compound-components'

const {
  takePhoto,
  selectFromGallery,
  uploadImage,
  uploadImages,
} = useCamera({
  enableAppwriteUpload: true,
  bucketId: 'menu-images',
  defaultQuality: 0.8,
  maxFileSize: 5 * 1024 * 1024, // 5MB
})

// Take photo and upload to Appwrite
const handleTakePhoto = async () => {
  const photo = await takePhoto({
    allowsEditing: true,
    aspect: [4, 3],
    quality: 0.8,
  })
  
  if (photo) {
    const imageUrl = await uploadImage(photo)
    console.log('Image uploaded:', imageUrl)
  }
}

// Select multiple images from gallery
const handleSelectImages = async () => {
  const images = await selectFromGallery({
    allowsMultipleSelection: true,
    selectionLimit: 5,
  })
  
  if (images) {
    const imageUrls = await uploadImages(images)
    console.log('Images uploaded:', imageUrls)
  }
}
```

## 🔐 Security & Permissions

### iOS Permissions (Info.plist)

The following permissions are automatically configured:

- `NSLocationAlwaysAndWhenInUseUsageDescription`
- `NSLocationWhenInUseUsageDescription`
- `NSCameraUsageDescription`
- `NSMicrophoneUsageDescription`
- `NSPhotoLibraryUsageDescription`
- `NSFaceIDUsageDescription`

### Android Permissions

The following permissions are automatically added:

- `ACCESS_COARSE_LOCATION`
- `ACCESS_FINE_LOCATION`
- `ACCESS_BACKGROUND_LOCATION`
- `CAMERA`
- `RECORD_AUDIO`
- `READ_EXTERNAL_STORAGE`
- `WRITE_EXTERNAL_STORAGE`
- `RECEIVE_BOOT_COMPLETED`
- `VIBRATE`
- `USE_FINGERPRINT`
- `USE_BIOMETRIC`
- `FOREGROUND_SERVICE`
- `FOREGROUND_SERVICE_LOCATION`

## 🌐 Appwrite Integration

### Database Collections

Create these collections in your Appwrite database:

1. **user_locations**
   - userId (string)
   - latitude (float)
   - longitude (float)
   - timestamp (integer)
   - address (string, optional)
   - accuracy (float, optional)
   - heading (float, optional)
   - speed (float, optional)

2. **notification_tokens**
   - userId (string)
   - token (string)
   - platform (string: ios/android/web)
   - deviceId (string)

### Storage Buckets

Create these storage buckets:

1. **menu-images** - For vendor menu item photos
2. **profile-images** - For user profile pictures
3. **order-images** - For order verification photos

### Functions

Create these Appwrite Functions:

1. **send-push-notification** - Send push notifications via FCM/APNs
2. **process-location-update** - Handle real-time location updates
3. **optimize-images** - Compress and optimize uploaded images

## 📊 Real-time Features

### Order Tracking

```typescript
// Subscribe to order updates
const unsubscribe = appwriteService.subscribeToOrderUpdates(
  orderId,
  (order) => {
    // Update UI with order status
    setOrderStatus(order.status)
    
    // Send notification
    sendOrderNotification(
      order.id,
      order.status,
      getStatusMessage(order.status)
    )
  }
)

// Subscribe to runner location for delivery tracking
const unsubscribeLocation = appwriteService.subscribeToRunnerLocation(
  runnerId,
  (location) => {
    // Update map with runner position
    updateRunnerMarker(location)
  }
)
```

### Background Tasks

```typescript
// Register background location task for runners
TaskManager.defineTask('background-location', ({ data, error }) => {
  if (error) {
    console.error('Background location error:', error)
    return
  }

  if (data) {
    const { locations } = data
    const location = locations[0]
    
    // Update location in Appwrite
    appwriteService.updateUserLocation({
      coords: location.coords,
      timestamp: location.timestamp,
    })
  }
})
```

## 🧪 Testing

### Location Testing

```typescript
// Test location services
const testLocation = async () => {
  const location = await getCurrentLocation()
  console.log('Current location:', location)
  
  const distance = calculateDistance(
    { latitude: -26.2041, longitude: 28.0473 }, // Johannesburg
    { latitude: -33.9249, longitude: 18.4241 }  // Cape Town
  )
  console.log('Distance:', distance, 'meters')
}
```

### Notification Testing

```typescript
// Test notifications
const testNotifications = async () => {
  // Schedule local notification
  await scheduleNotification({
    title: 'Test Notification',
    body: 'This is a test notification',
    data: { type: 'test' },
  })
  
  // Test order notification
  await sendOrderNotification(
    'test-order',
    'delivered',
    'Your test order has been delivered!'
  )
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Location not working on iOS**
   - Ensure background location permission is granted
   - Check that `UIBackgroundModes` includes `location`

2. **Notifications not appearing**
   - Verify push token is registered with Appwrite
   - Check notification permissions are granted

3. **Camera not working**
   - Ensure camera permissions are granted
   - Check that device has camera capability

4. **Appwrite connection issues**
   - Verify endpoint and project ID are correct
   - Check network connectivity
   - Ensure user is authenticated

### Debug Commands

```bash
# Check plugin installation
npx expo config --type introspect

# View native code (after prebuild)
npx expo run:ios --device
npx expo run:android --device

# Debug notifications
npx expo install expo-dev-client
```

## 📚 Additional Resources

- [Expo Location Documentation](https://docs.expo.dev/versions/latest/sdk/location/)
- [Expo Notifications Documentation](https://docs.expo.dev/versions/latest/sdk/notifications/)
- [Expo Camera Documentation](https://docs.expo.dev/versions/latest/sdk/camera/)
- [Appwrite Documentation](https://appwrite.io/docs)
- [React Native Maps](https://github.com/react-native-maps/react-native-maps)

## 🔄 Updates and Maintenance

Keep plugins updated by running:

```bash
npx expo install --fix
```

Monitor plugin compatibility with new Expo SDK versions and update accordingly.
