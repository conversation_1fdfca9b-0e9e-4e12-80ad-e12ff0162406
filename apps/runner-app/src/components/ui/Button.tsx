import React from 'react'
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet, ViewStyle, TextStyle } from 'react-native'
import { useTheme } from '../../providers/ThemeProvider'

export interface ButtonProps {
  title: string
  onPress: () => void
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  style?: ViewStyle
  textStyle?: TextStyle
  testID?: string
}

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  testID,
}: ButtonProps) {
  const { theme } = useTheme()

  const getButtonStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      opacity: disabled || loading ? 0.6 : 1,
    }

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.xs,
        minHeight: 36,
      },
      md: {
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.sm,
        minHeight: 44,
      },
      lg: {
        paddingHorizontal: theme.spacing.xl,
        paddingVertical: theme.spacing.md,
        minHeight: 52,
      },
    }

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: theme.colors.primary[600],
        borderColor: theme.colors.primary[600],
      },
      secondary: {
        backgroundColor: theme.colors.secondary[600],
        borderColor: theme.colors.secondary[600],
      },
      outline: {
        backgroundColor: theme.colors.transparent,
        borderColor: theme.colors.primary[600],
      },
      ghost: {
        backgroundColor: theme.colors.transparent,
        borderColor: theme.colors.transparent,
      },
      danger: {
        backgroundColor: theme.colors.error[600],
        borderColor: theme.colors.error[600],
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...(fullWidth && { width: '100%' }),
      ...style,
    }
  }

  const getTextStyles = (): TextStyle => {
    const baseStyles: TextStyle = {
      fontWeight: theme.fontWeight.semibold,
      textAlign: 'center',
    }

    // Size styles
    const sizeStyles: Record<string, TextStyle> = {
      sm: {
        fontSize: theme.fontSize.sm,
      },
      md: {
        fontSize: theme.fontSize.base,
      },
      lg: {
        fontSize: theme.fontSize.lg,
      },
    }

    // Variant styles
    const variantStyles: Record<string, TextStyle> = {
      primary: {
        color: theme.colors.white,
      },
      secondary: {
        color: theme.colors.white,
      },
      outline: {
        color: theme.colors.primary[600],
      },
      ghost: {
        color: theme.colors.primary[600],
      },
      danger: {
        color: theme.colors.white,
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...textStyle,
    }
  }

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? theme.colors.primary[600] : theme.colors.white}
        />
      )
    }

    const textElement = (
      <Text style={getTextStyles()}>
        {title}
      </Text>
    )

    if (!icon) {
      return textElement
    }

    const iconElement = React.cloneElement(icon as React.ReactElement, {
      size: size === 'sm' ? 16 : size === 'lg' ? 24 : 20,
      color: variant === 'outline' || variant === 'ghost' ? theme.colors.primary[600] : theme.colors.white,
    })

    return (
      <>
        {iconPosition === 'left' && (
          <>
            {iconElement}
            <Text style={[getTextStyles(), { marginLeft: theme.spacing.xs }]}>
              {title}
            </Text>
          </>
        )}
        {iconPosition === 'right' && (
          <>
            <Text style={[getTextStyles(), { marginRight: theme.spacing.xs }]}>
              {title}
            </Text>
            {iconElement}
          </>
        )}
      </>
    )
  }

  return (
    <TouchableOpacity
      style={getButtonStyles()}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      testID={testID}
    >
      {renderContent()}
    </TouchableOpacity>
  )
}
