import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { MapPin, Clock, Package, DollarSign, User } from 'lucide-react-native'

import { Card, Button, StatusBadge } from '../ui'
import { useTheme } from '../../providers/ThemeProvider'
import type { Order, OrderStatus } from '../../types'

export interface OrderCardProps {
  order: Order
  onAccept?: (orderId: string) => void
  onDecline?: (orderId: string) => void
  onViewDetails?: (orderId: string) => void
  onStartPickup?: (orderId: string) => void
  onCompleteDelivery?: (orderId: string) => void
  showActions?: boolean
  compact?: boolean
}

export function OrderCard({
  order,
  onAccept,
  onDecline,
  onViewDetails,
  onStartPickup,
  onCompleteDelivery,
  showActions = true,
  compact = false,
}: OrderCardProps) {
  const { theme } = useTheme()

  const getActionButtons = () => {
    switch (order.status) {
      case 'pending':
        return (
          <View style={styles.actionButtons}>
            {onDecline && (
              <Button
                title="Decline"
                variant="outline"
                size="sm"
                onPress={() => onDecline(order.id)}
                style={{ flex: 1 }}
              />
            )}
            {onAccept && (
              <Button
                title="Accept"
                variant="primary"
                size="sm"
                onPress={() => onAccept(order.id)}
                style={{ flex: 1, marginLeft: theme.spacing.sm }}
              />
            )}
          </View>
        )
      
      case 'accepted':
        return (
          <View style={styles.actionButtons}>
            {onStartPickup && (
              <Button
                title="Start Pickup"
                variant="secondary"
                size="sm"
                onPress={() => onStartPickup(order.id)}
                fullWidth
              />
            )}
          </View>
        )
      
      case 'picked_up':
      case 'en_route':
        return (
          <View style={styles.actionButtons}>
            {onCompleteDelivery && (
              <Button
                title="Complete Delivery"
                variant="primary"
                size="sm"
                onPress={() => onCompleteDelivery(order.id)}
                fullWidth
              />
            )}
          </View>
        )
      
      default:
        return null
    }
  }

  const formatDistance = (distance: string) => {
    return distance.replace(' km', 'km')
  }

  const formatTime = (time: string) => {
    return time.replace(' min', 'm')
  }

  return (
    <Card
      variant="elevated"
      size={compact ? 'sm' : 'md'}
      onPress={onViewDetails ? () => onViewDetails(order.id) : undefined}
      style={styles.card}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.customerInfo}>
          <View style={styles.customerRow}>
            <User size={16} color={theme.colors.gray[600]} />
            <Text style={[styles.customerName, { color: theme.colors.gray[900] }]}>
              {order.customerName}
            </Text>
          </View>
          <Text style={[styles.vendorName, { color: theme.colors.gray[600] }]}>
            {order.vendorName}
          </Text>
        </View>
        
        <StatusBadge status={order.status} size="sm" />
      </View>

      {/* Order Details */}
      <View style={styles.details}>
        <View style={styles.detailRow}>
          <MapPin size={16} color={theme.colors.gray[500]} />
          <Text style={[styles.detailText, { color: theme.colors.gray[600] }]} numberOfLines={1}>
            {order.deliveryAddress.street}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Clock size={16} color={theme.colors.gray[500]} />
          <Text style={[styles.detailText, { color: theme.colors.gray[600] }]}>
            {formatTime(order.estimatedDeliveryTime)} • {formatDistance('2.3 km')}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Package size={16} color={theme.colors.gray[500]} />
          <Text style={[styles.detailText, { color: theme.colors.gray[600] }]}>
            {order.items.length} items
          </Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.amountContainer}>
          <DollarSign size={18} color={theme.colors.success[600]} />
          <Text style={[styles.amount, { color: theme.colors.success[600] }]}>
            R{order.total.toFixed(2)}
          </Text>
          <Text style={[styles.deliveryFee, { color: theme.colors.gray[500] }]}>
            (+R{order.deliveryFee.toFixed(2)} delivery)
          </Text>
        </View>

        {showActions && getActionButtons()}
      </View>

      {/* Special Instructions */}
      {order.specialInstructions && !compact && (
        <View style={[styles.instructions, { backgroundColor: theme.colors.gray[50] }]}>
          <Text style={[styles.instructionsLabel, { color: theme.colors.gray[700] }]}>
            Special Instructions:
          </Text>
          <Text style={[styles.instructionsText, { color: theme.colors.gray[600] }]}>
            {order.specialInstructions}
          </Text>
        </View>
      )}
    </Card>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  customerInfo: {
    flex: 1,
  },
  customerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  vendorName: {
    fontSize: 14,
    marginLeft: 24,
  },
  details: {
    marginBottom: 16,
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  amount: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 4,
  },
  deliveryFee: {
    fontSize: 12,
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    marginLeft: 16,
  },
  instructions: {
    marginTop: 12,
    padding: 12,
    borderRadius: 8,
  },
  instructionsLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 20,
  },
})
