#!/usr/bin/env node

/**
 * Build preparation script for EAS Build
 * This script ensures all workspace dependencies are built before the main app build
 * Following Expo's official monorepo guidelines: https://docs.expo.dev/guides/monorepos/
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

const monorepoRoot = path.resolve(__dirname, '../../..');
const projectRoot = path.resolve(__dirname, '..');

console.log('🚀 Preparing EAS Build for HVPPYPlug+ Customer App...');
console.log(`📁 Monorepo root: ${monorepoRoot}`);
console.log(`📱 Project root: ${projectRoot}`);

// Function to run command with proper error handling
function runCommand(command, cwd = monorepoRoot, options = {}) {
  try {
    console.log(`⚡ Running: ${command}`);
    execSync(command, {
      cwd,
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'production' },
      ...options
    });
    console.log(`✅ Completed: ${command}`);
    return true;
  } catch (error) {
    if (options.allowFailure) {
      console.warn(`⚠️  Warning: ${command} failed but continuing...`);
      return false;
    }
    console.error(`❌ Failed: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

// Check if we're in the correct directory structure
if (!fs.existsSync(path.join(monorepoRoot, 'pnpm-workspace.yaml'))) {
  console.error('❌ Error: Not in a pnpm workspace. Make sure you\'re running this from the correct directory.');
  process.exit(1);
}

console.log('\n📦 Installing dependencies first...');
runCommand('pnpm install --frozen-lockfile');

console.log('\n🔨 Building workspace dependencies...');

// Build dependencies in the correct order with better error handling
const buildSteps = [
  { cmd: 'pnpm --filter @hvppyplug/common build', required: true },
  { cmd: 'pnpm --filter @hvppyplug/compound-components build', required: true },
  { cmd: 'pnpm --filter @hvppyplug/mobile-services build', required: true }
];

let allBuildsSuccessful = true;

buildSteps.forEach(({ cmd, required }) => {
  const success = runCommand(cmd, monorepoRoot, { allowFailure: !required });
  if (!success && required) {
    allBuildsSuccessful = false;
  }
});

if (!allBuildsSuccessful) {
  console.error('\n❌ Some required builds failed. Please check the errors above.');
  process.exit(1);
}

console.log('\n🎉 Build preparation completed successfully!');
console.log('📱 Ready for EAS Build...');
