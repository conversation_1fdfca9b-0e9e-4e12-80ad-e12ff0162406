import React from 'react'
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { ArrowLeftIcon } from 'lucide-react-native'
import { useTheme } from '../../hooks/useTheme'
import type { AuthStackScreenProps } from '../../types/navigation'

type Props = AuthStackScreenProps<'ForgotPassword'>

export function ForgotPasswordScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeftIcon size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={[typography.display.small, { color: colors.text.primary, marginTop: 24 }]}>
          Reset Password
        </Text>
        <Text style={[typography.body.large, { color: colors.text.secondary, marginTop: 8 }]}>
          Password reset coming soon...
        </Text>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { paddingHorizontal: 24, paddingTop: 16 },
})
