import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  TextInput,
  Image
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  SearchIcon,
  MessageSquareIcon,
  PhoneIcon,
  MoreVerticalIcon,
  ClockIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useOrdersStore } from '../../stores/ordersStore'
import type { ChatStackScreenProps } from '../../types/navigation'

type Props = ChatStackScreenProps<'ChatList'>

interface ChatItem {
  orderId: string
  customerId: string
  customerName: string
  customerAvatar?: string
  lastMessage: string
  lastMessageTime: string
  unreadCount: number
  orderStatus: string
  serviceName: string
}

export function ChatListScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { orders, fetchOrders } = useOrdersStore()

  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    loadChats()
  }, [])

  const loadChats = async () => {
    try {
      await fetchOrders()
    } catch (error) {
      console.error('Failed to load chats:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadChats()
    setRefreshing(false)
  }

  const getChatItems = (): ChatItem[] => {
    return orders
      .filter(order => order.messages.length > 0)
      .map(order => {
        const lastMessage = order.messages[order.messages.length - 1]
        return {
          orderId: order.id,
          customerId: order.customerId,
          customerName: order.customerName,
          customerAvatar: order.customerAvatar,
          lastMessage: lastMessage.message,
          lastMessageTime: lastMessage.timestamp,
          unreadCount: order.messages.filter(msg => msg.senderId !== 'vendor').length, // Mock unread count
          orderStatus: order.status,
          serviceName: order.serviceName
        }
      })
      .filter(chat =>
        searchQuery.trim() === '' ||
        chat.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.serviceName.toLowerCase().includes(searchQuery.toLowerCase())
      )
      .sort((a, b) => new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime())
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const handleChatPress = (chat: ChatItem) => {
    navigation.navigate('Chat', {
      orderId: chat.orderId,
      customerId: chat.customerId
    })
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <Text style={[typography.headline.large, { color: colors.text.primary, marginBottom: 16 }]}>
        Messages
      </Text>

      {/* Search Bar */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        alignItems: 'center',
      }}>
        <SearchIcon size={20} color={colors.text.secondary} />
        <TextInput
          style={[
            typography.body.medium,
            {
              flex: 1,
              marginLeft: 12,
              color: colors.text.primary,
            }
          ]}
          placeholder="Search conversations..."
          placeholderTextColor={colors.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
    </View>
  )

  const renderChatItem = (chat: ChatItem) => (
    <TouchableOpacity
      key={chat.orderId}
      style={{
        backgroundColor: colors.background.secondary,
        marginHorizontal: 16,
        marginBottom: 8,
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
      }}
      onPress={() => handleChatPress(chat)}
    >
      {/* Avatar */}
      <View style={{
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: colors.primary[100],
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        {chat.customerAvatar ? (
          <Image
            source={{ uri: chat.customerAvatar }}
            style={{ width: 50, height: 50, borderRadius: 25 }}
          />
        ) : (
          <Text style={[typography.body.large, { color: colors.primary[600], fontWeight: '600' }]}>
            {chat.customerName.charAt(0).toUpperCase()}
          </Text>
        )}
      </View>

      {/* Chat Info */}
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
          <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
            {chat.customerName}
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <ClockIcon size={12} color={colors.text.tertiary} />
            <Text style={[typography.body.small, { color: colors.text.tertiary, marginLeft: 4 }]}>
              {formatTime(chat.lastMessageTime)}
            </Text>
          </View>
        </View>

        <Text style={[typography.body.small, { color: colors.text.secondary, marginBottom: 4 }]}>
          {chat.serviceName}
        </Text>

        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text
            style={[
              typography.body.small,
              {
                color: colors.text.tertiary,
                flex: 1,
                marginRight: 8
              }
            ]}
            numberOfLines={1}
          >
            {chat.lastMessage}
          </Text>

          {chat.unreadCount > 0 && (
            <View style={{
              backgroundColor: colors.primary[500],
              borderRadius: 10,
              paddingHorizontal: 6,
              paddingVertical: 2,
              minWidth: 20,
              alignItems: 'center',
            }}>
              <Text style={[typography.body.small, { color: colors.white, fontSize: 10, fontWeight: '600' }]}>
                {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Action Buttons */}
      <View style={{ flexDirection: 'row', marginLeft: 8, gap: 8 }}>
        <TouchableOpacity
          style={{
            backgroundColor: colors.background.primary,
            borderRadius: 16,
            padding: 8,
          }}
        >
          <PhoneIcon size={16} color={colors.text.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            backgroundColor: colors.background.primary,
            borderRadius: 16,
            padding: 8,
          }}
        >
          <MoreVerticalIcon size={16} color={colors.text.primary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )

  const renderChatsList = () => {
    const chatItems = getChatItems()

    if (chatItems.length === 0) {
      return (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 24,
          paddingVertical: 48,
        }}>
          <MessageSquareIcon size={64} color={colors.text.tertiary} />
          <Text style={[typography.body.large, { color: colors.text.secondary, textAlign: 'center', marginTop: 16 }]}>
            {searchQuery.trim() ? 'No conversations match your search' : 'No messages yet'}
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.tertiary, textAlign: 'center', marginTop: 8 }]}>
            {searchQuery.trim()
              ? 'Try adjusting your search terms'
              : 'Messages from customers will appear here when they contact you about orders'
            }
          </Text>
        </View>
      )
    }

    return (
      <View style={{ paddingVertical: 8 }}>
        {chatItems.map(renderChatItem)}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderHeader()}
        {renderChatsList()}
      </ScrollView>
    </SafeAreaView>
  )
}
