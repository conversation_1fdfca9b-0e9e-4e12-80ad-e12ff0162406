import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HStack,
  <PERSON>ing,
  Text,
  Button,
  ButtonText,
  Card,
  CardHeader,
  CardBody,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Badge,
  BadgeText,
  Input,
  InputField,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  ChevronDownIcon,
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableData,
  Pressable,
} from "@/ui";

export interface Vendor {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: "active" | "pending" | "suspended" | "rejected";
  joinDate: string;
  totalProducts: number;
  totalSales: number;
  rating: number;
  commission: number;
  category: string;
}

export interface VendorManagementProps {
  /** Array of vendors */
  vendors?: Vendor[];
  /** Loading state */
  loading?: boolean;
  /** Search handler */
  onSearch?: (query: string) => void;
  /** Filter handler */
  onFilter?: (filters: { status?: string; category?: string }) => void;
  /** Vendor action handlers */
  onApprove?: (vendorId: string) => void;
  onSuspend?: (vendorId: string) => void;
  onReject?: (vendorId: string) => void;
  onViewDetails?: (vendorId: string) => void;
  /** Pagination */
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
}

const mockVendors: Vendor[] = [
  {
    id: "1",
    name: "TechStore Pro",
    email: "<EMAIL>",
    status: "active",
    joinDate: "2024-01-15",
    totalProducts: 245,
    totalSales: 125000,
    rating: 4.8,
    commission: 15,
    category: "Electronics"
  },
  {
    id: "2", 
    name: "Fashion Hub",
    email: "<EMAIL>",
    status: "pending",
    joinDate: "2024-03-20",
    totalProducts: 89,
    totalSales: 0,
    rating: 0,
    commission: 12,
    category: "Fashion"
  },
  {
    id: "3",
    name: "Home Essentials",
    email: "<EMAIL>", 
    status: "suspended",
    joinDate: "2023-11-10",
    totalProducts: 156,
    totalSales: 78000,
    rating: 3.2,
    commission: 18,
    category: "Home & Garden"
  }
];

const VendorManagement = React.memo<VendorManagementProps>(({
  vendors = mockVendors,
  loading = false,
  onSearch,
  onFilter,
  onApprove,
  onSuspend,
  onReject,
  onViewDetails,
  currentPage = 1,
  totalPages = 1,
  onPageChange
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    onSearch?.(query);
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    onFilter?.({ status, category: categoryFilter });
  };

  const handleCategoryFilter = (category: string) => {
    setCategoryFilter(category);
    onFilter?.({ status: statusFilter, category });
  };

  const getStatusBadge = (status: Vendor["status"]) => {
    const variants = {
      active: { variant: "solid", color: "bg-green-500" },
      pending: { variant: "solid", color: "bg-yellow-500" },
      suspended: { variant: "solid", color: "bg-red-500" },
      rejected: { variant: "solid", color: "bg-gray-500" }
    };

    const config = variants[status];
    return (
      <Badge className={`${config.color} text-white`}>
        <BadgeText className="text-white capitalize">{status}</BadgeText>
      </Badge>
    );
  };

  const renderActionButtons = (vendor: Vendor) => {
    return (
      <HStack space="sm">
        <Button size="xs" variant="outline" onPress={() => onViewDetails?.(vendor.id)}>
          <ButtonText>View</ButtonText>
        </Button>
        
        {vendor.status === "pending" && (
          <>
            <Button size="xs" className="bg-green-500" onPress={() => onApprove?.(vendor.id)}>
              <ButtonText className="text-white">Approve</ButtonText>
            </Button>
            <Button size="xs" className="bg-red-500" onPress={() => onReject?.(vendor.id)}>
              <ButtonText className="text-white">Reject</ButtonText>
            </Button>
          </>
        )}
        
        {vendor.status === "active" && (
          <Button size="xs" className="bg-orange-500" onPress={() => onSuspend?.(vendor.id)}>
            <ButtonText className="text-white">Suspend</ButtonText>
          </Button>
        )}
      </HStack>
    );
  };

  return (
    <VStack space="lg" className="p-6">
      <VStack space="md">
        <Heading size="xl">Vendor Management</Heading>
        <Text className="text-typography-600">
          Manage vendor applications, approvals, and account status
        </Text>
      </VStack>

      {/* Filters */}
      <Card>
        <CardBody>
          <HStack space="md" className="flex-wrap">
            <Input className="flex-1 min-w-[200px]">
              <InputField
                placeholder="Search vendors..."
                value={searchQuery}
                onChangeText={handleSearch}
              />
            </Input>

            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectInput placeholder="Status" />
                <SelectIcon as={ChevronDownIcon} />
              </SelectTrigger>
              <SelectPortal>
                <SelectBackdrop />
                <SelectContent>
                  <SelectDragIndicatorWrapper>
                    <SelectDragIndicator />
                  </SelectDragIndicatorWrapper>
                  <SelectItem label="All Status" value="" />
                  <SelectItem label="Active" value="active" />
                  <SelectItem label="Pending" value="pending" />
                  <SelectItem label="Suspended" value="suspended" />
                  <SelectItem label="Rejected" value="rejected" />
                </SelectContent>
              </SelectPortal>
            </Select>

            <Select value={categoryFilter} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectInput placeholder="Category" />
                <SelectIcon as={ChevronDownIcon} />
              </SelectTrigger>
              <SelectPortal>
                <SelectBackdrop />
                <SelectContent>
                  <SelectDragIndicatorWrapper>
                    <SelectDragIndicator />
                  </SelectDragIndicatorWrapper>
                  <SelectItem label="All Categories" value="" />
                  <SelectItem label="Electronics" value="Electronics" />
                  <SelectItem label="Fashion" value="Fashion" />
                  <SelectItem label="Home & Garden" value="Home & Garden" />
                </SelectContent>
              </SelectPortal>
            </Select>
          </HStack>
        </CardBody>
      </Card>

      {/* Vendors Table */}
      <Card>
        <CardBody className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vendor</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Products</TableHead>
                <TableHead>Sales</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Commission</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {vendors.map((vendor) => (
                <TableRow key={vendor.id}>
                  <TableData>
                    <HStack space="sm" className="items-center">
                      <Avatar size="sm">
                        <AvatarFallbackText>{vendor.name}</AvatarFallbackText>
                        {vendor.avatar && <AvatarImage source={{ uri: vendor.avatar }} />}
                      </Avatar>
                      <VStack>
                        <Text className="font-medium">{vendor.name}</Text>
                        <Text className="text-xs text-typography-500">{vendor.email}</Text>
                      </VStack>
                    </HStack>
                  </TableData>
                  <TableData>{getStatusBadge(vendor.status)}</TableData>
                  <TableData>
                    <Text>{vendor.totalProducts}</Text>
                  </TableData>
                  <TableData>
                    <Text>${vendor.totalSales.toLocaleString()}</Text>
                  </TableData>
                  <TableData>
                    <Text>{vendor.rating > 0 ? vendor.rating.toFixed(1) : "N/A"}</Text>
                  </TableData>
                  <TableData>
                    <Text>{vendor.commission}%</Text>
                  </TableData>
                  <TableData>{renderActionButtons(vendor)}</TableData>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <HStack space="sm" className="justify-center">
          <Button
            size="sm"
            variant="outline"
            onPress={() => onPageChange?.(currentPage - 1)}
            isDisabled={currentPage === 1}
          >
            <ButtonText>Previous</ButtonText>
          </Button>
          
          <Text className="px-4 py-2">
            Page {currentPage} of {totalPages}
          </Text>
          
          <Button
            size="sm"
            variant="outline"
            onPress={() => onPageChange?.(currentPage + 1)}
            isDisabled={currentPage === totalPages}
          >
            <ButtonText>Next</ButtonText>
          </Button>
        </HStack>
      )}
    </VStack>
  );
});

VendorManagement.displayName = "VendorManagement";
export default VendorManagement;