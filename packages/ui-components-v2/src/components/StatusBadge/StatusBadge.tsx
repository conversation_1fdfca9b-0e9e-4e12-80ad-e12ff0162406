"use client"

import React from 'react'
import { View } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { CheckIcon, ClockIcon, XIcon, AlertTriangleIcon, InfoIcon } from 'lucide-react-native'

// Types
export type StatusType = 'success' | 'warning' | 'error' | 'info' | 'neutral' | 'pending'

export interface StatusBadgeProps {
  status: StatusType | string
  label?: string
  showIcon?: boolean
  variant?: 'default' | 'outline' | 'subtle'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

// Styles
const badgeStyles = tva({
  base: "flex-row items-center justify-center rounded-full font-medium",
  variants: {
    variant: {
      default: "",
      outline: "border-2 bg-transparent",
      subtle: "",
    },
    status: {
      success: "",
      warning: "",
      error: "",
      info: "",
      neutral: "",
      pending: "",
    },
    size: {
      sm: "px-2 py-1 text-xs",
      md: "px-3 py-1.5 text-sm",
      lg: "px-4 py-2 text-base",
    },
  },
  compoundVariants: [
    // Default variant colors
    {
      variant: "default",
      status: "success",
      class: "bg-success-500 text-white",
    },
    {
      variant: "default",
      status: "warning",
      class: "bg-warning-500 text-white",
    },
    {
      variant: "default",
      status: "error",
      class: "bg-error-500 text-white",
    },
    {
      variant: "default",
      status: "info",
      class: "bg-info-500 text-white",
    },
    {
      variant: "default",
      status: "neutral",
      class: "bg-outline-500 text-white",
    },
    {
      variant: "default",
      status: "pending",
      class: "bg-warning-500 text-white",
    },
    // Outline variant colors
    {
      variant: "outline",
      status: "success",
      class: "border-success-500 text-success-700",
    },
    {
      variant: "outline",
      status: "warning",
      class: "border-warning-500 text-warning-700",
    },
    {
      variant: "outline",
      status: "error",
      class: "border-error-500 text-error-700",
    },
    {
      variant: "outline",
      status: "info",
      class: "border-info-500 text-info-700",
    },
    {
      variant: "outline",
      status: "neutral",
      class: "border-outline-500 text-outline-700",
    },
    {
      variant: "outline",
      status: "pending",
      class: "border-warning-500 text-warning-700",
    },
    // Subtle variant colors
    {
      variant: "subtle",
      status: "success",
      class: "bg-success-100 text-success-700",
    },
    {
      variant: "subtle",
      status: "warning",
      class: "bg-warning-100 text-warning-700",
    },
    {
      variant: "subtle",
      status: "error",
      class: "bg-error-100 text-error-700",
    },
    {
      variant: "subtle",
      status: "info",
      class: "bg-info-100 text-info-700",
    },
    {
      variant: "subtle",
      status: "neutral",
      class: "bg-outline-100 text-outline-700",
    },
    {
      variant: "subtle",
      status: "pending",
      class: "bg-warning-100 text-warning-700",
    },
  ],
})

/**
 * StatusBadge component for displaying status information
 * 
 * Features:
 * - Multiple status types with appropriate colors
 * - Different variants (default, outline, subtle)
 * - Optional icons
 * - Multiple sizes
 * - Custom status mapping for business logic
 */
export function StatusBadge({
  status,
  label,
  showIcon = false,
  variant = 'subtle',
  size = 'sm',
  className,
}: StatusBadgeProps) {
  // Map common business statuses to standard status types
  const getStatusType = (status: string): StatusType => {
    const statusMap: Record<string, StatusType> = {
      // Order statuses
      'pending': 'pending',
      'accepted': 'info',
      'preparing': 'info',
      'ready': 'success',
      'picked_up': 'success',
      'en_route': 'success',
      'delivered': 'success',
      'cancelled': 'error',
      
      // Payment statuses
      'paid': 'success',
      'failed': 'error',
      'processing': 'pending',
      
      // General statuses
      'active': 'success',
      'inactive': 'neutral',
      'open': 'success',
      'closed': 'error',
      'available': 'success',
      'unavailable': 'error',
      'online': 'success',
      'offline': 'neutral',
      'verified': 'success',
      'unverified': 'warning',
      'approved': 'success',
      'rejected': 'error',
      'draft': 'neutral',
      'published': 'success',
    }

    return statusMap[status.toLowerCase()] || 'neutral'
  }

  const statusType = getStatusType(status)
  const displayLabel = label || status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ')

  const getStatusIcon = () => {
    if (!showIcon) return null

    const iconSize = size === 'sm' ? 12 : size === 'md' ? 14 : 16
    const iconClass = variant === 'default' ? 'text-white' : 
                     variant === 'outline' ? `text-${statusType}-700` : 
                     `text-${statusType}-700`

    switch (statusType) {
      case 'success':
        return <CheckIcon size={iconSize} className={iconClass} />
      case 'warning':
        return <AlertTriangleIcon size={iconSize} className={iconClass} />
      case 'error':
        return <XIcon size={iconSize} className={iconClass} />
      case 'info':
        return <InfoIcon size={iconSize} className={iconClass} />
      case 'pending':
        return <ClockIcon size={iconSize} className={iconClass} />
      default:
        return <InfoIcon size={iconSize} className={iconClass} />
    }
  }

  return (
    <View className={badgeStyles({ 
      variant, 
      status: statusType, 
      size,
      class: className 
    })}>
      {getStatusIcon()}
      <Text className={showIcon ? 'ml-1' : ''}>
        {displayLabel}
      </Text>
    </View>
  )
}

// Export variants
export const StatusBadgeOutline = (props: Omit<StatusBadgeProps, 'variant'>) => (
  <StatusBadge {...props} variant="outline" />
)

export const StatusBadgeDefault = (props: Omit<StatusBadgeProps, 'variant'>) => (
  <StatusBadge {...props} variant="default" />
)

export const StatusBadgeSubtle = (props: Omit<StatusBadgeProps, 'variant'>) => (
  <StatusBadge {...props} variant="subtle" />
)
