import { Databases, Client } from 'node-appwrite';
import { notificationService } from './notifications';

interface OrderItem {
  itemId: string;
  quantity: number;
  price: number;
  name: string;
}

interface CreateOrderData {
  customerId: string;
  vendorId: string;
  items: OrderItem[];
  subtotal: number;
  discount?: number;
  total: number;
  paymentMethod: string;
  deliveryAddress: {
    street: string;
    city: string;
    postalCode: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  notes?: string;
}

export class OrderService {
  constructor(private databases: Databases) {}

  async createOrder(orderData: CreateOrderData) {
    try {
      // Create the order document
      const order = await this.databases.createDocument(
        'hvppyplug-main',
        'orders',
        'unique()',
        {
          customerId: orderData.customerId,
          vendorId: orderData.vendorId,
          items: JSON.stringify(orderData.items),
          subtotal: orderData.subtotal,
          discount: orderData.discount || 0,
          total: orderData.total,
          status: 'pending',
          paymentMethod: orderData.paymentMethod,
          paymentStatus: 'pending',
          deliveryAddress: JSON.stringify(orderData.deliveryAddress),
          notes: orderData.notes || '',
        }
      );

      // Get vendor information for notification
      const vendor = await this.databases.getDocument(
        'hvppyplug-main',
        'vendors',
        orderData.vendorId
      );

      // Get customer information
      const customer = await this.databases.getDocument(
        'hvppyplug-main',
        'users',
        orderData.customerId
      );

      // Send notification to vendor (if they have push token)
      if (vendor.pushToken) {
        await notificationService.sendNewOrderToVendor(
          vendor.pushToken,
          order.$id,
          customer.name || 'Customer',
          orderData.total
        );
      }

      return order;
    } catch (error: any) {
      throw new Error(`Failed to create order: ${error.message}`);
    }
  }

  async updateOrderStatus(orderId: string, newStatus: string, updatedBy: string) {
    try {
      // Get current order
      const order = await this.databases.getDocument(
        'hvppyplug-main',
        'orders',
        orderId
      );

      // Update order status
      const updatedOrder = await this.databases.updateDocument(
        'hvppyplug-main',
        'orders',
        orderId,
        {
          status: newStatus,
          updatedAt: new Date().toISOString(),
          updatedBy,
        }
      );

      // Send status update notification to customer
      const customer = await this.databases.getDocument(
        'hvppyplug-main',
        'users',
        order.customerId
      );

      const vendor = await this.databases.getDocument(
        'hvppyplug-main',
        'vendors',
        order.vendorId
      );

      if (customer.pushToken) {
        await notificationService.sendOrderStatusUpdate(
          customer.pushToken,
          orderId,
          newStatus,
          vendor.name
        );
      }

      // Special handling for certain status changes
      if (newStatus === 'accepted') {
        // Find available runners for assignment
        await this.findAndAssignRunner(orderId);
      }

      return updatedOrder;
    } catch (error: any) {
      throw new Error(`Failed to update order status: ${error.message}`);
    }
  }

  async assignRunner(orderId: string, runnerId: string) {
    try {
      const updatedOrder = await this.databases.updateDocument(
        'hvppyplug-main',
        'orders',
        orderId,
        {
          runnerId,
          status: 'assigned',
          assignedAt: new Date().toISOString(),
        }
      );

      // Get order details for runner notification
      const order = await this.databases.getDocument(
        'hvppyplug-main',
        'orders',
        orderId
      );

      const vendor = await this.databases.getDocument(
        'hvppyplug-main',
        'vendors',
        order.vendorId
      );

      const runner = await this.databases.getDocument(
        'hvppyplug-main',
        'users',
        runnerId
      );

      // Calculate estimated earnings (you can adjust this logic)
      const estimatedEarnings = Math.max(15, order.total * 0.1);

      // Send job assignment notification to runner
      if (runner.pushToken) {
        await notificationService.sendJobAssignmentToRunner(
          runner.pushToken,
          orderId,
          vendor.name,
          estimatedEarnings
        );
      }

      return updatedOrder;
    } catch (error: any) {
      throw new Error(`Failed to assign runner: ${error.message}`);
    }
  }

  async findAndAssignRunner(orderId: string) {
    try {
      // Get available runners (simplified - you'd want more sophisticated matching)
      const availableRunners = await this.databases.listDocuments(
        'hvppyplug-main',
        'users',
        [
          'equal("role", "runner")',
          'equal("status", "online")',
          'equal("available", true)'
        ]
      );

      if (availableRunners.documents.length > 0) {
        // For now, assign to the first available runner
        // In production, you'd want distance-based assignment
        const selectedRunner = availableRunners.documents[0];
        await this.assignRunner(orderId, selectedRunner.$id);
      } else {
        // No runners available, keep order in accepted state
        console.log(`No runners available for order ${orderId}`);
      }
    } catch (error: any) {
      console.error(`Failed to find runner for order ${orderId}:`, error.message);
    }
  }

  async getOrderHistory(userId: string, role: 'customer' | 'vendor' | 'runner') {
    try {
      let query: string[];
      
      switch (role) {
        case 'customer':
          query = [`equal("customerId", "${userId}")`];
          break;
        case 'vendor':
          query = [`equal("vendorId", "${userId}")`];
          break;
        case 'runner':
          query = [`equal("runnerId", "${userId}")`];
          break;
        default:
          throw new Error('Invalid role');
      }

      const orders = await this.databases.listDocuments(
        'hvppyplug-main',
        'orders',
        query
      );

      return orders.documents;
    } catch (error: any) {
      throw new Error(`Failed to get order history: ${error.message}`);
    }
  }

  async getOrderDetails(orderId: string) {
    try {
      const order = await this.databases.getDocument(
        'hvppyplug-main',
        'orders',
        orderId
      );

      // Get related data
      const [customer, vendor, runner] = await Promise.all([
        this.databases.getDocument('hvppyplug-main', 'users', order.customerId),
        this.databases.getDocument('hvppyplug-main', 'vendors', order.vendorId),
        order.runnerId ? this.databases.getDocument('hvppyplug-main', 'users', order.runnerId) : null
      ]);

      return {
        ...order,
        customer: {
          id: customer.$id,
          name: customer.name,
          phone: customer.phone,
        },
        vendor: {
          id: vendor.$id,
          name: vendor.name,
          location: vendor.location,
        },
        runner: runner ? {
          id: runner.$id,
          name: runner.name,
          phone: runner.phone,
        } : null,
        items: JSON.parse(order.items),
        deliveryAddress: JSON.parse(order.deliveryAddress),
      };
    } catch (error: any) {
      throw new Error(`Failed to get order details: ${error.message}`);
    }
  }

  async calculateDeliveryFee(
    vendorCoords: { lat: number; lng: number },
    customerCoords: { lat: number; lng: number }
  ): Promise<number> {
    // Simple distance-based calculation (you can enhance this)
    const distance = this.calculateDistance(
      vendorCoords.lat,
      vendorCoords.lng,
      customerCoords.lat,
      customerCoords.lng
    );

    // Base delivery fee + per km charge
    const baseFee = 15; // R15 base fee
    const perKmRate = 5; // R5 per km
    
    return Math.round((baseFee + (distance * perKmRate)) * 100) / 100;
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
}

export const orderService = (databases: Databases) => new OrderService(databases);
