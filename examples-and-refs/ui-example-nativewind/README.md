# gluestack-ui v2 Dashboard Application

## Installation
Ensure you have Node.js and Expo CLI installed on your machine.

Clone this repository:

```bash
<NAME_EMAIL>:gluestack/ui-example-nativewind.git dashboard-app
cd dashboard-app
```

Install dependencies:

```bash
npm install
 or
yarn
```

Run the Dashboard app:
```bash
expo start
```

This will start the Expo development server, and you can choose to run the app on an emulator, a physical device, or the web.

For detailed information on each component and its props, refer to the documentation provided. => https://gluestack.io/ui/docs/home/<USER>/introduction

## Give it a shot!
[Try it in your browser](https://ui-example-nativewind.vercel.app) or scan the QR code below <br/><br/>
<img src="https://qr.expo.dev/eas-update?slug=exp&projectId=42cce9b5-9fbe-4572-92ba-fc43b2437a85&groupId=5a598f1d-f9ad-4f4e-ab64-da240438da2b&host=u.expo.dev" alt="expo-icon" width="200" height="200"/> <br/>
with the Expo Go app on your phone.

## Created By GeekyAnts

GeekyAnts is a team of React Native experts who love open-source and solving developers problems. We’ve been working on React Native since 2015 and have designed and built React Native apps for almost 200+ clients across the globe. Our clients include startups to big enterprises! Need help with your React Native app?

[Contact Us](https://geekyants.com/?utm_source=gluestack-ui-home&utm_medium=home-page&utm_campaign=meet-the-creators)

## Contributing
We welcome contributions from the community! If you want to report bugs, suggest improvements, or add new features, please create an issue, we will actively look into it.

## License
This project is licensed under the MIT License.

## Contact
For any inquiries or feedback join our Discord Channel => https://discord.com/invite/V5SU7HZSAQ
