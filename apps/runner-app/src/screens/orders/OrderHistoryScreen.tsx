import React, { useEffect, useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { History, Package } from 'lucide-react-native'
import { useNavigation } from '@react-navigation/native'
import type { NativeStackNavigationProp } from '@react-navigation/native-stack'

import { useTheme } from '../../providers/ThemeProvider'
import { OrderCard } from '../../components/business'
import { useOrderStore } from '../../stores/orderStore'
import { useAuthStore } from '../../stores/authStore'
import type { OrdersStackParamList } from '../../types/navigation'
import type { Order } from '../../types'

type NavigationProp = NativeStackNavigationProp<OrdersStackParamList, 'OrderHistory'>

export function OrderHistoryScreen() {
  const { theme } = useTheme()
  const navigation = useNavigation<NavigationProp>()
  const { runner } = useAuthStore()
  
  const [historyOrders, setHistoryOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (runner) {
      fetchOrderHistory()
    }
  }, [runner])

  const fetchOrderHistory = async () => {
    if (!runner) return

    setLoading(true)
    setError(null)

    try {
      // In a real app, this would fetch completed/cancelled orders from Appwrite
      // For now, we'll use mock data
      const mockHistoryOrders: Order[] = [
        {
          id: 'hist-1',
          customerId: 'cust1',
          customerName: 'Sarah Johnson',
          customerPhone: '+27 71 234 5678',
          vendorId: 'vendor1',
          vendorName: 'KFC Soweto',
          vendorAddress: '123 Main Road, Soweto',
          items: [
            { id: 'item1', name: 'Zinger Burger', quantity: 2, price: 15.50, total: 31.00 },
            { id: 'item2', name: 'Regular Chips', quantity: 1, price: 12.50, total: 12.50 },
          ],
          subtotal: 43.50,
          deliveryFee: 15.00,
          total: 58.50,
          status: 'delivered',
          paymentMethod: 'card',
          paymentStatus: 'paid',
          deliveryAddress: {
            street: '123 Vilakazi Street, Orlando West',
            city: 'Soweto',
            province: 'Gauteng',
            postalCode: '1804',
            coordinates: { latitude: -26.2041, longitude: 27.8650 },
            instructions: 'Ring the bell twice',
          },
          estimatedDeliveryTime: '15 min',
          actualDeliveryTime: '2025-01-23T11:45:00Z',
          createdAt: '2025-01-23T10:30:00Z',
          updatedAt: '2025-01-23T11:45:00Z',
        },
        {
          id: 'hist-2',
          customerId: 'cust2',
          customerName: 'Michael Dlamini',
          customerPhone: '+27 82 345 6789',
          vendorId: 'vendor2',
          vendorName: 'Nandos Maponya Mall',
          vendorAddress: '456 Mall Road, Soweto',
          items: [
            { id: 'item4', name: 'Quarter Chicken', quantity: 1, price: 45.00, total: 45.00 },
          ],
          subtotal: 45.00,
          deliveryFee: 12.00,
          total: 57.00,
          status: 'delivered',
          paymentMethod: 'cash',
          paymentStatus: 'paid',
          deliveryAddress: {
            street: '456 Klipspruit Valley Road',
            city: 'Soweto',
            province: 'Gauteng',
            postalCode: '1809',
            coordinates: { latitude: -26.2341, longitude: 27.8750 },
          },
          estimatedDeliveryTime: '12 min',
          actualDeliveryTime: '2025-01-22T16:30:00Z',
          createdAt: '2025-01-22T16:00:00Z',
          updatedAt: '2025-01-22T16:30:00Z',
        },
      ]

      setHistoryOrders(mockHistoryOrders)
    } catch (err: any) {
      setError(err.message || 'Failed to fetch order history')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    await fetchOrderHistory()
  }

  const handleViewDetails = (orderId: string) => {
    navigation.navigate('OrderDetails', { orderId })
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-ZA', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  const getTotalEarnings = () => {
    return historyOrders.reduce((total, order) => {
      // Assuming delivery fee is the runner's earning
      return total + order.deliveryFee
    }, 0)
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <View style={styles.headerContent}>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
              Order History
            </Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.gray[600] }]}>
              {historyOrders.length} completed orders
            </Text>
          </View>
          
          {/* Earnings Summary */}
          <View style={[styles.earningsSummary, { backgroundColor: theme.colors.success[100] }]}>
            <Text style={[styles.earningsLabel, { color: theme.colors.success[700] }]}>
              Total Earned
            </Text>
            <Text style={[styles.earningsAmount, { color: theme.colors.success[700] }]}>
              R{getTotalEarnings().toFixed(2)}
            </Text>
          </View>
        </View>
      </View>

      <ScrollView 
        showsVerticalScrollIndicator={false} 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary[600]}
          />
        }
      >
        {historyOrders.length > 0 ? (
          historyOrders.map((order) => (
            <View key={order.id} style={styles.orderContainer}>
              <View style={styles.orderHeader}>
                <Text style={[styles.orderDate, { color: theme.colors.gray[600] }]}>
                  {formatDate(order.createdAt)}
                </Text>
                <Text style={[styles.orderEarning, { color: theme.colors.success[600] }]}>
                  +R{order.deliveryFee.toFixed(2)}
                </Text>
              </View>
              
              <OrderCard
                order={order}
                onViewDetails={handleViewDetails}
                showActions={false}
                compact={true}
              />
            </View>
          ))
        ) : (
          <View style={[styles.emptyState, { backgroundColor: theme.colors.white }]}>
            <History size={64} color={theme.colors.gray[400]} />
            <Text style={[styles.emptyTitle, { color: theme.colors.gray[900] }]}>
              No Order History
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.gray[600] }]}>
              Your completed orders will appear here once you start making deliveries.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  earningsSummary: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  earningsLabel: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    marginBottom: 4,
  },
  earningsAmount: {
    fontSize: 18,
    fontWeight: '700',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  orderContainer: {
    marginBottom: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderDate: {
    fontSize: 14,
    fontWeight: '500',
  },
  orderEarning: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
