{"name": "@hvppyplug/vendor-app", "version": "1.0.0", "main": "index.ts", "private": true, "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "clean": "expo r -c", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx"}, "dependencies": {"@hvppyplug/common": "workspace:*", "@hvppyplug/mobile-services": "workspace:*", "@hvppyplug/compound-components": "workspace:*", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "expo-notifications": "~0.30.0", "expo-device": "~7.0.1", "expo-constants": "~17.1.7", "expo-application": "~5.9.1", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.20.2", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.0.9", "@react-navigation/stack": "^7.1.1", "@react-navigation/bottom-tabs": "^7.1.5", "@tanstack/react-query": "^5.28.4", "zustand": "^4.5.2", "expo-router": "~4.0.15", "react-native-screens": "^4.13.1", "react-native-safe-area-context": "^4.14.0", "lucide-react-native": "^0.447.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1"}}