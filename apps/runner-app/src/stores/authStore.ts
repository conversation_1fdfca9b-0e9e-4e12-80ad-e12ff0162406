import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { AppwriteService } from '@hvppyplug/mobile-services'
import type { Runner, RunnerStatus, VehicleType } from '../types'

// Auth State Interface
interface AuthState {
  // State
  runner: Runner | null
  isAuthenticated: boolean
  hasCompletedOnboarding: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  forgotPassword: (email: string) => Promise<void>
  resetPassword: (token: string, password: string) => Promise<void>
  verifyOTP: (email: string, otp: string) => Promise<void>
  updateProfile: (data: Partial<Runner>) => Promise<void>
  updateOnboardingStatus: (completed: boolean) => void
  clearError: () => void
  refreshRunner: () => Promise<void>
}

// Registration Data Interface
interface RegisterData {
  name: string
  email: string
  password: string
  phone: string
  vehicleType: VehicleType
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial State
      runner: null,
      isAuthenticated: false,
      hasCompletedOnboarding: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })
        try {
          const appwrite = AppwriteService.getInstance()
          const session = await appwrite.account.createEmailPasswordSession(email, password)
          
          if (session) {
            const user = await appwrite.account.get()
            
            // Fetch runner profile from database
            const runnerProfile = await appwrite.databases.getDocument(
              'hvppyplug-main',
              'users',
              user.$id
            )

            if (runnerProfile.role !== 'runner') {
              throw new Error('This account is not registered as a runner')
            }

            const runner: Runner = {
              id: user.$id,
              name: user.name,
              email: user.email,
              phone: runnerProfile.phone || '',
              avatar: runnerProfile.avatarUrl,
              status: runnerProfile.status || 'pending_approval',
              vehicleType: runnerProfile.vehicleType || 'bicycle',
              vehicleDetails: runnerProfile.vehicleDetails || {},
              documents: runnerProfile.documents || {},
              bankDetails: runnerProfile.bankDetails || {},
              rating: runnerProfile.rating || 0,
              totalDeliveries: runnerProfile.totalDeliveries || 0,
              isOnline: false,
              createdAt: user.$createdAt,
              updatedAt: user.$updatedAt,
            }

            set({
              runner,
              isAuthenticated: true,
              isLoading: false,
            })
          }
        } catch (error: any) {
          set({
            error: error.message || 'Login failed',
            isLoading: false,
          })
          throw error
        }
      },

      register: async (data: RegisterData) => {
        set({ isLoading: true, error: null })
        try {
          const appwrite = AppwriteService.getInstance()
          
          // Create account
          const account = await appwrite.account.create(
            'unique()',
            data.email,
            data.password,
            data.name
          )

          // Create user profile in database
          await appwrite.databases.createDocument(
            'hvppyplug-main',
            'users',
            account.$id,
            {
              name: data.name,
              phone: data.phone,
              role: 'runner',
              status: 'pending_approval',
              vehicleType: data.vehicleType,
              vehicleDetails: {},
              documents: {},
              bankDetails: {},
              rating: 0,
              totalDeliveries: 0,
            }
          )

          // Send verification email
          await appwrite.account.createVerification('http://localhost:3000/verify')

          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Registration failed',
            isLoading: false,
          })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })
        try {
          const appwrite = AppwriteService.getInstance()
          await appwrite.account.deleteSession('current')
          
          set({
            runner: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          // Even if logout fails, clear local state
          set({
            runner: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        }
      },

      forgotPassword: async (email: string) => {
        set({ isLoading: true, error: null })
        try {
          const appwrite = AppwriteService.getInstance()
          await appwrite.account.createRecovery(
            email,
            'http://localhost:3000/reset-password'
          )
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to send reset email',
            isLoading: false,
          })
          throw error
        }
      },

      resetPassword: async (token: string, password: string) => {
        set({ isLoading: true, error: null })
        try {
          const appwrite = AppwriteService.getInstance()
          await appwrite.account.updateRecovery(
            token,
            password,
            password
          )
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to reset password',
            isLoading: false,
          })
          throw error
        }
      },

      verifyOTP: async (email: string, otp: string) => {
        set({ isLoading: true, error: null })
        try {
          // This would integrate with your OTP verification system
          // For now, we'll simulate the verification
          await new Promise(resolve => setTimeout(resolve, 1000))
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'OTP verification failed',
            isLoading: false,
          })
          throw error
        }
      },

      updateProfile: async (data: Partial<Runner>) => {
        set({ isLoading: true, error: null })
        try {
          const { runner } = get()
          if (!runner) throw new Error('No runner logged in')

          const appwrite = AppwriteService.getInstance()
          
          // Update user account if name or email changed
          if (data.name || data.email) {
            await appwrite.account.updateName(data.name || runner.name)
            if (data.email && data.email !== runner.email) {
              await appwrite.account.updateEmail(data.email, runner.email)
            }
          }

          // Update runner profile in database
          await appwrite.databases.updateDocument(
            'hvppyplug-main',
            'users',
            runner.id,
            {
              ...data,
              updatedAt: new Date().toISOString(),
            }
          )

          // Update local state
          set({
            runner: { ...runner, ...data },
            isLoading: false,
          })
        } catch (error: any) {
          set({
            error: error.message || 'Failed to update profile',
            isLoading: false,
          })
          throw error
        }
      },

      updateOnboardingStatus: (completed: boolean) => {
        set({ hasCompletedOnboarding: completed })
      },

      clearError: () => {
        set({ error: null })
      },

      refreshRunner: async () => {
        set({ isLoading: true })
        try {
          const appwrite = AppwriteService.getInstance()
          const user = await appwrite.account.get()
          
          const runnerProfile = await appwrite.databases.getDocument(
            'hvppyplug-main',
            'users',
            user.$id
          )

          const { runner: currentRunner } = get()
          if (currentRunner) {
            const updatedRunner: Runner = {
              ...currentRunner,
              name: user.name,
              email: user.email,
              phone: runnerProfile.phone,
              avatar: runnerProfile.avatarUrl,
              status: runnerProfile.status,
              vehicleType: runnerProfile.vehicleType,
              vehicleDetails: runnerProfile.vehicleDetails,
              documents: runnerProfile.documents,
              bankDetails: runnerProfile.bankDetails,
              rating: runnerProfile.rating,
              totalDeliveries: runnerProfile.totalDeliveries,
              updatedAt: user.$updatedAt,
            }

            set({
              runner: updatedRunner,
              isLoading: false,
            })
          }
        } catch (error: any) {
          set({
            error: error.message || 'Failed to refresh runner data',
            isLoading: false,
          })
        }
      },
    }),
    {
      name: 'runner-auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        runner: state.runner,
        isAuthenticated: state.isAuthenticated,
        hasCompletedOnboarding: state.hasCompletedOnboarding,
      }),
    }
  )
)

// Export types for use in components
export type { RegisterData }
