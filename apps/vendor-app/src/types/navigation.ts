import type { StackScreenProps } from '@react-navigation/stack'
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs'
import type { CompositeScreenProps } from '@react-navigation/native'

// Auth Stack
export type AuthStackParamList = {
  Onboarding: undefined
  Login: undefined
  Register: undefined
  ForgotPassword: undefined
}

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = 
  StackScreenProps<AuthStackParamList, T>

// Main Tab Navigator
export type MainTabParamList = {
  Dashboard: undefined
  Orders: undefined
  Services: undefined
  Calendar: undefined
  Chat: undefined
  Profile: undefined
}

export type MainTabScreenProps<T extends keyof MainTabParamList> = 
  BottomTabScreenProps<MainTabParamList, T>

// Dashboard Stack
export type DashboardStackParamList = {
  DashboardHome: undefined
  Earnings: undefined
}

export type DashboardStackScreenProps<T extends keyof DashboardStackParamList> = 
  CompositeScreenProps<
    StackScreenProps<DashboardStackParamList, T>,
    MainTabScreenProps<'Dashboard'>
  >

// Orders Stack
export type OrdersStackParamList = {
  OrdersList: undefined
  OrderDetail: { orderId: string }
}

export type OrdersStackScreenProps<T extends keyof OrdersStackParamList> = 
  CompositeScreenProps<
    StackScreenProps<OrdersStackParamList, T>,
    MainTabScreenProps<'Orders'>
  >

// Services Stack
export type ServicesStackParamList = {
  ServicesList: undefined
  ServiceForm: { serviceId?: string }
}

export type ServicesStackScreenProps<T extends keyof ServicesStackParamList> = 
  CompositeScreenProps<
    StackScreenProps<ServicesStackParamList, T>,
    MainTabScreenProps<'Services'>
  >

// Chat Stack
export type ChatStackParamList = {
  ChatList: undefined
  Chat: { orderId: string; customerId: string }
}

export type ChatStackScreenProps<T extends keyof ChatStackParamList> = 
  CompositeScreenProps<
    StackScreenProps<ChatStackParamList, T>,
    MainTabScreenProps<'Chat'>
  >

// Profile Stack
export type ProfileStackParamList = {
  ProfileHome: undefined
  EditProfile: undefined
  BusinessInfo: undefined
  Documents: undefined
  Settings: undefined
}

export type ProfileStackScreenProps<T extends keyof ProfileStackParamList> = 
  CompositeScreenProps<
    StackScreenProps<ProfileStackParamList, T>,
    MainTabScreenProps<'Profile'>
  >

// Root Stack
export type RootStackParamList = {
  Auth: undefined
  Main: undefined
}

export type RootStackScreenProps<T extends keyof RootStackParamList> = 
  StackScreenProps<RootStackParamList, T>

// Utility types
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
