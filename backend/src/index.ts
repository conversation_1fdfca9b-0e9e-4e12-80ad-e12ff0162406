import { Client, Databases, Storage, Users } from 'node-appwrite';
import { paymentService } from './services/payments';
import { notificationService } from './services/notifications';
import { orderService } from './services/orders';

// Initialize Appwrite SDK
const client = new Client();
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.APPWRITE_PROJECT_ID || 'hvppyplug')
  .setKey(process.env.APPWRITE_API_KEY || '');

const databases = new Databases(client);
const storage = new Storage(client);
const users = new Users(client);

// Main Appwrite function handler
export default async ({ req, res, log, error }: any) => {
  try {
    const { path, method } = req;
    log(`${method} ${path}`);

    // Route handling
    switch (true) {
      // Authentication routes
      case path.startsWith('/auth/login') && method === 'POST':
        return await handleLogin(req, res, { databases, users, log });
      
      case path.startsWith('/auth/verify') && method === 'POST':
        return await handleVerifyOTP(req, res, { databases, users, log });
      
      // Vendor routes
      case path.startsWith('/vendors') && method === 'GET':
        return await handleGetVendors(req, res, { databases, log });
      
      case path.match(/\/vendors\/(.+)\/menu/) && method === 'GET':
        return await handleGetVendorMenu(req, res, { databases, log });
      
      // Order routes
      case path.startsWith('/orders') && method === 'POST':
        return await handleCreateOrder(req, res, { databases, log });
      
      case path.match(/\/orders\/(.+)/) && method === 'GET':
        return await handleGetOrder(req, res, { databases, log });
      
      // Payment routes
      case path.startsWith('/payments/voucher/validate') && method === 'POST':
        return await handleValidateVoucher(req, res, { log });
      
      case path.startsWith('/payments/stripe/intent') && method === 'POST':
        return await handleStripePayment(req, res, { databases, log });
      
      case path.startsWith('/payments/payfast/init') && method === 'POST':
        return await handlePayFastPayment(req, res, { databases, log });
      
      // Notification routes
      case path.startsWith('/notifications/push') && method === 'POST':
        return await handlePushNotification(req, res, { log });
      
      default:
        return res.json({
          message: 'Welcome to HVPPYPlug+ API powered by Appwrite!',
          endpoints: [
            'POST /auth/login',
            'POST /auth/verify',
            'GET /vendors',
            'GET /vendors/:id/menu',
            'POST /orders',
            'GET /orders/:id',
            'POST /payments/voucher/validate',
            'POST /payments/stripe/intent',
            'POST /payments/payfast/init',
            'POST /notifications/push'
          ]
        }, 200);
    }
  } catch (err: any) {
    error(`Function error: ${err.message}`);
    return res.json({ error: err.message }, 500);
  }
};

// Auth handlers
async function handleLogin(req: any, res: any, { databases, users, log }: any) {
  const { phone } = req.body;
  
  if (!phone) {
    return res.json({ error: 'Phone number is required' }, 400);
  }
  
  // Generate OTP and send via SMS (implement with your SMS provider)
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  
  // Store OTP in database temporarily
  try {
    await databases.createDocument(
      'hvppyplug-main',
      'otp-codes',
      'unique()',
      {
        phone,
        otp,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
      }
    );
    
    log(`OTP sent to ${phone}`);
    return res.json({ message: 'OTP sent successfully', phone }, 200);
  } catch (err: any) {
    log(`OTP creation error: ${err.message}`);
    return res.json({ error: 'Failed to send OTP' }, 500);
  }
}

async function handleVerifyOTP(req: any, res: any, { databases, users, log }: any) {
  const { phone, otp } = req.body;
  
  if (!phone || !otp) {
    return res.json({ error: 'Phone and OTP are required' }, 400);
  }
  
  try {
    // Verify OTP
    const otpDocs = await databases.listDocuments(
      'hvppyplug-main',
      'otp-codes',
      [`equal("phone", "${phone}")`, `equal("otp", "${otp}")`]
    );
    
    if (otpDocs.documents.length === 0) {
      return res.json({ error: 'Invalid OTP' }, 400);
    }
    
    const otpDoc = otpDocs.documents[0];
    if (new Date() > new Date(otpDoc.expiresAt)) {
      return res.json({ error: 'OTP expired' }, 400);
    }
    
    // Check if user exists
    const userDocs = await databases.listDocuments(
      'hvppyplug-main',
      'users',
      [`equal("phone", "${phone}")`]
    );
    
    let user;
    if (userDocs.documents.length > 0) {
      user = userDocs.documents[0];
    } else {
      // Create new user
      user = await databases.createDocument(
        'hvppyplug-main',
        'users',
        'unique()',
        {
          phone,
          name: '',
          role: 'customer'
        }
      );
    }
    
    // Clean up OTP
    await databases.deleteDocument('hvppyplug-main', 'otp-codes', otpDoc.$id);
    
    return res.json({ 
      message: 'Login successful', 
      user: {
        id: user.$id,
        phone: user.phone,
        name: user.name,
        role: user.role
      }
    }, 200);
    
  } catch (err: any) {
    log(`OTP verification error: ${err.message}`);
    return res.json({ error: 'Failed to verify OTP' }, 500);
  }
}

// Vendor handlers
async function handleGetVendors(req: any, res: any, { databases, log }: any) {
  try {
    const vendors = await databases.listDocuments(
      'hvppyplug-main',
      'vendors',
      [`equal("status", "active")`]
    );
    
    return res.json({ vendors: vendors.documents }, 200);
  } catch (err: any) {
    log(`Get vendors error: ${err.message}`);
    return res.json({ error: 'Failed to fetch vendors' }, 500);
  }
}

async function handleGetVendorMenu(req: any, res: any, { databases, log }: any) {
  const vendorId = req.path.split('/')[2];
  
  try {
    const menuItems = await databases.listDocuments(
      'hvppyplug-main',
      'menu-items',
      [`equal("vendorId", "${vendorId}")`, `equal("available", true)`]
    );
    
    return res.json({ menuItems: menuItems.documents }, 200);
  } catch (err: any) {
    log(`Get vendor menu error: ${err.message}`);
    return res.json({ error: 'Failed to fetch menu' }, 500);
  }
}

// Order handlers
async function handleCreateOrder(req: any, res: any, { databases, log }: any) {
  const { customerId, vendorId, items, subtotal, total, paymentMethod } = req.body;
  
  try {
    const order = await databases.createDocument(
      'hvppyplug-main',
      'orders',
      'unique()',
      {
        customerId,
        vendorId,
        items: JSON.stringify(items),
        subtotal,
        total,
        status: 'pending',
        paymentMethod,
        paymentStatus: 'pending'
      }
    );
    
    return res.json({ order }, 201);
  } catch (err: any) {
    log(`Create order error: ${err.message}`);
    return res.json({ error: 'Failed to create order' }, 500);
  }
}

async function handleGetOrder(req: any, res: any, { databases, log }: any) {
  const orderId = req.path.split('/')[2];
  
  try {
    const order = await databases.getDocument(
      'hvppyplug-main',
      'orders',
      orderId
    );
    
    return res.json({ order }, 200);
  } catch (err: any) {
    log(`Get order error: ${err.message}`);
    return res.json({ error: 'Order not found' }, 404);
  }
}

// Payment handlers
async function handleValidateVoucher(req: any, res: any, { log }: any) {
  const { voucherCode } = req.body;
  
  // Implement voucher validation logic
  log(`Validating voucher: ${voucherCode}`);
  
  return res.json({ 
    valid: true, 
    amount: 50, 
    currency: 'ZAR' 
  }, 200);
}

async function handleStripePayment(req: any, res: any, { databases, log }: any) {
  const { orderId, amount } = req.body;
  
  try {
    const paymentIntent = await paymentService.createStripePayment(amount);
    
    return res.json({ 
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id 
    }, 200);
  } catch (err: any) {
    log(`Stripe payment error: ${err.message}`);
    return res.json({ error: 'Payment failed' }, 500);
  }
}

async function handlePayFastPayment(req: any, res: any, { databases, log }: any) {
  const { orderId, amount } = req.body;
  
  try {
    const paymentUrl = await paymentService.createPayFastPayment(orderId, amount);
    
    return res.json({ paymentUrl }, 200);
  } catch (err: any) {
    log(`PayFast payment error: ${err.message}`);
    return res.json({ error: 'Payment failed' }, 500);
  }
}

// Notification handler
async function handlePushNotification(req: any, res: any, { log }: any) {
  const { userId, title, body, data } = req.body;
  
  try {
    await notificationService.sendPushNotification(userId, { title, body, data });
    
    return res.json({ message: 'Notification sent' }, 200);
  } catch (err: any) {
    log(`Push notification error: ${err.message}`);
    return res.json({ error: 'Failed to send notification' }, 500);
  }
}

