import Stripe from 'stripe';
import axios from 'axios';
import crypto from 'crypto';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2023-10-16',
});

export class PaymentService {
  // Stripe Payment Intent
  async createStripePayment(amount: number, currency: string = 'zar') {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: amount * 100, // Amount in cents
        currency,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return paymentIntent;
    } catch (error: any) {
      throw new Error(`Stripe payment failed: ${error.message}`);
    }
  }

  // PayFast Payment URL Generation
  async createPayFastPayment(orderId: string, amount: number) {
    const merchantId = process.env.PAYFAST_MERCHANT_ID || '';
    const merchantKey = process.env.PAYFAST_MERCHANT_KEY || '';
    const passphrase = process.env.PAYFAST_PASSPHRASE || '';
    const sandbox = process.env.PAYFAST_SANDBOX === 'true';

    const baseUrl = sandbox 
      ? 'https://sandbox.payfast.co.za/eng/process'
      : 'https://www.payfast.co.za/eng/process';

    const data = {
      merchant_id: merchantId,
      merchant_key: merchantKey,
      return_url: `${process.env.API_URL}/payments/payfast/return`,
      cancel_url: `${process.env.API_URL}/payments/payfast/cancel`,
      notify_url: `${process.env.API_URL}/payments/payfast/notify`,
      name_first: 'HVPPYPlug+',
      name_last: 'Customer',
      email_address: '<EMAIL>',
      m_payment_id: orderId,
      amount: amount.toFixed(2),
      item_name: `HVPPYPlug+ Order #${orderId}`,
      item_description: 'Food delivery and services',
      custom_int1: orderId,
      custom_str1: 'hvppyplug',
    };

    // Generate signature
    const signature = this.generatePayFastSignature(data, passphrase);
    
    const paymentUrl = `${baseUrl}?${new URLSearchParams({
      ...data,
      signature
    }).toString()}`;

    return paymentUrl;
  }

  // Generate PayFast signature
  private generatePayFastSignature(data: any, passphrase: string): string {
    // Create parameter string
    const paramString = Object.keys(data)
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key])}`)
      .join('&');
    
    // Add passphrase if provided
    const stringToHash = passphrase ? `${paramString}&passphrase=${passphrase}` : paramString;
    
    // Generate MD5 hash
    return crypto.createHash('md5').update(stringToHash).digest('hex');
  }

  // Validate voucher codes
  async validateVoucher(voucherCode: string, provider: '1voucher' | 'ott'): Promise<{
    valid: boolean;
    amount: number;
    currency: string;
  }> {
    try {
      if (provider === '1voucher') {
        return await this.validate1Voucher(voucherCode);
      } else {
        return await this.validateOTTVoucher(voucherCode);
      }
    } catch (error: any) {
      throw new Error(`Voucher validation failed: ${error.message}`);
    }
  }

  private async validate1Voucher(voucherCode: string) {
    const apiKey = process.env.ONE_VOUCHER_API_KEY || '';
    const endpoint = process.env.ONE_VOUCHER_ENDPOINT || 'https://api.1voucher.co.za';
    
    try {
      const response = await axios.post(`${endpoint}/validate`, {
        voucher_code: voucherCode,
        api_key: apiKey,
      });

      return {
        valid: response.data.valid,
        amount: response.data.amount,
        currency: 'ZAR',
      };
    } catch (error: any) {
      return {
        valid: false,
        amount: 0,
        currency: 'ZAR',
      };
    }
  }

  private async validateOTTVoucher(voucherCode: string) {
    const apiKey = process.env.OTT_VOUCHER_API_KEY || '';
    const endpoint = process.env.OTT_VOUCHER_ENDPOINT || 'https://api.ottvouchers.com';
    
    try {
      const response = await axios.get(`${endpoint}/vouchers/${voucherCode}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
        },
      });

      return {
        valid: response.data.status === 'active',
        amount: response.data.value,
        currency: 'ZAR',
      };
    } catch (error: any) {
      return {
        valid: false,
        amount: 0,
        currency: 'ZAR',
      };
    }
  }
}

export const paymentService = new PaymentService();
