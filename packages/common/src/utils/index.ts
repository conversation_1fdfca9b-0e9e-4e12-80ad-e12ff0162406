import { Order } from '../types';

/**
 * Format currency value to South African Rand
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
  }).format(amount);
};

/**
 * Format phone number to South African format
 */
export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.startsWith('27')) {
    const withoutCountryCode = cleaned.slice(2);
    return `+27 ${withoutCountryCode.slice(0, 2)} ${withoutCountryCode.slice(2, 5)} ${withoutCountryCode.slice(5)}`;
  }
  
  if (cleaned.startsWith('0')) {
    const withoutZero = cleaned.slice(1);
    return `+27 ${withoutZero.slice(0, 2)} ${withoutZero.slice(2, 5)} ${withoutZero.slice(5)}`;
  }
  
  return phone;
};

/**
 * Calculate distance between two coordinates (Haversine formula)
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371; // Earth's radius in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Get order status display text and color
 */
export const getOrderStatusInfo = (status: Order['status']) => {
  const statusMap = {
    pending: { text: 'Order Placed', color: '#F59E0B' },
    accepted: { text: 'Preparing', color: '#10B981' },
    picked_up: { text: 'Picked Up', color: '#00C2FF' },
    en_route: { text: 'On the Way', color: '#00C2FF' },
    delivered: { text: 'Delivered', color: '#10B981' },
    cancelled: { text: 'Cancelled', color: '#EF4444' },
  };
  
  return statusMap[status];
};

/**
 * Generate OTP code
 */
export const generateOTP = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

/**
 * Validate South African phone number
 */
export const isValidSAPhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  
  // Check if it's a valid SA number format
  if (cleaned.startsWith('27') && cleaned.length === 11) return true;
  if (cleaned.startsWith('0') && cleaned.length === 10) return true;
  
  return false;
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Generate a unique ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};
