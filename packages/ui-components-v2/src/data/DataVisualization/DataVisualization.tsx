"use client"

import React from 'react'
import { View } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'

// Types
export interface DataVisualizationProps {
  children?: React.ReactNode
  className?: string
  disabled?: boolean
}

// Styles
const containerStyles = tva({
  base: "bg-background-0",
  variants: {
    disabled: {
      true: "opacity-50",
    },
  },
})

export function DataVisualization({
  children,
  className,
  disabled = false,
}: DataVisualizationProps) {
  return (
    <View className={containerStyles({ disabled, class: className })}>
      <Text className="text-typography-900">
        DataVisualization Component
      </Text>
      {children}
    </View>
  )
}
