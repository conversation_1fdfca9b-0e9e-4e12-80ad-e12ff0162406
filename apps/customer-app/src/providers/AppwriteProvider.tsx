import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { AppwriteService } from '@hvppyplug/mobile-services'
import Constants from 'expo-constants'

interface AppwriteContextType {
  isInitialized: boolean
  isConnected: boolean
  error: string | null
}

const AppwriteContext = createContext<AppwriteContextType>({
  isInitialized: false,
  isConnected: false,
  error: null,
})

export const useAppwrite = () => {
  const context = useContext(AppwriteContext)
  if (!context) {
    throw new Error('useAppwrite must be used within an AppwriteProvider')
  }
  return context
}

interface AppwriteProviderProps {
  children: ReactNode
}

export function AppwriteProvider({ children }: AppwriteProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    initializeAppwrite()
  }, [])

  const initializeAppwrite = async () => {
    try {
      setError(null)
      
      // Get configuration from environment variables
      const config = {
        endpoint: Constants.expoConfig?.extra?.appwriteEndpoint || 'https://cloud.appwrite.io/v1',
        projectId: Constants.expoConfig?.extra?.appwriteProjectId || '',
        databaseId: Constants.expoConfig?.extra?.appwriteDatabaseId || 'hvppyplug-main',
        storageId: Constants.expoConfig?.extra?.appwriteStorageId || 'images',
      }

      if (!config.projectId) {
        throw new Error('Appwrite Project ID is not configured. Please check your environment variables.')
      }

      // Initialize Appwrite service
      const appwriteService = AppwriteService.getInstance(config)
      
      // Test connection
      await testConnection(appwriteService)
      
      setIsInitialized(true)
      setIsConnected(true)
      
      console.log('✅ Appwrite initialized successfully')
    } catch (err: any) {
      console.error('❌ Failed to initialize Appwrite:', err)
      setError(err.message || 'Failed to initialize Appwrite')
      setIsInitialized(true) // Still mark as initialized even if connection failed
      setIsConnected(false)
    }
  }

  const testConnection = async (appwriteService: AppwriteService) => {
    try {
      // Try to get account info (will fail if not authenticated, but connection works)
      await appwriteService.account.get()
    } catch (err: any) {
      // If error is about authentication, connection is working
      if (err.code === 401 || err.type === 'general_unauthorized_scope') {
        return // Connection is working, user just not authenticated
      }
      // If it's a network error or other connection issue, throw it
      throw err
    }
  }

  const contextValue: AppwriteContextType = {
    isInitialized,
    isConnected,
    error,
  }

  return (
    <AppwriteContext.Provider value={contextValue}>
      {children}
    </AppwriteContext.Provider>
  )
}
