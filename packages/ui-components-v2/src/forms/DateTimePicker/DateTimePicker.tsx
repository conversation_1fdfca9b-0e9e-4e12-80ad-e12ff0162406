"use client"

import React, { useState, useCallback } from 'react'
import { View, Pressable, Modal, Platform } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { CalendarIcon, ClockIcon, XIcon, CheckIcon } from 'lucide-react-native'
import DateTimePicker from '@react-native-community/datetimepicker'

// Types
export interface DateTimePickerProps {
  value?: Date
  onChange?: (date: Date | undefined) => void
  mode?: 'date' | 'time' | 'datetime'
  minimumDate?: Date
  maximumDate?: Date
  disabled?: boolean
  placeholder?: string
  format?: string
  className?: string
  error?: string
  label?: string
  required?: boolean
  clearable?: boolean
}

// Styles
const containerStyles = tva({
  base: "w-full",
})

const labelStyles = tva({
  base: "text-sm font-medium text-typography-700 mb-2",
  variants: {
    required: {
      true: "after:content-['*'] after:text-error-500 after:ml-1",
    },
  },
})

const inputStyles = tva({
  base: "flex-row items-center justify-between p-3 border border-outline-300 rounded-lg bg-background-0",
  variants: {
    focused: {
      true: "border-primary-500",
    },
    error: {
      true: "border-error-500",
    },
    disabled: {
      true: "bg-background-100 opacity-60",
    },
  },
})

const placeholderStyles = tva({
  base: "text-typography-500",
})

const valueStyles = tva({
  base: "text-typography-900 flex-1",
})

const iconStyles = tva({
  base: "text-typography-400 ml-2",
})

const errorStyles = tva({
  base: "text-sm text-error-600 mt-1",
})

const modalStyles = tva({
  base: "flex-1 justify-center items-center bg-black/50",
})

const modalContentStyles = tva({
  base: "bg-background-0 rounded-lg p-4 m-4 w-80 max-w-full",
})

const modalHeaderStyles = tva({
  base: "flex-row items-center justify-between mb-4",
})

const modalFooterStyles = tva({
  base: "flex-row justify-end gap-3 mt-4",
})

const buttonStyles = tva({
  base: "px-4 py-2 rounded-lg",
  variants: {
    variant: {
      primary: "bg-primary-500",
      secondary: "bg-background-100 border border-outline-300",
    },
  },
})

export function DateTimePickerComponent({
  value,
  onChange,
  mode = 'date',
  minimumDate,
  maximumDate,
  disabled = false,
  placeholder,
  format,
  className,
  error,
  label,
  required = false,
  clearable = false,
}: DateTimePickerProps) {
  const [showPicker, setShowPicker] = useState(false)
  const [tempValue, setTempValue] = useState<Date | undefined>(value)
  const [focused, setFocused] = useState(false)

  // Format date/time based on mode
  const formatValue = useCallback((date: Date): string => {
    if (format) {
      // Custom format would require a date formatting library
      return date.toLocaleDateString()
    }

    switch (mode) {
      case 'date':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })
      case 'time':
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
        })
      case 'datetime':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        })
      default:
        return date.toLocaleDateString()
    }
  }, [mode, format])

  // Get placeholder text
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder
    
    switch (mode) {
      case 'date':
        return 'Select date'
      case 'time':
        return 'Select time'
      case 'datetime':
        return 'Select date and time'
      default:
        return 'Select'
    }
  }

  // Get icon based on mode
  const getIcon = () => {
    switch (mode) {
      case 'time':
        return ClockIcon
      case 'date':
      case 'datetime':
      default:
        return CalendarIcon
    }
  }

  // Handle native picker change (iOS/Android)
  const handleNativeChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false)
    }
    
    if (selectedDate) {
      if (Platform.OS === 'ios') {
        setTempValue(selectedDate)
      } else {
        onChange?.(selectedDate)
      }
    }
  }

  // Handle modal confirm (iOS)
  const handleConfirm = () => {
    onChange?.(tempValue)
    setShowPicker(false)
  }

  // Handle modal cancel (iOS)
  const handleCancel = () => {
    setTempValue(value)
    setShowPicker(false)
  }

  // Handle clear
  const handleClear = () => {
    onChange?.(undefined)
  }

  // Open picker
  const openPicker = () => {
    if (disabled) return
    setTempValue(value || new Date())
    setShowPicker(true)
    setFocused(true)
  }

  // Close picker
  const closePicker = () => {
    setShowPicker(false)
    setFocused(false)
  }

  const IconComponent = getIcon()

  // Render native picker
  const renderNativePicker = () => {
    if (!showPicker) return null

    if (Platform.OS === 'web') {
      // Web implementation would use HTML5 date/time inputs
      return (
        <Modal visible={showPicker} transparent animationType="fade">
          <View className={modalStyles()}>
            <View className={modalContentStyles()}>
              <View className={modalHeaderStyles()}>
                <Text className="text-lg font-semibold text-typography-900">
                  {mode === 'date' ? 'Select Date' : 
                   mode === 'time' ? 'Select Time' : 
                   'Select Date & Time'}
                </Text>
                <Pressable onPress={handleCancel}>
                  <XIcon size={24} className="text-typography-500" />
                </Pressable>
              </View>

              {/* Web date/time input */}
              <input
                type={mode === 'time' ? 'time' : mode === 'datetime' ? 'datetime-local' : 'date'}
                value={tempValue ? (
                  mode === 'time' ? 
                    tempValue.toTimeString().slice(0, 5) :
                  mode === 'datetime' ?
                    tempValue.toISOString().slice(0, 16) :
                    tempValue.toISOString().slice(0, 10)
                ) : ''}
                onChange={(e) => {
                  if (e.target.value) {
                    setTempValue(new Date(e.target.value))
                  }
                }}
                min={minimumDate?.toISOString().slice(0, 10)}
                max={maximumDate?.toISOString().slice(0, 10)}
                className="w-full p-3 border border-outline-300 rounded-lg"
              />

              <View className={modalFooterStyles()}>
                <Pressable
                  className={buttonStyles({ variant: 'secondary' })}
                  onPress={handleCancel}
                >
                  <Text className="text-typography-700">Cancel</Text>
                </Pressable>
                <Pressable
                  className={buttonStyles({ variant: 'primary' })}
                  onPress={handleConfirm}
                >
                  <Text className="text-white">Confirm</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      )
    }

    // iOS Modal
    if (Platform.OS === 'ios') {
      return (
        <Modal visible={showPicker} transparent animationType="slide">
          <View className={modalStyles()}>
            <View className={modalContentStyles()}>
              <View className={modalHeaderStyles()}>
                <Text className="text-lg font-semibold text-typography-900">
                  {mode === 'date' ? 'Select Date' : 
                   mode === 'time' ? 'Select Time' : 
                   'Select Date & Time'}
                </Text>
                <Pressable onPress={handleCancel}>
                  <XIcon size={24} className="text-typography-500" />
                </Pressable>
              </View>

              <DateTimePicker
                value={tempValue || new Date()}
                mode={mode === 'datetime' ? 'date' : mode}
                display="spinner"
                onChange={handleNativeChange}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
              />

              {mode === 'datetime' && (
                <DateTimePicker
                  value={tempValue || new Date()}
                  mode="time"
                  display="spinner"
                  onChange={handleNativeChange}
                />
              )}

              <View className={modalFooterStyles()}>
                <Pressable
                  className={buttonStyles({ variant: 'secondary' })}
                  onPress={handleCancel}
                >
                  <Text className="text-typography-700">Cancel</Text>
                </Pressable>
                <Pressable
                  className={buttonStyles({ variant: 'primary' })}
                  onPress={handleConfirm}
                >
                  <Text className="text-white">Confirm</Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      )
    }

    // Android direct picker
    return (
      <DateTimePicker
        value={tempValue || new Date()}
        mode={mode === 'datetime' ? 'date' : mode}
        display="default"
        onChange={handleNativeChange}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
      />
    )
  }

  return (
    <View className={containerStyles({ class: className })}>
      {label && (
        <Text className={labelStyles({ required })}>
          {label}
        </Text>
      )}

      <Pressable
        className={inputStyles({ 
          focused, 
          error: !!error, 
          disabled 
        })}
        onPress={openPicker}
        disabled={disabled}
      >
        <Text className={value ? valueStyles() : placeholderStyles()}>
          {value ? formatValue(value) : getPlaceholder()}
        </Text>

        <View className="flex-row items-center">
          {clearable && value && (
            <Pressable onPress={handleClear} className="mr-2">
              <XIcon size={20} className="text-typography-400" />
            </Pressable>
          )}
          <IconComponent size={20} className={iconStyles()} />
        </View>
      </Pressable>

      {error && (
        <Text className={errorStyles()}>
          {error}
        </Text>
      )}

      {renderNativePicker()}
    </View>
  )
}
