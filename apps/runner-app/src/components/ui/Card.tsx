import React from 'react'
import { View, ViewStyle, StyleSheet } from 'react-native'
import { useTheme } from '../../providers/ThemeProvider'

export interface CardProps {
  children: React.ReactNode
  variant?: 'elevated' | 'outline' | 'filled' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  style?: ViewStyle
  onPress?: () => void
  testID?: string
}

export function Card({
  children,
  variant = 'elevated',
  size = 'md',
  style,
  onPress,
  testID,
}: CardProps) {
  const { theme } = useTheme()

  const getCardStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: theme.borderRadius.md,
    }

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        padding: theme.spacing.sm,
      },
      md: {
        padding: theme.spacing.md,
      },
      lg: {
        padding: theme.spacing.lg,
      },
    }

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      elevated: {
        backgroundColor: theme.colors.white,
        shadowColor: theme.colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      },
      outline: {
        backgroundColor: theme.colors.white,
        borderWidth: 1,
        borderColor: theme.colors.gray[200],
      },
      filled: {
        backgroundColor: theme.colors.gray[50],
      },
      ghost: {
        backgroundColor: theme.colors.transparent,
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...style,
    }
  }

  const Component = onPress ? 'TouchableOpacity' : View

  if (onPress) {
    const { TouchableOpacity } = require('react-native')
    return (
      <TouchableOpacity
        style={getCardStyles()}
        onPress={onPress}
        activeOpacity={0.7}
        testID={testID}
      >
        {children}
      </TouchableOpacity>
    )
  }

  return (
    <View style={getCardStyles()} testID={testID}>
      {children}
    </View>
  )
}

// Card sub-components for better composition
export interface CardHeaderProps {
  children: React.ReactNode
  style?: ViewStyle
}

export function CardHeader({ children, style }: CardHeaderProps) {
  const { theme } = useTheme()
  
  return (
    <View style={[{ marginBottom: theme.spacing.md }, style]}>
      {children}
    </View>
  )
}

export interface CardBodyProps {
  children: React.ReactNode
  style?: ViewStyle
}

export function CardBody({ children, style }: CardBodyProps) {
  return (
    <View style={[{ flex: 1 }, style]}>
      {children}
    </View>
  )
}

export interface CardFooterProps {
  children: React.ReactNode
  style?: ViewStyle
}

export function CardFooter({ children, style }: CardFooterProps) {
  const { theme } = useTheme()
  
  return (
    <View style={[{ marginTop: theme.spacing.md }, style]}>
      {children}
    </View>
  )
}
