import React from 'react'
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native'
import { useTheme } from '../hooks/useTheme'

interface LoadingSpinnerProps {
  size?: 'small' | 'large'
  color?: string
  message?: string
  overlay?: boolean
}

export function LoadingSpinner({ 
  size = 'large', 
  color, 
  message, 
  overlay = false 
}: LoadingSpinnerProps) {
  const { colors, typography } = useTheme()
  const spinnerColor = color || colors.primary[500]

  const content = (
    <View style={[styles.container, overlay && styles.overlay]}>
      <ActivityIndicator size={size} color={spinnerColor} />
      {message && (
        <Text style={[
          typography.body.medium, 
          { color: colors.text.secondary, marginTop: 12 }
        ]}>
          {message}
        </Text>
      )}
    </View>
  )

  if (overlay) {
    return (
      <View style={styles.overlayContainer}>
        {content}
      </View>
    )
  }

  return content
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  overlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1000,
  },
})
