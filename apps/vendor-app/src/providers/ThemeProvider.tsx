import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useColorScheme } from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'

import { theme } from '../theme'
import type { Theme } from '../theme/types'

interface ThemeContextType {
  theme: Theme
  isDark: boolean
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme()
  const [isDark, setIsDark] = useState(systemColorScheme === 'dark')

  useEffect(() => {
    loadThemePreference()
  }, [])

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('@hvppyplug_vendor_theme')
      if (savedTheme) {
        setIsDark(savedTheme === 'dark')
      }
    } catch (error) {
      console.error('Failed to load theme preference:', error)
    }
  }

  const toggleTheme = async () => {
    try {
      const newTheme = !isDark
      setIsDark(newTheme)
      await AsyncStorage.setItem('@hvppyplug_vendor_theme', newTheme ? 'dark' : 'light')
    } catch (error) {
      console.error('Failed to save theme preference:', error)
    }
  }

  const contextValue: ThemeContextType = {
    theme,
    isDark,
    toggleTheme,
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  
  const { theme, isDark, toggleTheme } = context
  
  // Return theme with proper dark/light mode colors
  const currentColors = isDark ? theme.colors.dark : theme.colors.light
  
  return {
    colors: {
      ...theme.colors,
      background: currentColors.background,
      text: currentColors.text,
      border: currentColors.border,
      shadow: currentColors.shadow,
    },
    typography: theme.typography,
    spacing: theme.spacing,
    borderRadius: theme.borderRadius,
    shadows: theme.shadows,
    layout: theme.layout,
    isDark,
    toggleTheme,
  }
}
