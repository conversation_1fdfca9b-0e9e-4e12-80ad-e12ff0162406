{"name": "@hvppyplug/payments", "version": "1.0.0", "description": "Unified payment processing library for HVPPYPlug+ supporting Stripe, PayFast, M-Pesa, and voucher payments", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./providers": "./src/providers/index.ts", "./hooks": "./src/hooks/index.ts", "./components": "./src/components/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts", "./security": "./src/security/index.ts", "./validation": "./src/validation/index.ts"}, "scripts": {"build": "tsc --build", "build:watch": "tsc --build --watch", "dev": "tsc --build --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hvppyplug/common": "workspace:*", "@polar-sh/sdk": "^0.34.5", "@hookform/resolvers": "^3.9.0", "react-hook-form": "^7.53.0", "react-native-keychain": "^8.2.0", "react-native-encrypted-storage": "^4.0.3", "crypto-es": "^2.1.0", "zod": "^3.23.8", "axios": "^1.7.7", "react-native-uuid": "^2.0.2", "react-native-device-info": "^13.0.0", "react-native-reanimated": "~3.10.1", "react-native-svg": "15.2.0", "lucide-react-native": "^0.447.0", "react-native-webview": "^13.12.2"}, "devDependencies": {"@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@testing-library/react-native": "^12.7.2", "@testing-library/jest-native": "^5.4.3", "jest": "^29.7.0", "typescript": "~5.8.3", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1"}, "peerDependencies": {"expo": "~53.0.0", "react": "^19.0.0", "react-native": "^0.79.0"}, "keywords": ["react-native", "expo", "payments", "polar", "payfast", "mpesa", "vouchers", "south-africa", "mobile-money", "hvppyplug", "cross-platform"], "author": "HVPPYPlug+ Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/hvppyplug/monorepo.git", "directory": "packages/payments"}, "publishConfig": {"access": "restricted"}}