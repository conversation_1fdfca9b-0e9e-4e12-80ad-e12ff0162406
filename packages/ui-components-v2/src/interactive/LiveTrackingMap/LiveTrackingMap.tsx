"use client"

import React, { useEffect, useState } from 'react'
import { View, Platform } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { MapPinIcon, TruckIcon, StoreIcon } from 'lucide-react-native'

// Types
export interface Location {
  lat: number
  lng: number
}

export interface LocationWithAddress extends Location {
  address?: string
  name?: string
}

export interface LiveTrackingMapProps {
  runnerLocation?: Location
  deliveryAddress: LocationWithAddress
  vendorLocation: LocationWithAddress
  showRoute?: boolean
  showETA?: boolean
  estimatedArrival?: string
  className?: string
  height?: number
}

// Styles
const containerStyles = tva({
  base: "bg-background-0 rounded-lg overflow-hidden border border-outline-200",
})

const mapPlaceholderStyles = tva({
  base: "bg-primary-50 items-center justify-center",
})

const locationInfoStyles = tva({
  base: "absolute bottom-0 left-0 right-0 bg-white/90 backdrop-blur-sm p-4",
})

const locationItemStyles = tva({
  base: "flex-row items-center mb-2",
})

/**
 * LiveTrackingMap component for real-time order tracking
 * 
 * Features:
 * - Real-time runner location tracking
 * - Route visualization
 * - ETA display
 * - Vendor and delivery location markers
 * - Cross-platform compatibility (web/native)
 * 
 * Note: This is a placeholder implementation. In production, you would integrate
 * with react-native-maps for mobile and Google Maps API for web.
 */
export function LiveTrackingMap({
  runnerLocation,
  deliveryAddress,
  vendorLocation,
  showRoute = true,
  showETA = true,
  estimatedArrival,
  className,
  height = 300,
}: LiveTrackingMapProps) {
  const [currentRunnerLocation, setCurrentRunnerLocation] = useState(runnerLocation)

  // Simulate real-time location updates (in production, this would come from WebSocket/real-time API)
  useEffect(() => {
    if (!runnerLocation) return

    const interval = setInterval(() => {
      // Simulate slight movement towards delivery address
      setCurrentRunnerLocation(prev => {
        if (!prev) return runnerLocation

        const deltaLat = (deliveryAddress.lat - prev.lat) * 0.01
        const deltaLng = (deliveryAddress.lng - prev.lng) * 0.01

        return {
          lat: prev.lat + deltaLat,
          lng: prev.lng + deltaLng,
        }
      })
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [runnerLocation, deliveryAddress])

  const renderMapPlaceholder = () => (
    <View 
      className={mapPlaceholderStyles()}
      style={{ height }}
    >
      <MapPinIcon size={48} className="text-primary-500 mb-4" />
      <Text className="text-primary-700 font-medium">Live Tracking Map</Text>
      <Text className="text-primary-600 text-sm text-center mt-2">
        {Platform.OS === 'web' 
          ? 'Google Maps integration would be implemented here'
          : 'react-native-maps integration would be implemented here'
        }
      </Text>
    </View>
  )

  const renderLocationInfo = () => (
    <View className={locationInfoStyles()}>
      {/* Vendor Location */}
      <View className={locationItemStyles()}>
        <StoreIcon size={16} className="text-info-500" />
        <View className="ml-2 flex-1">
          <Text className="text-sm font-medium text-typography-900">
            {vendorLocation.name || 'Pickup Location'}
          </Text>
          <Text className="text-xs text-typography-600">
            {vendorLocation.address || `${vendorLocation.lat.toFixed(4)}, ${vendorLocation.lng.toFixed(4)}`}
          </Text>
        </View>
      </View>

      {/* Runner Location */}
      {currentRunnerLocation && (
        <View className={locationItemStyles()}>
          <TruckIcon size={16} className="text-success-500" />
          <View className="ml-2 flex-1">
            <Text className="text-sm font-medium text-typography-900">
              Runner Location
            </Text>
            <Text className="text-xs text-typography-600">
              {currentRunnerLocation.lat.toFixed(4)}, {currentRunnerLocation.lng.toFixed(4)}
            </Text>
          </View>
          {showETA && estimatedArrival && (
            <Text className="text-xs text-success-600 font-medium">
              ETA: {estimatedArrival}
            </Text>
          )}
        </View>
      )}

      {/* Delivery Location */}
      <View className={locationItemStyles()}>
        <MapPinIcon size={16} className="text-error-500" />
        <View className="ml-2 flex-1">
          <Text className="text-sm font-medium text-typography-900">
            Delivery Address
          </Text>
          <Text className="text-xs text-typography-600">
            {deliveryAddress.address || `${deliveryAddress.lat.toFixed(4)}, ${deliveryAddress.lng.toFixed(4)}`}
          </Text>
        </View>
      </View>
    </View>
  )

  return (
    <View className={containerStyles({ class: className })}>
      {renderMapPlaceholder()}
      {renderLocationInfo()}
    </View>
  )
}

// Production implementation would look like this for React Native:
/*
import MapView, { Marker, Polyline } from 'react-native-maps'

export function LiveTrackingMapNative({ ... }: LiveTrackingMapProps) {
  return (
    <MapView
      style={{ height }}
      initialRegion={{
        latitude: (vendorLocation.lat + deliveryAddress.lat) / 2,
        longitude: (vendorLocation.lng + deliveryAddress.lng) / 2,
        latitudeDelta: Math.abs(vendorLocation.lat - deliveryAddress.lat) * 1.5,
        longitudeDelta: Math.abs(vendorLocation.lng - deliveryAddress.lng) * 1.5,
      }}
    >
      <Marker
        coordinate={vendorLocation}
        title="Pickup Location"
        pinColor="blue"
      />
      
      {currentRunnerLocation && (
        <Marker
          coordinate={currentRunnerLocation}
          title="Runner"
          pinColor="green"
        />
      )}
      
      <Marker
        coordinate={deliveryAddress}
        title="Delivery Address"
        pinColor="red"
      />
      
      {showRoute && currentRunnerLocation && (
        <Polyline
          coordinates={[currentRunnerLocation, deliveryAddress]}
          strokeColor="#007AFF"
          strokeWidth={3}
        />
      )}
    </MapView>
  )
}
*/

// Production implementation would look like this for Web:
/*
import { GoogleMap, Marker, Polyline, useLoadScript } from '@react-google-maps/api'

export function LiveTrackingMapWeb({ ... }: LiveTrackingMapProps) {
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
  })

  if (!isLoaded) return <div>Loading map...</div>

  return (
    <GoogleMap
      zoom={13}
      center={{
        lat: (vendorLocation.lat + deliveryAddress.lat) / 2,
        lng: (vendorLocation.lng + deliveryAddress.lng) / 2,
      }}
      mapContainerStyle={{ height: `${height}px`, width: '100%' }}
    >
      <Marker position={vendorLocation} />
      {currentRunnerLocation && <Marker position={currentRunnerLocation} />}
      <Marker position={deliveryAddress} />
      
      {showRoute && currentRunnerLocation && (
        <Polyline
          path={[currentRunnerLocation, deliveryAddress]}
          options={{ strokeColor: '#007AFF', strokeWeight: 3 }}
        />
      )}
    </GoogleMap>
  )
}
*/
