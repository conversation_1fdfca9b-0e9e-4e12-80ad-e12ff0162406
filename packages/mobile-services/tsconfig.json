{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "composite": false, "incremental": false, "jsx": "react-jsx", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"]}