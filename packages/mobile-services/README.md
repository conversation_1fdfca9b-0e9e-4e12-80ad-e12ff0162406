# @hvppyplug/mobile-services

Mobile services and React hooks for HVPPYPlug+ Expo applications.

## Features

- 📍 **Location Services** - Real-time GPS tracking with background support
- 🔔 **Notification Services** - Push and local notifications with deep linking
- 📷 **Camera Services** - Photo capture and gallery selection with compression
- 🔗 **Appwrite Integration** - Full BaaS integration for all services

## Installation

```bash
pnpm add @hvppyplug/mobile-services
```

## Usage

### Location Services

```typescript
import { useLocation } from '@hvppyplug/mobile-services'

const { currentLocation, startTracking, stopTracking } = useLocation({
  enableBackgroundLocation: true,
  enableAppwriteSync: true,
})
```

### Notification Services

```typescript
import { useNotifications } from '@hvppyplug/mobile-services'

const { sendOrderNotification, pushToken } = useNotifications({
  enableAppwriteSync: true,
})
```

### Camera Services

```typescript
import { useCamera } from '@hvppyplug/mobile-services'

const { takePhoto, uploadImage } = useCamera({
  enableAppwriteUpload: true,
})
```

## Documentation

See the main documentation for detailed usage instructions and examples.
