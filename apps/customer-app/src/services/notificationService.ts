import * as Notifications from 'expo-notifications'
import * as Device from 'expo-device'
import { Platform } from 'react-native'
import Constants from 'expo-constants'

export interface NotificationData {
  title: string
  body: string
  data?: any
  categoryId?: string
  sound?: boolean
  badge?: number
}

export interface PushNotificationToken {
  token: string
  type: 'expo' | 'fcm' | 'apns'
}

class NotificationService {
  private isInitialized = false
  private pushToken: string | null = null

  async initialize() {
    if (this.isInitialized) return

    try {
      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      })

      // Request permissions
      await this.requestPermissions()

      // Get push token
      await this.registerForPushNotifications()

      // Setup notification categories
      await this.setupNotificationCategories()

      this.isInitialized = true
      console.log('✅ Notification service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error)
    }
  }

  private async requestPermissions() {
    if (!Device.isDevice) {
      console.warn('Push notifications only work on physical devices')
      return false
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = existingStatus

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync()
      finalStatus = status
    }

    if (finalStatus !== 'granted') {
      console.warn('Push notification permissions not granted')
      return false
    }

    return true
  }

  private async registerForPushNotifications() {
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId || Constants.easConfig?.projectId

      if (!projectId) {
        console.warn('No EAS project ID found for push notifications')
        return
      }

      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId,
      })

      this.pushToken = tokenData.data
      console.log('Push token:', this.pushToken)

      // In a real app, send this token to your backend
      // await this.sendTokenToBackend(this.pushToken)

      return this.pushToken
    } catch (error) {
      console.error('Failed to get push token:', error)
    }
  }

  private async setupNotificationCategories() {
    await Notifications.setNotificationCategoryAsync('order_update', [
      {
        identifier: 'view_order',
        buttonTitle: 'View Order',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'contact_provider',
        buttonTitle: 'Contact Provider',
        options: {
          opensAppToForeground: true,
        },
      },
    ])

    await Notifications.setNotificationCategoryAsync('message', [
      {
        identifier: 'reply',
        buttonTitle: 'Reply',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'view_chat',
        buttonTitle: 'View Chat',
        options: {
          opensAppToForeground: true,
        },
      },
    ])

    await Notifications.setNotificationCategoryAsync('booking_reminder', [
      {
        identifier: 'confirm',
        buttonTitle: 'Confirm',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'reschedule',
        buttonTitle: 'Reschedule',
        options: {
          opensAppToForeground: true,
        },
      },
    ])
  }

  async scheduleLocalNotification(notification: NotificationData, delay?: number) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          categoryIdentifier: notification.categoryId,
          sound: notification.sound !== false,
          badge: notification.badge,
        },
        trigger: delay ? { seconds: delay } : null,
      })

      return notificationId
    } catch (error) {
      console.error('Failed to schedule notification:', error)
      throw error
    }
  }

  async cancelNotification(notificationId: string) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId)
    } catch (error) {
      console.error('Failed to cancel notification:', error)
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync()
    } catch (error) {
      console.error('Failed to cancel all notifications:', error)
    }
  }

  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count)
    } catch (error) {
      console.error('Failed to set badge count:', error)
    }
  }

  async clearBadge() {
    await this.setBadgeCount(0)
  }

  // Predefined notification types for common use cases
  async notifyOrderUpdate(orderId: string, status: string, providerName: string) {
    const statusMessages = {
      confirmed: `Your order has been confirmed by ${providerName}`,
      in_progress: `${providerName} has started working on your order`,
      completed: `Your order has been completed by ${providerName}`,
      cancelled: `Your order has been cancelled`,
    }

    await this.scheduleLocalNotification({
      title: 'Order Update',
      body: statusMessages[status as keyof typeof statusMessages] || `Order status: ${status}`,
      data: { orderId, status, type: 'order_update' },
      categoryId: 'order_update',
    })
  }

  async notifyNewMessage(senderName: string, message: string, chatId: string) {
    await this.scheduleLocalNotification({
      title: `Message from ${senderName}`,
      body: message,
      data: { chatId, type: 'message' },
      categoryId: 'message',
    })
  }

  async notifyBookingReminder(serviceName: string, scheduledTime: string, orderId: string) {
    const reminderTime = new Date(scheduledTime)
    reminderTime.setHours(reminderTime.getHours() - 1) // 1 hour before

    const delay = Math.max(0, reminderTime.getTime() - Date.now()) / 1000

    await this.scheduleLocalNotification({
      title: 'Upcoming Service',
      body: `${serviceName} is scheduled in 1 hour`,
      data: { orderId, type: 'booking_reminder' },
      categoryId: 'booking_reminder',
    }, delay)
  }

  async notifyProviderArrival(providerName: string, orderId: string) {
    await this.scheduleLocalNotification({
      title: 'Provider Arrived',
      body: `${providerName} has arrived at your location`,
      data: { orderId, type: 'provider_arrival' },
      categoryId: 'order_update',
    })
  }

  async notifyServiceAvailable(serviceName: string, serviceId: string) {
    await this.scheduleLocalNotification({
      title: 'Service Available',
      body: `${serviceName} is now available in your area`,
      data: { serviceId, type: 'service_available' },
    })
  }

  // Handle notification responses
  addNotificationResponseListener(callback: (response: Notifications.NotificationResponse) => void) {
    return Notifications.addNotificationResponseReceivedListener(callback)
  }

  addNotificationReceivedListener(callback: (notification: Notifications.Notification) => void) {
    return Notifications.addNotificationReceivedListener(callback)
  }

  getPushToken(): string | null {
    return this.pushToken
  }

  async getDeliveredNotifications() {
    return await Notifications.getPresentedNotificationsAsync()
  }

  async dismissNotification(notificationId: string) {
    await Notifications.dismissNotificationAsync(notificationId)
  }

  async dismissAllNotifications() {
    await Notifications.dismissAllNotificationsAsync()
  }
}

export const notificationService = new NotificationService()
