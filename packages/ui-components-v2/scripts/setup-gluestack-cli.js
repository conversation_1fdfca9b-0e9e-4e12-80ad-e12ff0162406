#!/usr/bin/env node

/**
 * Official Gluestack UI CLI Setup Script
 * This script uses the official Gluestack UI CLI to properly set up and enhance
 * our compound components library with the latest v2 components and patterns.
 */

const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const { execSync } = require('child_process')
const inquirer = require('inquirer')

// Paths
const ROOT_DIR = path.resolve(__dirname, '..')
const EXAMPLE_DIR = path.resolve(__dirname, '../../../examples-and-refs/ui-example-nativewind')

// Colors for console output
const log = {
  info: (msg) => console.log(chalk.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(chalk.green(`✓ ${msg}`)),
  warning: (msg) => console.log(chalk.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(chalk.red(`✗ ${msg}`)),
  dim: (msg) => console.log(chalk.dim(msg)),
  title: (msg) => console.log(chalk.bold.cyan(`\n🚀 ${msg}\n`)),
}

// Available Gluestack UI components to add
const GLUESTACK_COMPONENTS = [
  'box', 'text', 'heading', 'button', 'input', 'textarea', 'select',
  'checkbox', 'radio', 'switch', 'slider', 'progress', 'spinner',
  'alert', 'toast', 'modal', 'actionsheet', 'popover', 'tooltip',
  'accordion', 'avatar', 'badge', 'card', 'divider', 'image',
  'icon', 'fab', 'menu', 'table', 'form-control', 'link',
  'pressable', 'hstack', 'vstack', 'center', 'grid', 'skeleton'
]

// Enhanced components to extract from example
const ENHANCED_EXTRACTIONS = [
  {
    name: 'Responsive Breakpoint Hook',
    source: 'components/ui/utils/use-break-point-value.ts',
    target: 'src/hooks/useBreakpointValue.ts',
    description: 'Advanced responsive utilities for compound components',
  },
  {
    name: 'Enhanced Theme Configuration',
    source: 'components/ui/gluestack-ui-provider/config.ts',
    target: 'src/theme/enhanced-config.ts',
    description: 'Additional color tokens and theme enhancements',
  },
  {
    name: 'Web Optimization Styles',
    source: 'global.css',
    target: 'src/styles/web-optimizations.css',
    description: 'Web-specific CSS optimizations',
  },
  {
    name: 'Advanced Component Patterns',
    source: 'kitchensink-components',
    target: 'src/examples/composition-patterns.ts',
    description: 'Real-world component composition examples',
  },
]

// CLI Commands
async function runCommand(command, options = {}) {
  const { cwd = ROOT_DIR, silent = false } = options
  
  try {
    if (!silent) log.info(`Running: ${command}`)
    
    const result = execSync(command, {
      cwd,
      stdio: silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
    })
    
    if (!silent) log.success(`✓ Command completed successfully`)
    return result
  } catch (error) {
    log.error(`Command failed: ${command}`)
    log.error(error.message)
    throw error
  }
}

// Step 1: Initialize Gluestack UI
async function initializeGluestackUI() {
  log.title('Step 1: Initialize Gluestack UI v2')
  
  try {
    // Check if already initialized
    const configPath = path.join(ROOT_DIR, 'gluestack-ui.config.json')
    if (await fs.pathExists(configPath)) {
      log.warning('Gluestack UI already initialized. Skipping...')
      return true
    }
    
    // Run the official init command
    await runCommand('npx gluestack-ui init --path src/gluestack-components/ui')
    
    log.success('Gluestack UI initialized successfully!')
    return true
    
  } catch (error) {
    log.error('Failed to initialize Gluestack UI')
    log.info('Falling back to manual setup...')
    return false
  }
}

// Step 2: Add essential components
async function addEssentialComponents() {
  log.title('Step 2: Add Essential Gluestack UI Components')
  
  const essentialComponents = [
    'box', 'text', 'heading', 'button', 'input', 'hstack', 'vstack',
    'pressable', 'icon', 'spinner', 'alert', 'modal', 'toast'
  ]
  
  for (const component of essentialComponents) {
    try {
      log.info(`Adding ${component} component...`)
      await runCommand(`npx gluestack-ui add ${component}`, { silent: true })
      log.success(`✓ Added ${component}`)
    } catch (error) {
      log.warning(`Failed to add ${component}: ${error.message}`)
    }
  }
}

// Step 3: Extract enhanced patterns from example
async function extractEnhancedPatterns() {
  log.title('Step 3: Extract Enhanced Patterns from Example')
  
  if (!await fs.pathExists(EXAMPLE_DIR)) {
    log.warning('Example directory not found. Skipping pattern extraction.')
    return
  }
  
  for (const extraction of ENHANCED_EXTRACTIONS) {
    try {
      await extractPattern(extraction)
    } catch (error) {
      log.warning(`Failed to extract ${extraction.name}: ${error.message}`)
    }
  }
}

async function extractPattern(extraction) {
  const { name, source, target, description } = extraction
  
  log.info(`Extracting ${name}...`)
  
  const sourcePath = path.join(EXAMPLE_DIR, source)
  const targetPath = path.join(ROOT_DIR, target)
  
  if (!await fs.pathExists(sourcePath)) {
    log.warning(`Source not found: ${sourcePath}`)
    return
  }
  
  // Ensure target directory exists
  await fs.ensureDir(path.dirname(targetPath))
  
  // Read and process the source
  if (await fs.stat(sourcePath).then(s => s.isDirectory())) {
    await extractFromDirectory(sourcePath, targetPath, name)
  } else {
    await extractFromFile(sourcePath, targetPath, name)
  }
  
  log.success(`✓ Extracted ${name}`)
}

async function extractFromFile(sourcePath, targetPath, name) {
  let content = await fs.readFile(sourcePath, 'utf8')
  
  // Add header comment
  const header = `/**
 * ${name}
 * Extracted and adapted from ui-example-nativewind
 * Enhanced for @hvppyplug/compound-components
 */

`
  
  // Process content based on file type
  if (sourcePath.endsWith('.ts') || sourcePath.endsWith('.tsx')) {
    content = processTypeScriptFile(content)
  } else if (sourcePath.endsWith('.css')) {
    content = processCSSFile(content)
  }
  
  await fs.writeFile(targetPath, header + content)
}

async function extractFromDirectory(sourcePath, targetPath, name) {
  const files = await fs.readdir(sourcePath)
  const patterns = []
  
  for (const file of files) {
    const filePath = path.join(sourcePath, file)
    const stat = await fs.stat(filePath)
    
    if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.ts'))) {
      const content = await fs.readFile(filePath, 'utf8')
      patterns.push({
        file,
        content: extractComponentPattern(content),
      })
    }
  }
  
  const exportContent = `/**
 * ${name}
 * Extracted composition patterns from ui-example-nativewind
 */

export const compositionPatterns = ${JSON.stringify(patterns, null, 2)}

// Helper function to analyze patterns
export function analyzePattern(patternName: string) {
  return compositionPatterns.find(p => p.file.includes(patternName))
}
`
  
  await fs.writeFile(targetPath, exportContent)
}

function processTypeScriptFile(content) {
  return content
    // Fix import paths
    .replace(/from ['"]\.\.\/components\/ui['"]/g, 'from "@gluestack-ui/themed"')
    .replace(/from ['"]tailwind\.config['"]/g, 'from "../../tailwind.config.js"')
    // Add proper exports
    .replace(/export default/g, 'export')
}

function processCSSFile(content) {
  return content + `

/* Additional optimizations for compound components */
.compound-component {
  @apply transition-all duration-200 ease-in-out;
}

.compound-component:focus-visible {
  @apply outline-2 outline-primary-500 outline-offset-2;
}
`
}

function extractComponentPattern(content) {
  // Extract key patterns like props interfaces, styling approaches, etc.
  const patterns = {
    interfaces: extractInterfaces(content),
    styles: extractStyles(content),
    hooks: extractHooks(content),
  }
  
  return patterns
}

function extractInterfaces(content) {
  const interfaceRegex = /interface\s+(\w+)\s*{[^}]+}/g
  const matches = []
  let match
  
  while ((match = interfaceRegex.exec(content)) !== null) {
    matches.push(match[0])
  }
  
  return matches
}

function extractStyles(content) {
  const styleRegex = /const\s+\w+Styles?\s*=\s*tva\({[^}]+}\)/g
  const matches = []
  let match
  
  while ((match = styleRegex.exec(content)) !== null) {
    matches.push(match[0])
  }
  
  return matches
}

function extractHooks(content) {
  const hookRegex = /use\w+\([^)]*\)/g
  const matches = []
  let match
  
  while ((match = hookRegex.exec(content)) !== null) {
    matches.push(match[0])
  }
  
  return matches
}

// Step 4: Update package.json scripts
async function updatePackageScripts() {
  log.title('Step 4: Update Package Scripts')
  
  const packagePath = path.join(ROOT_DIR, 'package.json')
  const packageJson = await fs.readJson(packagePath)
  
  // Add CLI-based scripts
  packageJson.scripts = {
    ...packageJson.scripts,
    'gluestack:init': 'npx gluestack-ui init',
    'gluestack:add': 'npx gluestack-ui add',
    'gluestack:add-all': 'npx gluestack-ui add --all',
    'setup:enhanced': 'node scripts/setup-gluestack-cli.js',
    'extract:patterns': 'node scripts/extract-from-example.js',
  }
  
  await fs.writeJson(packagePath, packageJson, { spaces: 2 })
  log.success('Package scripts updated')
}

// Step 5: Interactive component selection
async function interactiveComponentSelection() {
  log.title('Step 5: Interactive Component Selection')
  
  const answers = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'components',
      message: 'Select additional components to add:',
      choices: GLUESTACK_COMPONENTS.map(comp => ({
        name: comp,
        value: comp,
      })),
      pageSize: 15,
    },
    {
      type: 'confirm',
      name: 'addAll',
      message: 'Add all available components?',
      default: false,
      when: (answers) => answers.components.length === 0,
    },
  ])
  
  if (answers.addAll) {
    log.info('Adding all components...')
    await runCommand('npx gluestack-ui add --all')
  } else if (answers.components.length > 0) {
    for (const component of answers.components) {
      try {
        await runCommand(`npx gluestack-ui add ${component}`, { silent: true })
        log.success(`✓ Added ${component}`)
      } catch (error) {
        log.warning(`Failed to add ${component}`)
      }
    }
  }
}

// Main execution
async function main() {
  log.title('🚀 Gluestack UI CLI Setup & Enhancement')
  log.info('Setting up compound components library with official Gluestack UI CLI')
  
  try {
    // Step 1: Initialize
    const initialized = await initializeGluestackUI()
    
    if (initialized) {
      // Step 2: Add essential components
      await addEssentialComponents()
      
      // Step 3: Extract patterns
      await extractEnhancedPatterns()
      
      // Step 4: Update scripts
      await updatePackageScripts()
      
      // Step 5: Interactive selection
      const answers = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'interactive',
          message: 'Would you like to add more components interactively?',
          default: true,
        },
      ])
      
      if (answers.interactive) {
        await interactiveComponentSelection()
      }
    }
    
    log.title('🎉 Setup Complete!')
    log.success('Your compound components library is now enhanced with Gluestack UI v2')
    log.info('Next steps:')
    log.dim('  1. Review the generated components in src/gluestack-components/ui/')
    log.dim('  2. Check extracted patterns in src/hooks/ and src/examples/')
    log.dim('  3. Run pnpm install to ensure all dependencies are installed')
    log.dim('  4. Start building your compound components!')
    log.dim('  5. Use "pnpm gluestack:add <component>" to add more components')
    
  } catch (error) {
    log.error(`Setup failed: ${error.message}`)
    log.info('Please check the manual installation guide at:')
    log.dim('https://gluestack.io/ui/docs/home/<USER>/installation')
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    log.error(`Script failed: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { main, initializeGluestackUI, addEssentialComponents }
