import React, { useState } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  UserIcon,
  BuildingIcon,
  MapPinIcon,
  PhoneIcon,
  MailIcon,
  StarIcon,
  DollarSignIcon,
  SettingsIcon,
  LogOutIcon,
  EditIcon,
  FileTextIcon,
  CreditCardIcon,
  BellIcon,
  MoonIcon,
  SunIcon,
  ChevronRightIcon,
  ShieldCheckIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import type { ProfileStackScreenProps } from '../../types/navigation'

type Props = ProfileStackScreenProps<'ProfileHome'>

export function ProfileScreen({ navigation }: Props) {
  const { colors, typography, isDark, toggleTheme } = useTheme()
  const { user, logout } = useAuthStore()
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)

  if (!user) {
    return null
  }

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout()
          }
        }
      ]
    )
  }

  const renderProfileHeader = () => (
    <View style={{
      backgroundColor: colors.background.secondary,
      padding: 24,
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      {/* Profile Avatar */}
      <View style={{
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: colors.primary[100],
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
      }}>
        <UserIcon size={40} color={colors.primary[600]} />
      </View>

      {/* Business Info */}
      <Text style={[typography.headline.medium, { color: colors.text.primary, textAlign: 'center' }]}>
        {user.businessName}
      </Text>
      <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginTop: 4 }]}>
        {user.name}
      </Text>

      {/* Verification Status */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: user.isVerified ? colors.success[100] : colors.warning[100],
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 6,
        marginTop: 12,
      }}>
        <ShieldCheckIcon
          size={16}
          color={user.isVerified ? colors.success[600] : colors.warning[600]}
        />
        <Text style={[
          typography.body.small,
          {
            color: user.isVerified ? colors.success[700] : colors.warning[700],
            marginLeft: 6,
            fontWeight: '600'
          }
        ]}>
          {user.isVerified ? 'Verified Vendor' : 'Pending Verification'}
        </Text>
      </View>

      {/* Stats Row */}
      <View style={{ flexDirection: 'row', gap: 24, marginTop: 20 }}>
        <View style={{ alignItems: 'center' }}>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            {user.rating.average.toFixed(1)}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Rating
          </Text>
        </View>

        <View style={{ alignItems: 'center' }}>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            {user.stats.completedOrders}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Orders
          </Text>
        </View>

        <View style={{ alignItems: 'center' }}>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            R{user.stats.totalEarnings.toLocaleString()}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Earned
          </Text>
        </View>
      </View>
    </View>
  )

  const renderMenuItem = (
    icon: React.ReactNode,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode,
    showChevron = true
  ) => (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 16,
        paddingHorizontal: 20,
        backgroundColor: colors.background.primary,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.primary,
      }}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 20,
        padding: 8,
        marginRight: 16,
      }}>
        {icon}
      </View>

      <View style={{ flex: 1 }}>
        <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '500' }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
            {subtitle}
          </Text>
        )}
      </View>

      {rightElement || (showChevron && (
        <ChevronRightIcon size={20} color={colors.text.tertiary} />
      ))}
    </TouchableOpacity>
  )

  const renderBusinessSection = () => (
    <View style={{ marginTop: 24 }}>
      <Text style={[
        typography.headline.small,
        { color: colors.text.primary, paddingHorizontal: 20, marginBottom: 8 }
      ]}>
        Business Information
      </Text>

      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        marginHorizontal: 16,
        overflow: 'hidden',
      }}>
        {renderMenuItem(
          <EditIcon size={20} color={colors.text.primary} />,
          'Edit Profile',
          'Update your business information',
          () => Alert.alert('Coming Soon', 'Profile editing will be available soon!')
        )}

        {renderMenuItem(
          <BuildingIcon size={20} color={colors.text.primary} />,
          'Business Details',
          `${user.businessType} • ${user.serviceAreas.join(', ')}`,
          () => Alert.alert('Coming Soon', 'Business details editing will be available soon!')
        )}

        {renderMenuItem(
          <MapPinIcon size={20} color={colors.text.primary} />,
          'Service Areas',
          `${user.serviceAreas.length} locations`,
          () => Alert.alert('Coming Soon', 'Service areas management will be available soon!')
        )}

        {renderMenuItem(
          <FileTextIcon size={20} color={colors.text.primary} />,
          'Documents',
          `${user.documents.length} uploaded`,
          () => Alert.alert('Coming Soon', 'Document management will be available soon!')
        )}
      </View>
    </View>
  )

  const renderFinancialSection = () => (
    <View style={{ marginTop: 24 }}>
      <Text style={[
        typography.headline.small,
        { color: colors.text.primary, paddingHorizontal: 20, marginBottom: 8 }
      ]}>
        Financial
      </Text>

      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        marginHorizontal: 16,
        overflow: 'hidden',
      }}>
        {renderMenuItem(
          <DollarSignIcon size={20} color={colors.success[600]} />,
          'Earnings',
          `R${user.stats.totalEarnings.toLocaleString()} total earned`,
          () => navigation.navigate('Dashboard', { screen: 'Earnings' })
        )}

        {renderMenuItem(
          <CreditCardIcon size={20} color={colors.text.primary} />,
          'Payment Methods',
          user.bankDetails ? 'Bank details added' : 'Add payment method',
          () => Alert.alert('Coming Soon', 'Payment methods management will be available soon!')
        )}
      </View>
    </View>
  )

  const renderSettingsSection = () => (
    <View style={{ marginTop: 24 }}>
      <Text style={[
        typography.headline.small,
        { color: colors.text.primary, paddingHorizontal: 20, marginBottom: 8 }
      ]}>
        Settings
      </Text>

      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        marginHorizontal: 16,
        overflow: 'hidden',
      }}>
        {renderMenuItem(
          <BellIcon size={20} color={colors.text.primary} />,
          'Notifications',
          'Manage your notification preferences',
          undefined,
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            trackColor={{ false: colors.gray[300], true: colors.primary[200] }}
            thumbColor={notificationsEnabled ? colors.primary[500] : colors.gray[500]}
          />,
          false
        )}

        {renderMenuItem(
          isDark ? <SunIcon size={20} color={colors.text.primary} /> : <MoonIcon size={20} color={colors.text.primary} />,
          'Theme',
          isDark ? 'Switch to light mode' : 'Switch to dark mode',
          toggleTheme
        )}

        {renderMenuItem(
          <SettingsIcon size={20} color={colors.text.primary} />,
          'App Settings',
          'Privacy, security, and more',
          () => Alert.alert('Coming Soon', 'App settings will be available soon!')
        )}
      </View>
    </View>
  )

  const renderLogoutSection = () => (
    <View style={{ marginTop: 24, marginBottom: 32 }}>
      <View style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        marginHorizontal: 16,
        overflow: 'hidden',
      }}>
        {renderMenuItem(
          <LogOutIcon size={20} color={colors.error[500]} />,
          'Logout',
          'Sign out of your account',
          handleLogout,
          undefined,
          false
        )}
      </View>
    </View>
  )

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderProfileHeader()}
        {renderBusinessSection()}
        {renderFinancialSection()}
        {renderSettingsSection()}
        {renderLogoutSection()}
      </ScrollView>
    </SafeAreaView>
  )
}
