import React, { useState, useEffect } from 'react'
import { View, ScrollView, RefreshControl, TouchableOpacity, Switch, Text } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  BellIcon,
  SettingsIcon,
  PlusIcon,
  TrendingUpIcon,
  DollarSignIcon,
  ClockIcon,
  StarIcon,
  UsersIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { useOrdersStore } from '../../stores/ordersStore'

import type { MainTabScreenProps } from '../../types/navigation'

type Props = MainTabScreenProps<'Dashboard'>

export function DashboardScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const {
    orders,
    pendingOrders,
    activeOrders,
    fetchOrders,
    acceptOrder,
    declineOrder,
    getOrdersStats,
    initializeRealtime,
    cleanup,
    isLoading
  } = useOrdersStore()

  const [isStoreOpen, setIsStoreOpen] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadDashboardData()

    // Initialize real-time updates if user is authenticated
    if (user?.id) {
      initializeRealtime(user.id)
    }

    // Cleanup on unmount
    return () => {
      cleanup()
    }
  }, [user?.id])

  const loadDashboardData = async () => {
    try {
      await fetchOrders()
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  const handleToggleStoreStatus = (isOpen: boolean) => {
    setIsStoreOpen(isOpen)
    // In a real app, update this in the backend
    console.log('Store status changed:', isOpen ? 'Open' : 'Closed')
  }

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await acceptOrder(orderId)
    } catch (error) {
      console.error('Failed to accept order:', error)
    }
  }

  const handleDeclineOrder = async (orderId: string) => {
    try {
      await declineOrder(orderId, 'Unable to take this order at the moment')
    } catch (error) {
      console.error('Failed to decline order:', error)
    }
  }

  const handleNavigate = (route: string, params?: any) => {
    switch (route) {
      case 'earnings':
        navigation.navigate('Earnings')
        break
      case 'orders':
        navigation.navigate('Orders')
        break
      case 'services':
        navigation.navigate('Services')
        break
      case 'profile':
        navigation.navigate('Profile')
        break
      case 'order_detail':
        navigation.navigate('Orders', { 
          screen: 'OrderDetail', 
          params: { orderId: params.orderId } 
        })
        break
      default:
        break
    }
  }

  if (!user) {
    return null
  }

  // Prepare data for the UI component
  const stats = getOrdersStats()
  const vendorStats = {
    todayOrders: stats.pending + stats.active,
    todayRevenue: stats.todayEarnings,
    avgRating: user.rating.average,
    activeMenuItems: 0, // Will be updated when services are implemented
    totalMenuItems: 0,
    ordersChange: 12, // Mock data
    revenueChange: 8.5, // Mock data
  }

  const vendor = {
    id: user.id,
    name: user.businessName,
    description: user.businessInfo.description,
    rating: user.rating.average,
    reviewCount: user.rating.count,
    deliveryTime: '30-45 min', // Mock data
    deliveryFee: 0,
    categories: ['Home Services'], // Mock data
    distance: 0,
    isOpen: isStoreOpen,
    location: {
      address: `${user.businessAddress.street}, ${user.businessAddress.city}`,
      coordinates: user.businessAddress.coordinates || { lat: -26.2041, lng: 28.0473 }
    }
  }

  const recentOrders = [...pendingOrders, ...activeOrders].slice(0, 5).map(order => ({
    id: order.id,
    customerName: order.customerName,
    items: [{ name: order.serviceName, quantity: 1, price: order.totalAmount }],
    total: order.totalAmount,
    status: order.status,
    estimatedTime: order.estimatedDuration,
    orderTime: order.createdAt,
    deliveryAddress: order.serviceAddress.street,
    paymentMethod: order.paymentMethod,
    specialInstructions: order.description,
  }))

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <View>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            Welcome back,
          </Text>
          <Text style={[typography.headline.medium, { color: colors.text.primary, fontWeight: '700' }]}>
            {user.businessName}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
          <TouchableOpacity
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 20,
              padding: 8,
            }}
          >
            <BellIcon size={20} color={colors.text.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 20,
              padding: 8,
            }}
          >
            <SettingsIcon size={20} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Store Status Toggle */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 16,
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <View>
          <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
            Store Status
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary }]}>
            {isStoreOpen ? 'Currently accepting orders' : 'Not accepting orders'}
          </Text>
        </View>
        <Switch
          value={isStoreOpen}
          onValueChange={handleToggleStoreStatus}
          trackColor={{ false: colors.gray[300], true: colors.success[200] }}
          thumbColor={isStoreOpen ? colors.success[500] : colors.gray[500]}
        />
      </View>
    </View>
  )

  const renderStatsCards = () => (
    <View style={{ padding: 16 }}>
      <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 16 }]}>
        Today's Overview
      </Text>

      <View style={{ flexDirection: 'row', gap: 12, marginBottom: 16 }}>
        <View style={{
          flex: 1,
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 16,
          alignItems: 'center',
        }}>
          <View style={{
            backgroundColor: colors.primary[100],
            borderRadius: 20,
            padding: 8,
            marginBottom: 8,
          }}>
            <ClockIcon size={20} color={colors.primary[600]} />
          </View>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            {stats.pending + stats.active}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary, textAlign: 'center' }]}>
            Active Orders
          </Text>
        </View>

        <View style={{
          flex: 1,
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 16,
          alignItems: 'center',
        }}>
          <View style={{
            backgroundColor: colors.success[100],
            borderRadius: 20,
            padding: 8,
            marginBottom: 8,
          }}>
            <DollarSignIcon size={20} color={colors.success[600]} />
          </View>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            R{stats.todayEarnings}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary, textAlign: 'center' }]}>
            Today's Earnings
          </Text>
        </View>
      </View>

      <View style={{ flexDirection: 'row', gap: 12 }}>
        <View style={{
          flex: 1,
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 16,
          alignItems: 'center',
        }}>
          <View style={{
            backgroundColor: colors.warning[100],
            borderRadius: 20,
            padding: 8,
            marginBottom: 8,
          }}>
            <StarIcon size={20} color={colors.warning[600]} />
          </View>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            {user.rating.average.toFixed(1)}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary, textAlign: 'center' }]}>
            Rating
          </Text>
        </View>

        <View style={{
          flex: 1,
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 16,
          alignItems: 'center',
        }}>
          <View style={{
            backgroundColor: colors.info[100],
            borderRadius: 20,
            padding: 8,
            marginBottom: 8,
          }}>
            <UsersIcon size={20} color={colors.info[600]} />
          </View>
          <Text style={[typography.title.large, { color: colors.text.primary, fontWeight: '700' }]}>
            {stats.completed}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary, textAlign: 'center' }]}>
            Completed
          </Text>
        </View>
      </View>
    </View>
  )

  const renderQuickActions = () => (
    <View style={{ paddingHorizontal: 16, marginBottom: 24 }}>
      <Text style={[typography.headline.small, { color: colors.text.primary, marginBottom: 16 }]}>
        Quick Actions
      </Text>

      <View style={{ flexDirection: 'row', gap: 12 }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.primary[500],
            borderRadius: 12,
            padding: 16,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPress={() => handleNavigate('services')}
        >
          <PlusIcon size={20} color={colors.white} />
          <Text style={[typography.label.medium, { color: colors.white, marginLeft: 8 }]}>
            Add Service
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.background.secondary,
            borderRadius: 12,
            padding: 16,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            borderWidth: 1,
            borderColor: colors.border.primary,
          }}
          onPress={() => handleNavigate('earnings')}
        >
          <TrendingUpIcon size={20} color={colors.text.primary} />
          <Text style={[typography.label.medium, { color: colors.text.primary, marginLeft: 8 }]}>
            View Earnings
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  const renderRecentOrders = () => (
    <View style={{ paddingHorizontal: 16, marginBottom: 24 }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
        <Text style={[typography.headline.small, { color: colors.text.primary }]}>
          Recent Orders
        </Text>
        <TouchableOpacity onPress={() => handleNavigate('orders')}>
          <Text style={[typography.body.medium, { color: colors.primary[500], fontWeight: '600' }]}>
            View All
          </Text>
        </TouchableOpacity>
      </View>

      {recentOrders.length === 0 ? (
        <View style={{
          backgroundColor: colors.background.secondary,
          borderRadius: 12,
          padding: 24,
          alignItems: 'center',
        }}>
          <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center' }]}>
            No recent orders
          </Text>
          <Text style={[typography.body.small, { color: colors.text.tertiary, textAlign: 'center', marginTop: 4 }]}>
            Orders will appear here when customers book your services
          </Text>
        </View>
      ) : (
        <View style={{ gap: 12 }}>
          {recentOrders.map((order) => (
            <TouchableOpacity
              key={order.id}
              style={{
                backgroundColor: colors.background.secondary,
                borderRadius: 12,
                padding: 16,
                borderWidth: 1,
                borderColor: colors.border.primary,
              }}
              onPress={() => handleNavigate('order_detail', { orderId: order.id })}
            >
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
                <View style={{ flex: 1 }}>
                  <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                    {order.customerName}
                  </Text>
                  <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
                    {order.items[0]?.name}
                  </Text>
                </View>
                <View style={{
                  backgroundColor: order.status === 'pending' ? colors.warning[100] :
                                 order.status === 'in_progress' ? colors.info[100] : colors.success[100],
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 6,
                }}>
                  <Text style={[
                    typography.body.small,
                    {
                      color: order.status === 'pending' ? colors.warning[700] :
                             order.status === 'in_progress' ? colors.info[700] : colors.success[700],
                      fontWeight: '600'
                    }
                  ]}>
                    {order.status.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={[typography.body.small, { color: colors.text.tertiary }]}>
                  {new Date(order.orderTime).toLocaleDateString()} • {order.deliveryAddress}
                </Text>
                <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                  R{order.total}
                </Text>
              </View>

              {order.status === 'pending' && (
                <View style={{ flexDirection: 'row', gap: 8, marginTop: 12 }}>
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      backgroundColor: colors.success[500],
                      borderRadius: 8,
                      padding: 12,
                      alignItems: 'center',
                    }}
                    onPress={() => handleAcceptOrder(order.id)}
                  >
                    <Text style={[typography.label.small, { color: colors.white }]}>
                      Accept
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      flex: 1,
                      backgroundColor: colors.background.primary,
                      borderRadius: 8,
                      padding: 12,
                      alignItems: 'center',
                      borderWidth: 1,
                      borderColor: colors.border.primary,
                    }}
                    onPress={() => handleDeclineOrder(order.id)}
                  >
                    <Text style={[typography.label.small, { color: colors.text.primary }]}>
                      Decline
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  )

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderHeader()}
        {renderStatsCards()}
        {renderQuickActions()}
        {renderRecentOrders()}
      </ScrollView>
    </SafeAreaView>
  )
}
