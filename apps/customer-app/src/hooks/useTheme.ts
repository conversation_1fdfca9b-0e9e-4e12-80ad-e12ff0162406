import { useContext, createContext } from 'react'
import { theme, type Theme, type ThemeContextType } from '../theme'

// Create Theme Context
export const ThemeContext = createContext<ThemeContextType>({
  theme,
  isDark: false,
  toggleTheme: () => {},
})

// Theme Hook with dark mode support
export const useTheme = () => {
  const context = useContext(ThemeContext)

  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }

  const { theme, isDark, toggleTheme } = context

  // Return theme with proper dark/light mode colors
  const currentColors = isDark ? theme.colors.dark : theme.colors.light

  return {
    colors: {
      ...theme.colors,
      background: currentColors.background,
      text: currentColors.text,
      border: currentColors.border,
      shadow: currentColors.shadow,
    },
    typography: theme.typography,
    spacing: theme.spacing,
    borderRadius: theme.borderRadius,
    shadows: theme.shadows,
    layout: theme.layout,
    isDark,
    toggleTheme,
  }
}

// Convenience hooks for specific theme parts
export const useColors = () => {
  const { colors } = useTheme()
  return colors
}

export const useTypography = () => {
  const { typography } = useTheme()
  return typography
}

export const useSpacing = () => {
  const { spacing } = useTheme()
  return spacing
}

export const useShadows = () => {
  const { shadows } = useTheme()
  return shadows
}

export const useLayout = () => {
  const { layout } = useTheme()
  return layout
}
