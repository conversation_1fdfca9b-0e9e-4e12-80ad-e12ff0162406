{"name": "@hvppyplug/compound-components", "version": "1.0.0", "description": "Business-focused compound components for HVPPYPlug+ built on Gluestack UI v2 primitives", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./layouts": "./src/layouts/index.ts", "./data": "./src/data/index.ts", "./forms": "./src/forms/index.ts", "./business": "./src/business/index.ts", "./interactive": "./src/interactive/index.ts", "./hooks": "./src/hooks/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts", "./provider": "./src/provider/index.ts", "./theme": "./src/theme/index.ts"}, "scripts": {"build": "node scripts/build.js", "build:watch": "node scripts/build.js --watch", "build:analyze": "node scripts/build.js --analyze", "dev": "node scripts/dev.js", "clean": "node scripts/clean.js", "lint": "node scripts/lint.js", "lint:fix": "node scripts/lint.js --fix --format", "type-check": "node scripts/lint.js --type-check", "test": "node scripts/test.js", "test:watch": "node scripts/test.js --watch", "test:coverage": "node scripts/test.js --coverage", "docs:build": "node scripts/docs-build.js", "docs:dev": "node scripts/docs-dev.js", "generate:component": "node scripts/generate-component.js", "generate:batch": "node scripts/batch-generate-components.js", "extract:example": "node scripts/extract-from-example.js", "setup:quick": "node scripts/quick-setup.js", "setup:gluestack": "npx gluestack-ui init --path src/gluestack-components/ui", "setup:enhanced": "node scripts/setup-gluestack-cli.js", "gluestack:add": "npx gluestack-ui add", "gluestack:add-all": "npx gluestack-ui add --all", "extract:patterns": "node scripts/extract-from-example.js", "health-check": "node scripts/health-check.js"}, "dependencies": {"@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/themed": "^1.1.73", "@gluestack-ui/toast": "^1.0.9", "@hookform/resolvers": "^3.9.0", "@react-native-community/datetimepicker": "^8.2.0", "lucide-react-native": "^0.447.0", "nativewind": "^4.1.23", "react-hook-form": "^7.53.0", "react-native-reanimated": "~3.10.1", "react-native-svg": "15.2.0", "tailwindcss": "^4.1.11", "zod": "^3.23.8", "@hvppyplug/mobile-services": "workspace:*"}, "devDependencies": {"@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@storybook/react-native": "^7.6.20", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.7.2", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "chalk": "^4.1.2", "chokidar": "^3.6.0", "eslint": "^8.57.0", "execa": "^5.1.1", "express": "^4.19.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "inquirer": "^8.2.6", "jest": "^29.7.0", "madge": "^7.0.0", "prettier": "^3.2.5", "rollup": "^4.13.0", "rollup-plugin-terser": "^7.0.2", "typedoc": "^0.25.12", "typescript": "~5.8.3", "yargs": "^17.7.2"}, "peerDependencies": {"expo": "~53.0.0", "react": "^19.0.0", "react-native": "^0.79.0"}, "keywords": ["react-native", "expo", "ui-components", "gluestack-ui", "nativewind", "tailwindcss", "design-system", "hvppyplug", "cross-platform", "mobile", "web", "admin-interface"], "author": "HVPPYPlug+ Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/hvppyplug/monorepo.git", "directory": "packages/ui-components-v2"}, "publishConfig": {"access": "restricted"}}