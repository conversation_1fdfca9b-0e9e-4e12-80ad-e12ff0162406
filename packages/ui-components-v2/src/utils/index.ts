/**
 * Utility functions for compound components
 */

import { Platform } from 'react-native'
import type { ColorVariant, SizeVariant } from '../types'

// Platform utilities
export const platformUtils = {
  isWeb: Platform.OS === 'web',
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
  isMobile: Platform.OS === 'ios' || Platform.OS === 'android',
  isNative: Platform.OS !== 'web',
}

// Format utilities
export const formatUtils = {
  // Format numbers with K/M suffixes
  formatNumber: (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    }
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  },

  // Format currency
  formatCurrency: (value: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(value)
  },

  // Format percentage
  formatPercentage: (value: number, decimals: number = 1): string => {
    return `${value.toFixed(decimals)}%`
  },

  // Format date
  formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toLocaleDateString('en-US', options)
  },

  // Format relative time
  formatRelativeTime: (date: Date | string): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)

    if (diffInSeconds < 60) return 'just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    
    return formatUtils.formatDate(dateObj, { month: 'short', day: 'numeric' })
  },

  // Truncate text
  truncateText: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text
    return `${text.slice(0, maxLength)}...`
  },

  // Format file size
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  },
}

// Color utilities
export const colorUtils = {
  // Get color class for variant
  getColorClass: (variant: ColorVariant, shade: number = 500): string => {
    return `${variant}-${shade}`
  },

  // Get text color for background
  getTextColorForBackground: (backgroundColor: string): 'white' | 'black' => {
    // Simple heuristic - in a real app you'd use proper color contrast calculation
    const darkColors = ['primary', 'secondary', 'error', 'success']
    return darkColors.some(color => backgroundColor.includes(color)) ? 'white' : 'black'
  },

  // Generate status color
  getStatusColor: (status: 'success' | 'warning' | 'error' | 'info' | 'neutral'): string => {
    const colorMap = {
      success: 'success-500',
      warning: 'warning-500',
      error: 'error-500',
      info: 'info-500',
      neutral: 'typography-500',
    }
    return colorMap[status]
  },
}

// Size utilities
export const sizeUtils = {
  // Get size class
  getSizeClass: (size: SizeVariant): string => {
    const sizeMap = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
    }
    return sizeMap[size]
  },

  // Get padding for size
  getPaddingForSize: (size: SizeVariant): string => {
    const paddingMap = {
      xs: 'p-1',
      sm: 'p-2',
      md: 'p-3',
      lg: 'p-4',
      xl: 'p-6',
    }
    return paddingMap[size]
  },
}

// Validation utilities
export const validationUtils = {
  // Email validation
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  // Phone validation (basic)
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
    return phoneRegex.test(phone)
  },

  // URL validation
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  // Required field validation
  isRequired: (value: any): boolean => {
    if (value === null || value === undefined) return false
    if (typeof value === 'string') return value.trim().length > 0
    if (Array.isArray(value)) return value.length > 0
    return true
  },

  // Min length validation
  hasMinLength: (value: string, minLength: number): boolean => {
    return value.length >= minLength
  },

  // Max length validation
  hasMaxLength: (value: string, maxLength: number): boolean => {
    return value.length <= maxLength
  },
}

// Array utilities
export const arrayUtils = {
  // Remove item by id
  removeById: <T extends { id: string | number }>(array: T[], id: string | number): T[] => {
    return array.filter(item => item.id !== id)
  },

  // Update item by id
  updateById: <T extends { id: string | number }>(
    array: T[], 
    id: string | number, 
    updates: Partial<T>
  ): T[] => {
    return array.map(item => 
      item.id === id ? { ...item, ...updates } : item
    )
  },

  // Move item in array
  moveItem: <T>(array: T[], fromIndex: number, toIndex: number): T[] => {
    const result = [...array]
    const [removed] = result.splice(fromIndex, 1)
    result.splice(toIndex, 0, removed)
    return result
  },

  // Group array by key
  groupBy: <T>(array: T[], key: keyof T): Record<string, T[]> => {
    return array.reduce((groups, item) => {
      const groupKey = String(item[key])
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(item)
      return groups
    }, {} as Record<string, T[]>)
  },

  // Sort array by key
  sortBy: <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
    return [...array].sort((a, b) => {
      const aValue = a[key]
      const bValue = b[key]
      
      if (aValue < bValue) return direction === 'asc' ? -1 : 1
      if (aValue > bValue) return direction === 'asc' ? 1 : -1
      return 0
    })
  },

  // Get unique values
  unique: <T>(array: T[]): T[] => {
    return [...new Set(array)]
  },

  // Chunk array
  chunk: <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  },
}

// Object utilities
export const objectUtils = {
  // Deep clone
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj))
  },

  // Pick keys
  pick: <T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
    const result = {} as Pick<T, K>
    keys.forEach(key => {
      if (key in obj) {
        result[key] = obj[key]
      }
    })
    return result
  },

  // Omit keys
  omit: <T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
    const result = { ...obj }
    keys.forEach(key => {
      delete result[key]
    })
    return result
  },

  // Check if object is empty
  isEmpty: (obj: object): boolean => {
    return Object.keys(obj).length === 0
  },
}

// Async utilities
export const asyncUtils = {
  // Delay function
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // Retry function
  retry: async <T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 1000
  ): Promise<T> => {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        if (attempt < maxAttempts) {
          await asyncUtils.delay(delayMs * attempt)
        }
      }
    }
    
    throw lastError!
  },

  // Debounce function
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void => {
    let timeout: NodeJS.Timeout
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  },

  // Throttle function
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void => {
    let inThrottle: boolean
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },
}

// Export all utilities
export {
  platformUtils as platform,
  formatUtils as format,
  colorUtils as color,
  sizeUtils as size,
  validationUtils as validation,
  arrayUtils as array,
  objectUtils as object,
  asyncUtils as async,
}
