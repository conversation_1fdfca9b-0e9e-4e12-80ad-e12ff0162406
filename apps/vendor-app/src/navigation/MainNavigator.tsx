import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { createStackNavigator } from '@react-navigation/stack'
import { 
  HomeIcon, 
  ClipboardListIcon, 
  CalendarIcon, 
  MessageSquareIcon, 
  UserIcon,
  DollarSignIcon
} from 'lucide-react-native'

import { useTheme } from '../hooks/useTheme'

// Import screens
import { DashboardScreen } from '../screens/dashboard/DashboardScreen'
import { OrdersScreen } from '../screens/orders/OrdersScreen'
import { OrderDetailScreen } from '../screens/orders/OrderDetailScreen'
import { ServicesScreen } from '../screens/services/ServicesScreen'
import { ServiceFormScreen } from '../screens/services/ServiceFormScreen'
import { CalendarScreen } from '../screens/calendar/CalendarScreen'
import { ChatScreen } from '../screens/chat/ChatScreen'
import { ChatListScreen } from '../screens/chat/ChatListScreen'
import { ProfileScreen } from '../screens/profile/ProfileScreen'
import { EarningsScreen } from '../screens/earnings/EarningsScreen'

export type MainTabParamList = {
  Dashboard: undefined
  Orders: undefined
  Services: undefined
  Calendar: undefined
  Chat: undefined
  Profile: undefined
}

export type DashboardStackParamList = {
  DashboardHome: undefined
  Earnings: undefined
}

export type OrdersStackParamList = {
  OrdersList: undefined
  OrderDetail: { orderId: string }
}

export type ServicesStackParamList = {
  ServicesList: undefined
  ServiceForm: { serviceId?: string }
}

export type ChatStackParamList = {
  ChatList: undefined
  Chat: { orderId: string; customerId: string }
}

export type ProfileStackParamList = {
  ProfileHome: undefined
  EditProfile: undefined
  BusinessInfo: undefined
  Documents: undefined
  Settings: undefined
}

const Tab = createBottomTabNavigator<MainTabParamList>()
const DashboardStack = createStackNavigator<DashboardStackParamList>()
const OrdersStack = createStackNavigator<OrdersStackParamList>()
const ServicesStack = createStackNavigator<ServicesStackParamList>()
const ChatStack = createStackNavigator<ChatStackParamList>()
const ProfileStack = createStackNavigator<ProfileStackParamList>()

function DashboardNavigator() {
  return (
    <DashboardStack.Navigator screenOptions={{ headerShown: false }}>
      <DashboardStack.Screen name="DashboardHome" component={DashboardScreen} />
      <DashboardStack.Screen name="Earnings" component={EarningsScreen} />
    </DashboardStack.Navigator>
  )
}

function OrdersNavigator() {
  return (
    <OrdersStack.Navigator screenOptions={{ headerShown: false }}>
      <OrdersStack.Screen name="OrdersList" component={OrdersScreen} />
      <OrdersStack.Screen name="OrderDetail" component={OrderDetailScreen} />
    </OrdersStack.Navigator>
  )
}

function ServicesNavigator() {
  return (
    <ServicesStack.Navigator screenOptions={{ headerShown: false }}>
      <ServicesStack.Screen name="ServicesList" component={ServicesScreen} />
      <ServicesStack.Screen name="ServiceForm" component={ServiceFormScreen} />
    </ServicesStack.Navigator>
  )
}

function ChatNavigator() {
  return (
    <ChatStack.Navigator screenOptions={{ headerShown: false }}>
      <ChatStack.Screen name="ChatList" component={ChatListScreen} />
      <ChatStack.Screen name="Chat" component={ChatScreen} />
    </ChatStack.Navigator>
  )
}

function ProfileNavigator() {
  return (
    <ProfileStack.Navigator screenOptions={{ headerShown: false }}>
      <ProfileStack.Screen name="ProfileHome" component={ProfileScreen} />
    </ProfileStack.Navigator>
  )
}

export function MainNavigator() {
  const { colors, isDark } = useTheme()

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, size }) => {
          const iconColor = focused ? colors.primary[500] : colors.text.secondary
          
          switch (route.name) {
            case 'Dashboard':
              return <HomeIcon size={size} color={iconColor} />
            case 'Orders':
              return <ClipboardListIcon size={size} color={iconColor} />
            case 'Services':
              return <DollarSignIcon size={size} color={iconColor} />
            case 'Calendar':
              return <CalendarIcon size={size} color={iconColor} />
            case 'Chat':
              return <MessageSquareIcon size={size} color={iconColor} />
            case 'Profile':
              return <UserIcon size={size} color={iconColor} />
            default:
              return <HomeIcon size={size} color={iconColor} />
          }
        },
        tabBarActiveTintColor: colors.primary[500],
        tabBarInactiveTintColor: colors.text.secondary,
        tabBarStyle: {
          backgroundColor: colors.background.primary,
          borderTopColor: colors.border.primary,
          borderTopWidth: 1,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardNavigator}
        options={{ tabBarLabel: 'Dashboard' }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersNavigator}
        options={{ tabBarLabel: 'Orders' }}
      />
      <Tab.Screen 
        name="Services" 
        component={ServicesNavigator}
        options={{ tabBarLabel: 'Services' }}
      />
      <Tab.Screen 
        name="Calendar" 
        component={CalendarScreen}
        options={{ tabBarLabel: 'Calendar' }}
      />
      <Tab.Screen 
        name="Chat" 
        component={ChatNavigator}
        options={{ tabBarLabel: 'Messages' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileNavigator}
        options={{ tabBarLabel: 'Profile' }}
      />
    </Tab.Navigator>
  )
}
