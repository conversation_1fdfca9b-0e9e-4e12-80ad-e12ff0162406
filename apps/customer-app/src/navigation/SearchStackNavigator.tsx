import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { SearchStackParamList } from '../types/navigation'

// Import Screens
import { SearchScreen } from '../screens/search/SearchScreen'
import { SearchResultsScreen } from '../screens/search/SearchResultsScreen'
import { FilterOptionsScreen } from '../screens/search/FilterOptionsScreen'

const Stack = createNativeStackNavigator<SearchStackParamList>()

export function SearchStackNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="SearchMain"
        component={SearchScreen}
      />
      <Stack.Screen
        name="SearchResults"
        component={SearchResultsScreen}
        options={{
          headerShown: true,
          title: 'Search Results',
        }}
      />
      <Stack.Screen
        name="FilterOptions"
        component={FilterOptionsScreen}
        options={{
          headerShown: true,
          title: 'Filters',
        }}
      />
    </Stack.Navigator>
  )
}
