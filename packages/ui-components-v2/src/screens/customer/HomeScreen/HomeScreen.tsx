"use client"

import React, { useState, useEffect } from 'react'
import { View, ScrollView } from 'react-native'
import { Text, Button } from '@gluestack-ui/themed'
import { SearchIcon, NotificationIcon, MapPinIcon } from 'lucide-react-native'

// Import components from the UI package
import { NavigationLayout } from '../../../layouts/NavigationLayout'
import { SearchForm } from '../../../forms/SearchForm'
import { ResponsiveGrid } from '../../../layouts/ResponsiveGrid'
import { VendorCard, type Vendor } from '../../../business/VendorCard'
import { useDataManagement } from '../../../hooks'

// Types
export interface HomeScreenProps {
  onNavigate: (route: string, params?: any) => void
  onSearch: (query: string, filters?: any) => void
  currentLocation?: {
    address: string
    coordinates: { lat: number; lng: number }
  }
  vendors: Vendor[]
  categories: string[]
  loading?: boolean
}

/**
 * Customer App Home Screen
 * 
 * Features:
 * - Location display and selection
 * - Vendor search and filtering
 * - Featured vendors grid
 * - Category filtering
 * - Navigation to vendor details
 */
export function HomeScreen({
  onNavigate,
  onSearch,
  currentLocation,
  vendors,
  categories,
  loading = false,
}: HomeScreenProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  // Use data management hook for search and filtering
  const {
    processedData: filteredVendors,
    searchState,
    setSearch,
    setFilters,
  } = useDataManagement(vendors, {
    searchKeys: ['name', 'description', 'categories'],
    initialFilters: { category: 'All' },
  })

  const handleSearch = (query: string, filters?: any) => {
    setSearch(query)
    if (filters) {
      setFilters(filters)
    }
    onSearch(query, filters)
  }

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category)
    setFilters({ category: category === 'All' ? undefined : category })
  }

  const handleVendorPress = (vendor: Vendor) => {
    onNavigate('/vendor', { vendorId: vendor.id })
  }

  const renderLocationHeader = () => (
    <View className="p-4 bg-primary-50 border-b border-outline-200">
      <View className="flex-row items-center">
        <MapPinIcon size={16} className="text-primary-600" />
        <View className="ml-2 flex-1">
          <Text className="text-sm text-typography-600">Delivering to</Text>
          <Text className="font-semibold text-typography-900">
            {currentLocation?.address || 'Select delivery location'}
          </Text>
        </View>
        <Button
          variant="outline"
          size="sm"
          onPress={() => onNavigate('/location')}
        >
          <Text>Change</Text>
        </Button>
      </View>
    </View>
  )

  const renderSearchSection = () => (
    <View className="p-4">
      <SearchForm
        placeholder="Search vendors, food, services..."
        onSearch={handleSearch}
        filters={[
          {
            key: 'category',
            type: 'select',
            label: 'Category',
            options: ['All', ...categories],
          },
          {
            key: 'distance',
            type: 'range',
            label: 'Distance (km)',
            min: 0,
            max: 10,
            step: 1,
          },
          {
            key: 'rating',
            type: 'range',
            label: 'Minimum Rating',
            min: 0,
            max: 5,
            step: 0.5,
          },
        ]}
        showFilters
      />
    </View>
  )

  const renderCategoryTabs = () => (
    <View className="px-4 pb-4">
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View className="flex-row space-x-2">
          {['All', ...categories].map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'solid' : 'outline'}
              size="sm"
              onPress={() => handleCategoryFilter(category)}
              className="mr-2"
            >
              <Text>{category}</Text>
            </Button>
          ))}
        </View>
      </ScrollView>
    </View>
  )

  const renderVendorsGrid = () => {
    if (loading) {
      return (
        <View className="p-4">
          <Text className="text-center text-typography-600">Loading vendors...</Text>
        </View>
      )
    }

    if (filteredVendors.length === 0) {
      return (
        <View className="p-4">
          <Text className="text-center text-typography-600">
            No vendors found matching your criteria
          </Text>
          <Button
            variant="outline"
            onPress={() => {
              setSearch('')
              setFilters({})
              setSelectedCategory('All')
            }}
            className="mt-4"
          >
            <Text>Clear Filters</Text>
          </Button>
        </View>
      )
    }

    return (
      <View className="p-4">
        <Text className="text-lg font-semibold text-typography-900 mb-4">
          {searchState.query ? 'Search Results' : 'Featured Vendors'}
        </Text>
        <ResponsiveGrid columns={{ xs: 1, sm: 2, lg: 3 }}>
          {filteredVendors.map((vendor) => (
            <VendorCard
              key={vendor.id}
              vendor={vendor}
              onPress={handleVendorPress}
              variant="featured"
              showDistance
              showDeliveryFee
            />
          ))}
        </ResponsiveGrid>
      </View>
    )
  }

  return (
    <NavigationLayout
      variant="tabs"
      header={{
        title: "HVPPYPlug+",
        actions: [
          {
            icon: SearchIcon,
            onPress: () => onNavigate('/search'),
            label: 'Search',
          },
          {
            icon: NotificationIcon,
            onPress: () => onNavigate('/notifications'),
            label: 'Notifications',
          },
        ],
      }}
      tabs={{
        items: [
          { key: 'home', label: 'Home', icon: 'home' },
          { key: 'orders', label: 'Orders', icon: 'list' },
          { key: 'favorites', label: 'Favorites', icon: 'heart' },
          { key: 'profile', label: 'Profile', icon: 'user' },
        ],
        activeKey: 'home',
        onTabPress: (key) => onNavigate(`/${key}`),
      }}
    >
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {renderLocationHeader()}
        {renderSearchSection()}
        {renderCategoryTabs()}
        {renderVendorsGrid()}
      </ScrollView>
    </NavigationLayout>
  )
}

// Export with default props for easier usage
export const CustomerHomeScreen = (props: HomeScreenProps) => (
  <HomeScreen {...props} />
)
