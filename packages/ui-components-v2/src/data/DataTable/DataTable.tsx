"use client"

import React, { useState, useMemo } from 'react'
import { View, ScrollView, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'
import { ChevronUpIcon, ChevronDownIcon, SearchIcon, FilterIcon } from 'lucide-react-native'

// Types
export interface DataTableColumn<T = any> {
  key: string
  title: string
  sortable?: boolean
  filterable?: boolean
  width?: number | string
  render?: (value: any, row: T, index: number) => React.ReactNode
  align?: 'left' | 'center' | 'right'
}

export interface DataTableProps<T = any> {
  data: T[]
  columns: DataTableColumn<T>[]
  loading?: boolean
  searchable?: boolean
  filterable?: boolean
  sortable?: boolean
  pagination?: boolean
  pageSize?: number
  onRowPress?: (row: T, index: number) => void
  onSort?: (column: string, direction: 'asc' | 'desc') => void
  onFilter?: (filters: Record<string, any>) => void
  onSearch?: (query: string) => void
  className?: string
  emptyMessage?: string
  loadingMessage?: string
}

// Styles
const tableStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg overflow-hidden",
  variants: {
    loading: {
      true: "opacity-60",
    },
  },
})

const headerStyles = tva({
  base: "bg-background-50 border-b border-outline-200 px-4 py-3 flex-row items-center justify-between",
})

const headerCellStyles = tva({
  base: "flex-row items-center gap-2 flex-1",
  variants: {
    sortable: {
      true: "cursor-pointer hover:bg-background-100",
    },
    align: {
      left: "justify-start",
      center: "justify-center", 
      right: "justify-end",
    },
  },
})

const rowStyles = tva({
  base: "border-b border-outline-100 px-4 py-3 flex-row items-center hover:bg-background-50",
  variants: {
    pressable: {
      true: "cursor-pointer active:bg-background-100",
    },
  },
})

const cellStyles = tva({
  base: "flex-1 pr-4",
  variants: {
    align: {
      left: "text-left",
      center: "text-center",
      right: "text-right",
    },
  },
})

export function DataTable<T = any>({
  data,
  columns,
  loading = false,
  searchable = false,
  filterable = false,
  sortable = true,
  pagination = false,
  pageSize = 10,
  onRowPress,
  onSort,
  onFilter,
  onSearch,
  className,
  emptyMessage = "No data available",
  loadingMessage = "Loading...",
}: DataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  // Handle sorting
  const handleSort = (columnKey: string) => {
    if (!sortable) return
    
    const newDirection = sortColumn === columnKey && sortDirection === 'asc' ? 'desc' : 'asc'
    setSortColumn(columnKey)
    setSortDirection(newDirection)
    onSort?.(columnKey, newDirection)
  }

  // Filter and sort data
  const processedData = useMemo(() => {
    let result = [...data]

    // Search filtering
    if (searchQuery && searchable) {
      result = result.filter(row =>
        columns.some(column => {
          const value = row[column.key]
          return value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        })
      )
    }

    // Sorting
    if (sortColumn && sortable) {
      result.sort((a, b) => {
        const aValue = a[sortColumn]
        const bValue = b[sortColumn]
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
        return 0
      })
    }

    // Pagination
    if (pagination) {
      const startIndex = (currentPage - 1) * pageSize
      result = result.slice(startIndex, startIndex + pageSize)
    }

    return result
  }, [data, searchQuery, sortColumn, sortDirection, currentPage, pageSize, columns, searchable, sortable, pagination])

  // Render search bar
  const renderSearchBar = () => {
    if (!searchable) return null

    return (
      <View className="p-4 border-b border-outline-200">
        <View className="flex-row items-center bg-background-50 rounded-lg px-3 py-2">
          <SearchIcon size={20} className="text-typography-400 mr-2" />
          <Text
            className="flex-1 text-typography-900"
            placeholder="Search..."
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text)
              onSearch?.(text)
            }}
          />
        </View>
      </View>
    )
  }

  // Render table header
  const renderHeader = () => (
    <View className={headerStyles()}>
      {columns.map((column) => (
        <Pressable
          key={column.key}
          className={headerCellStyles({ 
            sortable: sortable && column.sortable !== false,
            align: column.align || 'left'
          })}
          onPress={() => sortable && column.sortable !== false && handleSort(column.key)}
          style={{ width: column.width }}
        >
          <Text className="font-semibold text-typography-700 text-sm">
            {column.title}
          </Text>
          {sortable && column.sortable !== false && sortColumn === column.key && (
            sortDirection === 'asc' ? 
              <ChevronUpIcon size={16} className="text-typography-500" /> :
              <ChevronDownIcon size={16} className="text-typography-500" />
          )}
        </Pressable>
      ))}
    </View>
  )

  // Render table row
  const renderRow = (row: T, index: number) => (
    <Pressable
      key={index}
      className={rowStyles({ pressable: !!onRowPress })}
      onPress={() => onRowPress?.(row, index)}
    >
      {columns.map((column) => (
        <View
          key={column.key}
          className={cellStyles({ align: column.align || 'left' })}
          style={{ width: column.width }}
        >
          {column.render ? (
            column.render(row[column.key], row, index)
          ) : (
            <Text className="text-typography-900 text-sm">
              {row[column.key]?.toString() || '-'}
            </Text>
          )}
        </View>
      ))}
    </Pressable>
  )

  // Render empty state
  const renderEmptyState = () => (
    <View className="p-8 items-center justify-center">
      <Text className="text-typography-500 text-center">
        {loading ? loadingMessage : emptyMessage}
      </Text>
    </View>
  )

  return (
    <View className={tableStyles({ loading, class: className })}>
      {renderSearchBar()}
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={{ minWidth: '100%' }}>
          {renderHeader()}
          
          <ScrollView style={{ maxHeight: 400 }}>
            {processedData.length > 0 ? (
              processedData.map((row, index) => renderRow(row, index))
            ) : (
              renderEmptyState()
            )}
          </ScrollView>
        </View>
      </ScrollView>
      
      {pagination && (
        <View className="p-4 border-t border-outline-200 flex-row justify-between items-center">
          <Text className="text-sm text-typography-600">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, data.length)} of {data.length}
          </Text>
          
          <View className="flex-row gap-2">
            <Pressable
              className="px-3 py-1 bg-background-100 rounded disabled:opacity-50"
              onPress={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <Text className="text-sm">Previous</Text>
            </Pressable>
            
            <Pressable
              className="px-3 py-1 bg-background-100 rounded disabled:opacity-50"
              onPress={() => setCurrentPage(prev => prev + 1)}
              disabled={currentPage * pageSize >= data.length}
            >
              <Text className="text-sm">Next</Text>
            </Pressable>
          </View>
        </View>
      )}
    </View>
  )
}
