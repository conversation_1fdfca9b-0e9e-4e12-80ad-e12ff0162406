import { colors, type ColorPalette } from './colors'
import { typography, type Typography } from './typography'
import { spacing, borderRadius, shadows, layout, type Spacing, type BorderRadius, type Shadows, type Layout } from './spacing'

// Main Theme Interface
export interface Theme {
  colors: ColorPalette
  typography: Typography
  spacing: Spacing
  borderRadius: BorderRadius
  shadows: Shadows
  layout: Layout
}

// Default Theme
export const theme: Theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
}

// Theme Context Type
export interface ThemeContextType {
  theme: Theme
  isDark: boolean
  toggleTheme: () => void
}

// Export individual theme parts
export { colors } from './colors'
export { typography } from './typography'
export { spacing, borderRadius, shadows, layout } from './spacing'

// Export types
export type { ColorPalette, Typography, Spacing, BorderRadius, Shadows, Layout }

// Default export
export default theme
