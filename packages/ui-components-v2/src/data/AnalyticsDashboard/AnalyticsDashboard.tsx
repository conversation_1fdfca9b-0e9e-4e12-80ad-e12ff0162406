"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { View, ScrollView, Pressable, Dimensions } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button } from '@gluestack-ui/themed'
import {
  TrendingUpIcon,
  TrendingDownIcon,
  BarChart3Icon,
  PieChartIcon,
  LineChartIcon,
  RefreshCwIcon,
  DownloadIcon,
  FilterIcon,
  CalendarIcon,
  UsersIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  EyeIcon
} from 'lucide-react-native'
import { useBreakpointValue } from '@/hooks/useBreakpointValue'

// Types
export interface MetricData {
  id: string
  title: string
  value: string | number
  change: number
  changeType: 'increase' | 'decrease' | 'neutral'
  icon: React.ComponentType<any>
  color: 'primary' | 'success' | 'warning' | 'error' | 'info'
  trend?: number[]
}

export interface ChartData {
  id: string
  title: string
  type: 'line' | 'bar' | 'pie' | 'area'
  data: any[]
  color: string
}

export interface AnalyticsDashboardProps {
  metrics?: MetricData[]
  charts?: ChartData[]
  timeRange?: '24h' | '7d' | '30d' | '90d' | '1y'
  onTimeRangeChange?: (range: string) => void
  onRefresh?: () => void
  onExport?: () => void
  loading?: boolean
  className?: string
  variant?: 'default' | 'compact' | 'detailed'
}

// Default metrics data
const defaultMetrics: MetricData[] = [
  {
    id: 'revenue',
    title: 'Total Revenue',
    value: '$45,231',
    change: 12.5,
    changeType: 'increase',
    icon: DollarSignIcon,
    color: 'success',
    trend: [20, 25, 30, 28, 35, 40, 45]
  },
  {
    id: 'users',
    title: 'Active Users',
    value: '2,345',
    change: -2.3,
    changeType: 'decrease',
    icon: UsersIcon,
    color: 'primary',
    trend: [100, 120, 115, 130, 125, 140, 135]
  },
  {
    id: 'orders',
    title: 'Orders',
    value: '1,234',
    change: 8.7,
    changeType: 'increase',
    icon: ShoppingCartIcon,
    color: 'info',
    trend: [50, 55, 60, 58, 65, 70, 75]
  },
  {
    id: 'views',
    title: 'Page Views',
    value: '98.5K',
    change: 0,
    changeType: 'neutral',
    icon: EyeIcon,
    color: 'warning',
    trend: [80, 85, 82, 88, 85, 90, 87]
  }
]

// Styles
const containerStyles = tva({
  base: "flex-1 bg-background-0",
  variants: {
    variant: {
      default: "p-4",
      compact: "p-2",
      detailed: "p-6",
    },
  },
})

const headerStyles = tva({
  base: "flex-row items-center justify-between mb-6",
  variants: {
    variant: {
      compact: "mb-4",
      detailed: "mb-8",
    },
  },
})

const metricsGridStyles = tva({
  base: "flex-row flex-wrap -mx-2 mb-6",
  variants: {
    variant: {
      compact: "mb-4",
      detailed: "mb-8",
    },
  },
})

const metricCardStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg p-4 mx-2 mb-4 min-h-32",
  variants: {
    variant: {
      compact: "p-3 min-h-24",
      detailed: "p-6 min-h-36",
    },
    color: {
      primary: "border-l-4 border-l-primary-500",
      success: "border-l-4 border-l-success-500",
      warning: "border-l-4 border-l-warning-500",
      error: "border-l-4 border-l-error-500",
      info: "border-l-4 border-l-info-500",
    },
  },
})

const chartContainerStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg p-4 mb-4",
  variants: {
    variant: {
      compact: "p-3",
      detailed: "p-6",
    },
  },
})

const actionButtonStyles = tva({
  base: "flex-row items-center px-3 py-2 rounded-lg border border-outline-200",
  variants: {
    variant: {
      primary: "bg-primary-500 border-primary-500",
      secondary: "bg-background-50",
    },
  },
})

export function AnalyticsDashboard({
  metrics = defaultMetrics,
  charts = [],
  timeRange = '30d',
  onTimeRangeChange,
  onRefresh,
  onExport,
  loading = false,
  className,
  variant = 'default',
}: AnalyticsDashboardProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange)
  const [refreshing, setRefreshing] = useState(false)

  // Responsive grid columns
  const gridColumns = useBreakpointValue({
    xs: 1,
    sm: 2,
    md: 2,
    lg: 4,
    xl: 4,
  })

  const chartColumns = useBreakpointValue({
    xs: 1,
    sm: 1,
    md: 2,
    lg: 2,
    xl: 3,
  })

  // Calculate metric card width based on grid columns
  const metricCardWidth = useMemo(() => {
    const screenWidth = Dimensions.get('window').width
    const padding = variant === 'compact' ? 16 : variant === 'detailed' ? 48 : 32
    const margins = 16 * gridColumns
    return (screenWidth - padding - margins) / gridColumns
  }, [gridColumns, variant])

  // Handle time range change
  const handleTimeRangeChange = (range: string) => {
    setSelectedTimeRange(range)
    onTimeRangeChange?.(range)
  }

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await onRefresh?.()
    } finally {
      setRefreshing(false)
    }
  }

  // Render metric card
  const renderMetricCard = (metric: MetricData) => {
    const IconComponent = metric.icon
    const isPositive = metric.changeType === 'increase'
    const isNegative = metric.changeType === 'decrease'
    const TrendIcon = isPositive ? TrendingUpIcon : isNegative ? TrendingDownIcon : null

    return (
      <View
        key={metric.id}
        className={metricCardStyles({ variant, color: metric.color })}
        style={{ width: metricCardWidth }}
      >
        <View className="flex-row items-center justify-between mb-2">
          <View className={`p-2 rounded-lg bg-${metric.color}-100`}>
            <IconComponent size={variant === 'compact' ? 16 : 20} className={`text-${metric.color}-600`} />
          </View>
          {TrendIcon && (
            <View className="flex-row items-center">
              <TrendIcon
                size={14}
                className={isPositive ? 'text-success-600' : 'text-error-600'}
              />
              <Text className={`text-xs ml-1 ${isPositive ? 'text-success-600' : 'text-error-600'}`}>
                {Math.abs(metric.change)}%
              </Text>
            </View>
          )}
        </View>

        <Text className="text-xs text-typography-600 mb-1">
          {metric.title}
        </Text>

        <Text className={`font-bold text-typography-900 ${
          variant === 'compact' ? 'text-lg' : variant === 'detailed' ? 'text-2xl' : 'text-xl'
        }`}>
          {metric.value}
        </Text>

        {metric.trend && variant !== 'compact' && (
          <View className="mt-2">
            {/* Simple trend visualization - in real implementation, use a chart library */}
            <View className="flex-row items-end h-8">
              {metric.trend.map((value, index) => (
                <View
                  key={index}
                  className={`bg-${metric.color}-200 rounded-t mx-0.5 flex-1`}
                  style={{ height: (value / Math.max(...metric.trend)) * 32 }}
                />
              ))}
            </View>
          </View>
        )}
      </View>
    )
  }

  // Render chart placeholder
  const renderChart = (chart: ChartData) => {
    const ChartIcon = chart.type === 'line' ? LineChartIcon :
                     chart.type === 'bar' ? BarChart3Icon :
                     chart.type === 'pie' ? PieChartIcon : LineChartIcon

    return (
      <View
        key={chart.id}
        className={chartContainerStyles({ variant })}
        style={{ width: `${100 / chartColumns}%` }}
      >
        <View className="flex-row items-center justify-between mb-4">
          <Text className="font-medium text-typography-900">
            {chart.title}
          </Text>
          <ChartIcon size={20} className="text-typography-500" />
        </View>

        {/* Chart placeholder - in real implementation, integrate with chart library */}
        <View className="h-48 bg-background-50 rounded-lg items-center justify-center">
          <ChartIcon size={48} className="text-typography-300 mb-2" />
          <Text className="text-sm text-typography-500">
            {chart.type.charAt(0).toUpperCase() + chart.type.slice(1)} Chart
          </Text>
          <Text className="text-xs text-typography-400 mt-1">
            {chart.data.length} data points
          </Text>
        </View>
      </View>
    )
  }

  // Time range options
  const timeRangeOptions = [
    { label: '24H', value: '24h' },
    { label: '7D', value: '7d' },
    { label: '30D', value: '30d' },
    { label: '90D', value: '90d' },
    { label: '1Y', value: '1y' },
  ]

  return (
    <ScrollView className={containerStyles({ variant, class: className })}>
      {/* Header */}
      <View className={headerStyles({ variant })}>
        <View>
          <Text className={`font-bold text-typography-900 ${
            variant === 'compact' ? 'text-lg' : variant === 'detailed' ? 'text-2xl' : 'text-xl'
          }`}>
            Analytics Dashboard
          </Text>
          <Text className="text-sm text-typography-600 mt-1">
            {selectedTimeRange.toUpperCase()} Overview
          </Text>
        </View>

        <View className="flex-row items-center gap-2">
          {/* Time Range Selector */}
          <View className="flex-row bg-background-50 rounded-lg p-1">
            {timeRangeOptions.map((option) => (
              <Pressable
                key={option.value}
                onPress={() => handleTimeRangeChange(option.value)}
                className={`px-3 py-1 rounded ${
                  selectedTimeRange === option.value
                    ? 'bg-primary-500'
                    : 'bg-transparent'
                }`}
              >
                <Text className={`text-xs ${
                  selectedTimeRange === option.value
                    ? 'text-white font-medium'
                    : 'text-typography-600'
                }`}>
                  {option.label}
                </Text>
              </Pressable>
            ))}
          </View>

          {/* Action Buttons */}
          <Pressable
            onPress={handleRefresh}
            className={actionButtonStyles({ variant: 'secondary' })}
            disabled={refreshing}
          >
            <RefreshCwIcon
              size={16}
              className={`text-typography-600 ${refreshing ? 'animate-spin' : ''}`}
            />
          </Pressable>

          {onExport && (
            <Pressable
              onPress={onExport}
              className={actionButtonStyles({ variant: 'secondary' })}
            >
              <DownloadIcon size={16} className="text-typography-600" />
            </Pressable>
          )}
        </View>
      </View>

      {/* Metrics Grid */}
      <View className={metricsGridStyles({ variant })}>
        {metrics.map(renderMetricCard)}
      </View>

      {/* Charts Section */}
      {charts.length > 0 && (
        <View>
          <Text className="text-lg font-medium text-typography-900 mb-4">
            Charts & Insights
          </Text>
          <View className="flex-row flex-wrap -mx-2">
            {charts.map(renderChart)}
          </View>
        </View>
      )}

      {/* Loading State */}
      {loading && (
        <View className="absolute inset-0 bg-background-0/80 items-center justify-center">
          <View className="bg-background-0 rounded-lg p-6 shadow-lg">
            <RefreshCwIcon size={24} className="text-primary-500 animate-spin mb-2" />
            <Text className="text-sm text-typography-600">Loading analytics...</Text>
          </View>
        </View>
      )}
    </ScrollView>
  )
}
