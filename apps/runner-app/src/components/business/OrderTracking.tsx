import React from 'react'
import { View, Text, StyleSheet } from 'react-native'
import { 
  CheckCircle, 
  Clock, 
  Package, 
  Truck, 
  MapPin,
  Circle
} from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import type { Order, OrderStatus } from '../../types'

interface OrderTrackingProps {
  order: Order
  compact?: boolean
}

interface TrackingStep {
  key: OrderStatus
  label: string
  icon: React.ReactNode
  time?: string
  description?: string
}

export function OrderTracking({ order, compact = false }: OrderTrackingProps) {
  const { theme } = useTheme()

  const getTrackingSteps = (): TrackingStep[] => {
    const iconSize = compact ? 16 : 20
    
    return [
      {
        key: 'pending',
        label: 'Order Placed',
        icon: <Clock size={iconSize} />,
        time: order.createdAt,
        description: 'Order received and waiting for runner acceptance'
      },
      {
        key: 'accepted',
        label: 'Order Accepted',
        icon: <CheckCircle size={iconSize} />,
        description: 'Runner has accepted your order'
      },
      {
        key: 'picked_up',
        label: 'Order Picked Up',
        icon: <Package size={iconSize} />,
        description: 'Order collected from vendor'
      },
      {
        key: 'en_route',
        label: 'Out for Delivery',
        icon: <Truck size={iconSize} />,
        description: 'Runner is on the way to delivery address'
      },
      {
        key: 'delivered',
        label: 'Delivered',
        icon: <MapPin size={iconSize} />,
        time: order.actualDeliveryTime,
        description: 'Order successfully delivered'
      }
    ]
  }

  const getCurrentStepIndex = () => {
    const steps = getTrackingSteps()
    return steps.findIndex(step => step.key === order.status)
  }

  const isStepCompleted = (stepIndex: number) => {
    return stepIndex <= getCurrentStepIndex()
  }

  const isStepActive = (stepIndex: number) => {
    return stepIndex === getCurrentStepIndex()
  }

  const formatTime = (timeString?: string) => {
    if (!timeString) return ''
    
    try {
      const date = new Date(timeString)
      return date.toLocaleTimeString('en-ZA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    } catch {
      return ''
    }
  }

  const steps = getTrackingSteps()
  const currentStepIndex = getCurrentStepIndex()

  return (
    <View style={styles.container}>
      {steps.map((step, index) => {
        const isCompleted = isStepCompleted(index)
        const isActive = isStepActive(index)
        const isLast = index === steps.length - 1

        return (
          <View key={step.key} style={styles.stepContainer}>
            {/* Step Indicator */}
            <View style={styles.stepIndicator}>
              <View style={[
                styles.stepIcon,
                {
                  backgroundColor: isCompleted 
                    ? theme.colors.success[600] 
                    : isActive 
                      ? theme.colors.primary[600]
                      : theme.colors.gray[300],
                  borderColor: isCompleted 
                    ? theme.colors.success[600] 
                    : isActive 
                      ? theme.colors.primary[600]
                      : theme.colors.gray[300],
                }
              ]}>
                {React.cloneElement(step.icon as React.ReactElement, {
                  color: isCompleted || isActive ? theme.colors.white : theme.colors.gray[500]
                })}
              </View>

              {/* Connecting Line */}
              {!isLast && (
                <View style={[
                  styles.connectingLine,
                  {
                    backgroundColor: isCompleted 
                      ? theme.colors.success[600] 
                      : theme.colors.gray[300]
                  }
                ]} />
              )}
            </View>

            {/* Step Content */}
            <View style={[styles.stepContent, { marginBottom: isLast ? 0 : theme.spacing.lg }]}>
              <View style={styles.stepHeader}>
                <Text style={[
                  styles.stepLabel,
                  {
                    color: isCompleted || isActive 
                      ? theme.colors.gray[900] 
                      : theme.colors.gray[500],
                    fontWeight: isActive ? '600' : '500'
                  }
                ]}>
                  {step.label}
                </Text>
                
                {step.time && (
                  <Text style={[
                    styles.stepTime,
                    { color: theme.colors.gray[600] }
                  ]}>
                    {formatTime(step.time)}
                  </Text>
                )}
              </View>

              {!compact && step.description && (isCompleted || isActive) && (
                <Text style={[
                  styles.stepDescription,
                  { color: theme.colors.gray[600] }
                ]}>
                  {step.description}
                </Text>
              )}
            </View>
          </View>
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepIndicator: {
    alignItems: 'center',
    marginRight: 16,
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  connectingLine: {
    width: 2,
    height: 32,
    marginTop: 4,
  },
  stepContent: {
    flex: 1,
    paddingTop: 8,
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  stepLabel: {
    fontSize: 16,
    flex: 1,
  },
  stepTime: {
    fontSize: 14,
    marginLeft: 8,
  },
  stepDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
})
