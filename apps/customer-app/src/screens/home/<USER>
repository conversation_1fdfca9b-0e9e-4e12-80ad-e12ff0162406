import React, { useState, useEffect } from 'react'
import { View, ScrollView, RefreshControl, Alert, Text, TouchableOpacity, TextInput, Pressable } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useLocation } from '@hvppyplug/mobile-services'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { useServicesStore } from '../../stores/servicesStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import { SkeletonList } from '../../components/SkeletonLoader'

// Import UI components from the library
// import { VendorCard, type Vendor } from '@hvppyplug/ui-components-v2'
// import { StatusBadge } from '@hvppyplug/ui-components-v2'
// import { SearchForm } from '@hvppyplug/ui-components-v2'

// Temporary placeholder components and types
type Vendor = {
  id: string
  name: string
  category: string
  rating: number
  distance: string
  image?: string
  isOpen: boolean
}

const VendorCard = ({ vendor, onPress }: { vendor: Vendor; onPress: () => void }) => {
  return (
    <Pressable onPress={onPress} className="bg-white p-4 rounded-lg shadow-sm mb-3">
      <Text className="font-semibold text-lg">{vendor.name}</Text>
      <Text className="text-gray-600">{vendor.category}</Text>
      <Text className="text-sm text-gray-500">Rating: {vendor.rating} • {vendor.distance}</Text>
    </Pressable>
  )
}

const StatusBadge = ({ status, variant }: any) => {
  return (
    <View className="px-3 py-1 bg-green-100 rounded-full">
      <Text className="text-green-800 text-sm font-medium">{status}</Text>
    </View>
  )
}

const SearchForm = ({ onSearch, placeholder }: any) => {
  return (
    <View className="p-4 bg-white">
      <TextInput
        placeholder={placeholder || "Search..."}
        className="border border-gray-300 rounded-lg px-4 py-2"
        onChangeText={onSearch}
      />
    </View>
  )
}

import type { HomeStackScreenProps } from '../../types/navigation'

type Props = HomeStackScreenProps<'HomeMain'>

export function HomeScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()
  const {
    services,
    categories,
    featuredServices,
    nearbyServices,
    isLoading,
    error,
    getServices,
    getCategories,
    getFeaturedServices,
    getNearbyServices,
    searchServices,
    clearError
  } = useServicesStore()

  const [refreshing, setRefreshing] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  // Location hook for getting user's current location
  const {
    currentLocation,
    getCurrentLocation,
    isLoading: locationLoading,
  } = useLocation({
    enableBackgroundLocation: false,
    enableAppwriteSync: true,
  })

  useEffect(() => {
    initializeData()
  }, [])

  useEffect(() => {
    if (currentLocation) {
      // Get nearby services when location is available
      getNearbyServices(
        {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
        },
        10 // 10km radius
      )
    }
  }, [currentLocation])

  const initializeData = async () => {
    try {
      clearError()

      // Get current location
      await getCurrentLocation()

      // Load initial data
      await Promise.all([
        getCategories(),
        getFeaturedServices(),
        getServices({ limit: 20 })
      ])
    } catch (err: any) {
      console.error('Failed to initialize home screen data:', err)
      Alert.alert('Error', 'Failed to load data. Please try again.')
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await initializeData()
    setRefreshing(false)
  }

  const handleSearch = (query: string, filters?: any) => {
    searchServices(query, filters)
  }

  const handleNavigate = (route: string, params?: any) => {
    switch (route) {
      case '/search':
        navigation.navigate('Search')
        break
      case '/notifications':
        navigation.navigate('Notifications')
        break
      case '/location':
        navigation.navigate('LocationPicker', {
          currentLocation: currentLocation ? {
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            address: currentLocation.address || 'Current Location'
          } : undefined
        })
        break
      case '/vendor':
        if (params?.vendorId) {
          navigation.navigate('ProviderProfile', { providerId: params.vendorId })
        }
        break
      default:
        console.warn('Unknown route:', route)
    }
  }

  // Convert services to vendor format for the compound component
  const convertServicesToVendors = (services: any[]) => {
    return services.map(service => ({
      id: service.id,
      name: service.provider.name,
      description: service.description,
      imageUrl: service.images?.[0],
      rating: service.rating.average,
      reviewCount: service.rating.count,
      deliveryTime: `${service.duration} min`,
      deliveryFee: service.price.amount,
      categories: [service.category.name],
      distance: 2.5, // TODO: Calculate actual distance
      isOpen: service.isActive,
      location: {
        address: service.location.address?.street || 'Location not specified',
        coordinates: {
          lat: service.location.address?.coordinates.latitude || 0,
          lng: service.location.address?.coordinates.longitude || 0,
        }
      }
    }))
  }

  const vendors = convertServicesToVendors(nearbyServices.length > 0 ? nearbyServices : featuredServices)
  const categoryNames = categories.map(cat => cat.name)

  // Show loading state
  if (isLoading && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <SkeletonList count={5} style={{ padding: 16 }} />
      </SafeAreaView>
    )
  }

  // Show error state
  if (error && !refreshing) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24 }}>
          <Text style={[typography.body.large, { color: colors.text.primary, textAlign: 'center', marginBottom: 16 }]}>
            Something went wrong
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center', marginBottom: 24 }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary[500],
              paddingHorizontal: 24,
              paddingVertical: 12,
              borderRadius: 8,
            }}
            onPress={initializeData}
          >
            <Text style={[typography.label.medium, { color: colors.white }]}>
              Try Again
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Header */}
        <View style={{ padding: 16, backgroundColor: colors.background.secondary }}>
          <Text style={[typography.display.small, { color: colors.text.primary }]}>
            Welcome back, {user?.name || 'Guest'}!
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.secondary, marginTop: 4 }]}>
            What service do you need today?
          </Text>
        </View>

        {/* Location Header */}
        <View style={{
          padding: 16,
          backgroundColor: colors.primary[50],
          borderBottomWidth: 1,
          borderBottomColor: colors.border.primary
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flex: 1 }}>
              <Text style={[typography.body.small, { color: colors.text.secondary }]}>
                Delivering to
              </Text>
              <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
                {currentLocation?.address || 'Select delivery location'}
              </Text>
            </View>
            <TouchableOpacity
              style={{
                backgroundColor: colors.primary[500],
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 6,
              }}
              onPress={() => handleNavigate('/location')}
            >
              <Text style={[typography.label.small, { color: colors.white }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Categories */}
        <View style={{ padding: 16 }}>
          <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }]}>
            Categories
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              {['All', ...categoryNames].map((category) => (
                <TouchableOpacity
                  key={category}
                  style={{
                    backgroundColor: selectedCategory === category ? colors.primary[500] : colors.background.tertiary,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 20,
                    borderWidth: 1,
                    borderColor: selectedCategory === category ? colors.primary[500] : colors.border.primary,
                  }}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[
                    typography.label.small,
                    {
                      color: selectedCategory === category ? colors.white : colors.text.primary,
                      fontWeight: selectedCategory === category ? '600' : '400'
                    }
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Featured Services */}
        <View style={{ padding: 16 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 }}>
            <Text style={[typography.body.large, { color: colors.text.primary, fontWeight: '600' }]}>
              {nearbyServices.length > 0 ? 'Nearby Services' : 'Featured Services'}
            </Text>
            <StatusBadge
              status={nearbyServices.length > 0 ? 'success' : 'info'}
              text={`${vendors.length} available`}
              variant="subtle"
            />
          </View>

          {vendors.length === 0 ? (
            <View style={{
              backgroundColor: colors.background.tertiary,
              padding: 24,
              borderRadius: 12,
              alignItems: 'center'
            }}>
              <Text style={[typography.body.medium, { color: colors.text.secondary, textAlign: 'center' }]}>
                No services available in your area yet.
              </Text>
              <Text style={[typography.body.small, { color: colors.text.tertiary, textAlign: 'center', marginTop: 4 }]}>
                We're working to bring more services to your location.
              </Text>
            </View>
          ) : (
            <View style={{ gap: 12 }}>
              {vendors.slice(0, 5).map((vendor) => (
                <VendorCard
                  key={vendor.id}
                  vendor={vendor}
                  onPress={(vendor) => handleNavigate('/vendor', { vendorId: vendor.id })}
                  variant="default"
                  showDistance={true}
                  showDeliveryFee={true}
                />
              ))}

              {vendors.length > 5 && (
                <TouchableOpacity
                  style={{
                    backgroundColor: colors.primary[50],
                    borderRadius: 12,
                    padding: 16,
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: colors.primary[200],
                  }}
                  onPress={() => handleNavigate('/search')}
                >
                  <Text style={[typography.body.medium, { color: colors.primary[600], fontWeight: '600' }]}>
                    View All {vendors.length} Services
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
