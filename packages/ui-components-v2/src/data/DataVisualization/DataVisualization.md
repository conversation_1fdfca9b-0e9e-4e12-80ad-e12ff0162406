# DataVisualization

DataVisualization component for data functionality.

## Usage

```tsx
import { DataVisualization } from '@hvppyplug/compound-components'

function Example() {
  return (
    <DataVisualization>
      Content here
    </DataVisualization>
  )
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | - | Child components |
| className | string | - | Additional CSS classes |
| disabled | boolean | false | Whether the component is disabled |

## Examples

### Basic Usage

```tsx
<DataVisualization />
```

### Disabled State

```tsx
<DataVisualization disabled />
```

### With Custom Styling

```tsx
<DataVisualization className="custom-styles" />
```
