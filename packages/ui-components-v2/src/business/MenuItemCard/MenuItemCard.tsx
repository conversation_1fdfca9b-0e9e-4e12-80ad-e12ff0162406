"use client"

import React, { useState } from 'react'
import { View, Pressable, Image } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button } from '@gluestack-ui/themed'
import { PlusIcon, MinusIcon, HeartIcon } from 'lucide-react-native'

// Types
export interface MenuItem {
  id: string
  vendorId: string
  name: string
  description: string
  price: number
  imageUrl?: string
  category: string
  available: boolean
  preparationTime?: number
  customizations?: Array<{
    id: string
    name: string
    options: Array<{
      id: string
      name: string
      price: number
    }>
  }>
  nutritionInfo?: {
    calories?: number
    allergens?: string[]
  }
}

export interface MenuItemCardProps {
  item: MenuItem
  onAddToCart?: (item: MenuItem, quantity: number, customizations?: any) => void
  onPress?: (item: MenuItem) => void
  onToggleFavorite?: (itemId: string) => void
  isFavorite?: boolean
  variant?: 'default' | 'compact' | 'detailed'
  showAddButton?: boolean
  className?: string
}

// Styles
const cardStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg overflow-hidden mb-4",
  variants: {
    variant: {
      default: "p-4",
      compact: "p-3",
      detailed: "p-0",
    },
    pressable: {
      true: "active:bg-background-50",
    },
    unavailable: {
      true: "opacity-60",
    },
  },
})

const imageStyles = tva({
  base: "rounded-lg",
  variants: {
    variant: {
      default: "w-20 h-20",
      compact: "w-16 h-16",
      detailed: "w-full h-48",
    },
  },
})

const contentStyles = tva({
  base: "flex-1",
  variants: {
    variant: {
      default: "ml-4",
      compact: "ml-3",
      detailed: "p-4",
    },
  },
})

const priceStyles = tva({
  base: "font-bold",
  variants: {
    size: {
      sm: "text-sm",
      md: "text-base",
      lg: "text-lg",
    },
  },
})

/**
 * MenuItemCard component for displaying menu items
 * 
 * Features:
 * - Multiple display variants (default, compact, detailed)
 * - Add to cart functionality with quantity controls
 * - Favorite toggle
 * - Availability status
 * - Customization options
 * - Nutrition information
 */
export function MenuItemCard({
  item,
  onAddToCart,
  onPress,
  onToggleFavorite,
  isFavorite = false,
  variant = 'default',
  showAddButton = true,
  className,
}: MenuItemCardProps) {
  const [quantity, setQuantity] = useState(0)

  const handlePress = () => {
    onPress?.(item)
  }

  const handleAddToCart = () => {
    if (quantity > 0) {
      onAddToCart?.(item, quantity)
      setQuantity(0)
    } else {
      setQuantity(1)
    }
  }

  const handleIncrement = () => {
    setQuantity(prev => prev + 1)
  }

  const handleDecrement = () => {
    setQuantity(prev => Math.max(0, prev - 1))
  }

  const handleToggleFavorite = () => {
    onToggleFavorite?.(item.id)
  }

  const renderImage = () => (
    <View className="relative">
      <Image
        source={{ uri: item.imageUrl || 'https://via.placeholder.com/150' }}
        className={imageStyles({ variant })}
        resizeMode="cover"
      />
      {!item.available && (
        <View className="absolute inset-0 bg-black/50 rounded-lg items-center justify-center">
          <Text className="text-white text-xs font-medium">Unavailable</Text>
        </View>
      )}
      {variant === 'detailed' && (
        <Pressable
          onPress={handleToggleFavorite}
          className="absolute top-2 right-2 w-8 h-8 bg-white/80 rounded-full items-center justify-center"
        >
          <HeartIcon 
            size={16} 
            className={isFavorite ? "text-error-500" : "text-typography-400"} 
            fill={isFavorite ? "currentColor" : "none"}
          />
        </Pressable>
      )}
    </View>
  )

  const renderPrice = () => (
    <Text className={priceStyles({ size: variant === 'compact' ? 'sm' : 'md' })}>
      R{item.price.toFixed(2)}
    </Text>
  )

  const renderQuantityControls = () => {
    if (quantity === 0) {
      return (
        <Button
          size="sm"
          onPress={handleAddToCart}
          disabled={!item.available}
          className="px-4"
        >
          <Text>Add</Text>
        </Button>
      )
    }

    return (
      <View className="flex-row items-center bg-primary-100 rounded-lg">
        <Pressable
          onPress={handleDecrement}
          className="w-8 h-8 items-center justify-center"
        >
          <MinusIcon size={16} className="text-primary-700" />
        </Pressable>
        <Text className="mx-3 font-medium text-primary-700">{quantity}</Text>
        <Pressable
          onPress={handleIncrement}
          className="w-8 h-8 items-center justify-center"
        >
          <PlusIcon size={16} className="text-primary-700" />
        </Pressable>
      </View>
    )
  }

  const renderPreparationTime = () => {
    if (!item.preparationTime) return null
    
    return (
      <Text className="text-xs text-typography-500">
        {item.preparationTime} min prep
      </Text>
    )
  }

  const renderCustomizations = () => {
    if (!item.customizations?.length) return null

    return (
      <Text className="text-xs text-primary-600 mt-1">
        Customizable
      </Text>
    )
  }

  const renderNutritionInfo = () => {
    if (!item.nutritionInfo) return null

    return (
      <View className="mt-2">
        {item.nutritionInfo.calories && (
          <Text className="text-xs text-typography-500">
            {item.nutritionInfo.calories} cal
          </Text>
        )}
        {item.nutritionInfo.allergens?.length && (
          <Text className="text-xs text-warning-600">
            Contains: {item.nutritionInfo.allergens.join(', ')}
          </Text>
        )}
      </View>
    )
  }

  const renderContent = () => (
    <View className={contentStyles({ variant })}>
      <View className="flex-row items-start justify-between mb-1">
        <Text 
          className="text-base font-semibold text-typography-900 flex-1 mr-2"
          numberOfLines={variant === 'compact' ? 1 : 2}
        >
          {item.name}
        </Text>
        {variant !== 'detailed' && onToggleFavorite && (
          <Pressable onPress={handleToggleFavorite}>
            <HeartIcon 
              size={16} 
              className={isFavorite ? "text-error-500" : "text-typography-400"} 
              fill={isFavorite ? "currentColor" : "none"}
            />
          </Pressable>
        )}
      </View>
      
      <Text 
        className="text-sm text-typography-600 mb-2" 
        numberOfLines={variant === 'compact' ? 1 : variant === 'detailed' ? undefined : 2}
      >
        {item.description}
      </Text>
      
      <View className="flex-row items-center justify-between">
        <View>
          {renderPrice()}
          {renderPreparationTime()}
          {renderCustomizations()}
        </View>
        
        {showAddButton && item.available && renderQuantityControls()}
      </View>
      
      {variant === 'detailed' && renderNutritionInfo()}
    </View>
  )

  if (variant === 'detailed') {
    return (
      <Pressable
        onPress={handlePress}
        className={cardStyles({ 
          variant, 
          pressable: !!onPress, 
          unavailable: !item.available,
          class: className 
        })}
      >
        {renderImage()}
        {renderContent()}
      </Pressable>
    )
  }

  return (
    <Pressable
      onPress={handlePress}
      className={cardStyles({ 
        variant, 
        pressable: !!onPress, 
        unavailable: !item.available,
        class: className 
      })}
    >
      <View className="flex-row">
        {renderImage()}
        {renderContent()}
      </View>
    </Pressable>
  )
}

// Export variants
export const MenuItemCardCompact = (props: Omit<MenuItemCardProps, 'variant'>) => (
  <MenuItemCard {...props} variant="compact" />
)

export const MenuItemCardDetailed = (props: Omit<MenuItemCardProps, 'variant'>) => (
  <MenuItemCard {...props} variant="detailed" />
)
