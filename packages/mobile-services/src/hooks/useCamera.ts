"use client"

import { useState, useCallback, useEffect } from 'react'
import { CameraService, ImageResult, CameraOptions } from '../services/CameraService'
import { AppwriteService } from '../services/AppwriteService'

export interface UseCameraOptions {
  enableAppwriteUpload?: boolean
  bucketId?: string
  defaultQuality?: number
  maxFileSize?: number
  allowedTypes?: string[]
  onError?: (error: Error) => void
}

export interface UseCameraReturn {
  isLoading: boolean
  error: Error | null
  isCameraAvailable: boolean
  isGalleryAvailable: boolean
  takePhoto: (options?: CameraOptions) => Promise<ImageResult | null>
  selectFromGallery: (options?: CameraOptions) => Promise<ImageResult[] | null>
  showImagePicker: (options?: CameraOptions) => Promise<ImageResult | null>
  uploadImage: (image: ImageResult, fileId?: string) => Promise<string | null>
  uploadImages: (images: ImageResult[]) => Promise<string[]>
  compressImage: (uri: string, quality?: number, maxWidth?: number, maxHeight?: number) => Promise<string>
  deleteImage: (uri: string) => Promise<boolean>
  imageToBase64: (uri: string) => Promise<string | null>
}

/**
 * useCamera hook - React hook for camera services with Appwrite integration
 * 
 * Features:
 * - Camera capture and gallery selection
 * - Image compression and optimization
 * - Appwrite Storage integration
 * - Multiple image handling
 * - Permission management
 * - Image validation
 */
export function useCamera(options: UseCameraOptions = {}): UseCameraReturn {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [isCameraAvailable, setIsCameraAvailable] = useState(false)
  const [isGalleryAvailable, setIsGalleryAvailable] = useState(false)

  const cameraService = CameraService.getInstance()
  const appwriteService = options.enableAppwriteUpload ? AppwriteService.getInstance() : null

  // Initialize camera service
  useEffect(() => {
    const initializeCameraService = async () => {
      try {
        setIsLoading(true)
        setError(null)

        await cameraService.initialize({
          defaultQuality: options.defaultQuality,
          maxFileSize: options.maxFileSize,
          allowedTypes: options.allowedTypes,
          onError: handleError,
        })

        // Check availability
        const cameraAvailable = await cameraService.isCameraAvailable()
        const galleryAvailable = await cameraService.isGalleryAvailable()
        
        setIsCameraAvailable(cameraAvailable)
        setIsGalleryAvailable(galleryAvailable)
      } catch (err) {
        handleError(err as Error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeCameraService()
  }, [])

  // Handle errors
  const handleError = useCallback(
    (err: Error) => {
      setError(err)
      options.onError?.(err)
    },
    [options.onError]
  )

  // Take photo
  const takePhoto = useCallback(
    async (cameraOptions?: CameraOptions): Promise<ImageResult | null> => {
      try {
        setIsLoading(true)
        setError(null)

        const result = await cameraService.takePhoto(cameraOptions)
        return result
      } catch (err) {
        handleError(err as Error)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [handleError]
  )

  // Select from gallery
  const selectFromGallery = useCallback(
    async (cameraOptions?: CameraOptions): Promise<ImageResult[] | null> => {
      try {
        setIsLoading(true)
        setError(null)

        const results = await cameraService.selectFromGallery(cameraOptions)
        return results
      } catch (err) {
        handleError(err as Error)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [handleError]
  )

  // Show image picker
  const showImagePicker = useCallback(
    async (cameraOptions?: CameraOptions): Promise<ImageResult | null> => {
      try {
        setIsLoading(true)
        setError(null)

        const result = await cameraService.showImagePicker(cameraOptions)
        return result
      } catch (err) {
        handleError(err as Error)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [handleError]
  )

  // Upload image to Appwrite
  const uploadImage = useCallback(
    async (image: ImageResult, fileId?: string): Promise<string | null> => {
      if (!options.enableAppwriteUpload || !appwriteService) {
        handleError(new Error('Appwrite upload not enabled'))
        return null
      }

      try {
        setIsLoading(true)
        setError(null)

        const url = await appwriteService.uploadImage(
          image,
          options.bucketId,
          fileId
        )
        return url
      } catch (err) {
        handleError(err as Error)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    [options.enableAppwriteUpload, options.bucketId, appwriteService, handleError]
  )

  // Upload multiple images
  const uploadImages = useCallback(
    async (images: ImageResult[]): Promise<string[]> => {
      if (!options.enableAppwriteUpload || !appwriteService) {
        handleError(new Error('Appwrite upload not enabled'))
        return []
      }

      try {
        setIsLoading(true)
        setError(null)

        const urls = await appwriteService.uploadImages(images, options.bucketId)
        return urls
      } catch (err) {
        handleError(err as Error)
        return []
      } finally {
        setIsLoading(false)
      }
    },
    [options.enableAppwriteUpload, options.bucketId, appwriteService, handleError]
  )

  // Compress image
  const compressImage = useCallback(
    async (
      uri: string,
      quality: number = 0.8,
      maxWidth?: number,
      maxHeight?: number
    ): Promise<string> => {
      try {
        setError(null)
        return await cameraService.compressImage(uri, quality, maxWidth, maxHeight)
      } catch (err) {
        handleError(err as Error)
        return uri
      }
    },
    [handleError]
  )

  // Delete image
  const deleteImage = useCallback(
    async (uri: string): Promise<boolean> => {
      try {
        setError(null)
        return await cameraService.deleteImage(uri)
      } catch (err) {
        handleError(err as Error)
        return false
      }
    },
    [handleError]
  )

  // Convert image to base64
  const imageToBase64 = useCallback(
    async (uri: string): Promise<string | null> => {
      try {
        setError(null)
        return await cameraService.imageToBase64(uri)
      } catch (err) {
        handleError(err as Error)
        return null
      }
    },
    [handleError]
  )

  return {
    isLoading,
    error,
    isCameraAvailable,
    isGalleryAvailable,
    takePhoto,
    selectFromGallery,
    showImagePicker,
    uploadImage,
    uploadImages,
    compressImage,
    deleteImage,
    imageToBase64,
  }
}
