# @hvppyplug/payments

> **Unified payment processing library for HVPPYPlug+ supporting Polar, PayFast, M-Pesa, and voucher payments**

A comprehensive, production-ready payment processing library built specifically for the South African market and international payments. Supports multiple payment providers with a unified API, comprehensive error handling, and cross-platform compatibility.

## 🌍 **Supported Payment Methods**

### **International Payments**
- **Polar.sh** - Modern payment infrastructure with global support including South Africa 🇿🇦
- Credit/Debit Cards via Polar checkout

### **South African Payments**
- **PayFast** - Leading South African payment gateway
- **EFT/Bank Transfer** via PayFast
- **Local Cards** via PayFast

### **Mobile Money**
- **M-Pesa** - Mobile money payments
- **STK Push** integration

### **Voucher Payments**
- **1Voucher** - Prepaid voucher system
- **OTT Vouchers** - Over-the-top voucher payments

### **Alternative Methods**
- **Cash on Delivery** - For local deliveries

## 🚀 **Quick Start**

### Installation

```bash
pnpm add @hvppyplug/payments
```

### Basic Usage

```typescript
import { PaymentManager, createPaymentProvider } from '@hvppyplug/payments';

// Initialize payment manager
const paymentManager = new PaymentManager({
  providers: [
    {
      type: 'polar',
      config: {
        accessToken: process.env.POLAR_ACCESS_TOKEN!,
        webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
        testMode: true,
        server: 'sandbox'
      }
    }
  ],
  defaultProvider: 'polar'
});

// Create a payment
const paymentResult = await paymentManager.createPaymentIntent({
  amount: {
    value: 99.99,
    currency: 'ZAR',
    formatted: 'R99.99'
  },
  paymentMethod: 'polar_checkout',
  customerId: 'customer_123',
  orderId: 'order_456',
  description: 'HVPPYPlug+ Order #456'
});

if (paymentResult.success && paymentResult.requiresAction) {
  // Redirect user to payment URL
  window.location.href = paymentResult.actionUrl!;
}
```

## 📱 **React Native Integration**

### Polar Checkout with WebView

```typescript
import React from 'react';
import { WebView } from 'react-native-webview';
import { usePayment } from '@hvppyplug/payments';

export function PaymentScreen({ orderId, amount }) {
  const { createPayment, paymentStatus } = usePayment();

  const handlePayment = async () => {
    const result = await createPayment({
      amount: { value: amount, currency: 'ZAR', formatted: `R${amount}` },
      paymentMethod: 'polar_checkout',
      customerId: 'customer_123',
      orderId,
      returnUrl: 'hvppyplug://payment/success',
      cancelUrl: 'hvppyplug://payment/cancel'
    });

    if (result.success && result.actionUrl) {
      // Show WebView with payment URL
      return (
        <WebView
          source={{ uri: result.actionUrl }}
          onNavigationStateChange={(navState) => {
            if (navState.url.includes('hvppyplug://payment/success')) {
              // Payment successful
              navigation.navigate('PaymentSuccess');
            }
          }}
        />
      );
    }
  };

  return (
    // Your payment UI
  );
}
```

## 🔧 **Configuration**

### Environment Variables

```bash
# Polar Configuration
POLAR_ACCESS_TOKEN=polar_pat_your_access_token
POLAR_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayFast Configuration (TODO)
PAYFAST_MERCHANT_ID=your_merchant_id
PAYFAST_MERCHANT_KEY=your_merchant_key
PAYFAST_PASSPHRASE=your_passphrase

# M-Pesa Configuration (TODO)
MPESA_CONSUMER_KEY=your_consumer_key
MPESA_CONSUMER_SECRET=your_consumer_secret
MPESA_BUSINESS_SHORT_CODE=your_short_code

# Voucher Configuration (TODO)
VOUCHER_API_KEY=your_api_key
VOUCHER_API_SECRET=your_api_secret
```

### Provider Configuration

```typescript
import { PaymentManager } from '@hvppyplug/payments';

const paymentManager = new PaymentManager({
  providers: [
    // Polar for international payments
    {
      type: 'polar',
      config: {
        accessToken: process.env.POLAR_ACCESS_TOKEN!,
        webhookSecret: process.env.POLAR_WEBHOOK_SECRET!,
        testMode: process.env.NODE_ENV !== 'production',
        server: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
        successUrl: 'https://hvppyplug.co.za/payment/success',
        cancelUrl: 'https://hvppyplug.co.za/payment/cancel'
      }
    },
    // PayFast for South African payments (TODO)
    // {
    //   type: 'payfast',
    //   config: {
    //     merchantId: process.env.PAYFAST_MERCHANT_ID!,
    //     merchantKey: process.env.PAYFAST_MERCHANT_KEY!,
    //     testMode: process.env.NODE_ENV !== 'production'
    //   }
    // }
  ],
  defaultProvider: 'polar',
  fallbackProviders: ['payfast'],
  retryAttempts: 3
});
```

## 🎯 **Features**

### ✅ **Currently Implemented**
- **Polar.sh Integration** - Complete with checkout, webhooks, validation
- **Comprehensive TypeScript Types** - Full type safety
- **Error Handling** - Detailed error codes and recovery
- **Webhook Processing** - Secure signature verification
- **Payment Manager** - Unified API across providers
- **Validation** - Request/response validation
- **Cross-Platform** - React Native and Web support

### 🚧 **Coming Soon**
- **PayFast Integration** - South African payment gateway
- **M-Pesa Integration** - Mobile money payments
- **Voucher System** - 1Voucher and OTT support
- **React Hooks** - `usePayment`, `usePaymentStatus`
- **UI Components** - Pre-built payment forms
- **Security Features** - Encryption and secure storage
- **Analytics** - Payment metrics and reporting

## 🔐 **Security**

### PCI DSS Compliance
- **No sensitive data storage** - All card data handled by providers
- **Tokenization** - Secure token-based payments
- **Encryption** - All data encrypted in transit and at rest
- **Webhook verification** - Cryptographic signature validation

### Best Practices
- **Environment variables** for sensitive configuration
- **Secure storage** using React Native Keychain
- **Input validation** and sanitization
- **Error handling** without exposing sensitive data

## 📊 **Supported Countries & Currencies**

### **Polar.sh Coverage**
- **Global payments** including South Africa 🇿🇦
- **Currencies**: USD, EUR, GBP, ZAR, CAD, AUD
- **Merchant of Record** - Handles international tax compliance

### **PayFast Coverage** (Coming Soon)
- **South Africa** 🇿🇦 only
- **Currency**: ZAR
- **Local payment methods** - EFT, Cards, Instant EFT

### **M-Pesa Coverage** (Coming Soon)
- **Kenya** 🇰🇪, **Tanzania** 🇹🇿, **Uganda** 🇺🇬
- **Currency**: KES, TZS, UGX
- **Mobile money** payments

## 🛠️ **Development**

### Building the Library

```bash
# Install dependencies
pnpm install

# Build the library
pnpm build

# Watch mode for development
pnpm build:watch

# Type checking
pnpm type-check

# Linting
pnpm lint

# Testing
pnpm test
```

### Testing

```bash
# Run all tests
pnpm test

# Watch mode
pnpm test:watch

# Coverage report
pnpm test:coverage
```

## 📚 **API Reference**

### PaymentManager

```typescript
class PaymentManager {
  constructor(config: PaymentManagerConfig)
  
  async createPaymentIntent(request: PaymentRequest): Promise<PaymentResult>
  async confirmPayment(paymentIntentId: string): Promise<PaymentResult>
  async cancelPayment(paymentIntentId: string): Promise<PaymentResult>
  async refundPayment(request: RefundRequest): Promise<RefundResult>
  async getPaymentStatus(paymentIntentId: string): Promise<PaymentIntent>
  async handleWebhook(payload: string, signature: string, providerType: string): Promise<void>
}
```

### Types

```typescript
interface PaymentRequest {
  amount: Amount;
  paymentMethod: PaymentMethod;
  customerId: string;
  orderId: string;
  description?: string;
  metadata?: Record<string, string>;
  returnUrl?: string;
  cancelUrl?: string;
}

interface PaymentResult {
  success: boolean;
  paymentIntent: PaymentIntent;
  transactionId?: string;
  requiresAction?: boolean;
  actionUrl?: string;
  error?: string;
}
```

## 🤝 **Contributing**

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Issues**: [GitHub Issues](https://github.com/hvppyplug/monorepo/issues)
- **Email**: <EMAIL>
- **Discord**: [HVPPYPlug+ Community](https://discord.gg/hvppyplug)

---

> **Ready to accept payments in South Africa and globally?** 
> Get started with HVPPYPlug+ Payments - where local meets global. 💳🌍
