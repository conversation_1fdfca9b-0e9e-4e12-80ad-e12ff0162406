export type UserRole = 'customer' | 'vendor' | 'runner';

export interface User {
  id: string;
  role: UserRole;
  name: string;
  phone: string;
  avatarUrl?: string;
}

export interface MenuItem {
  id: string;
  vendorId: string;
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  category: 'snack' | 'drink' | 'service';
}

export interface Order {
  id: string;
  customerId: string;
  vendorId: string;
  runnerId?: string;
  items: Array<{ itemId: string; quantity: number }>;
  subtotal: number;
  discount: number;
  total: number;
  status:
    | 'pending'
    | 'accepted'
    | 'picked_up'
    | 'en_route'
    | 'delivered'
    | 'cancelled';
  paymentMethod: 'card' | 'mpesa' | 'voucher' | 'payfast';
  createdAt: string;
  updatedAt: string;
}

export interface VoucherResult {
  valid: boolean;
  amount: number;
  currency: string;
}
