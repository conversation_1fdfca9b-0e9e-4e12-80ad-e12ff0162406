import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { DashboardStackParamList } from '../types/navigation'
import { useTheme } from '../providers/ThemeProvider'

// Import Dashboard Screens (placeholders for now)
import { DashboardHomeScreen } from '../screens/dashboard/DashboardHomeScreen'

const Stack = createNativeStackNavigator<DashboardStackParamList>()

export function DashboardStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="DashboardHome"
        component={DashboardHomeScreen}
        options={{
          title: 'Dashboard',
          headerShown: false, // Custom header in the screen
        }}
      />
    </Stack.Navigator>
  )
}
