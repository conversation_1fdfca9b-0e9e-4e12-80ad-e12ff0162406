{"name": "@hvppyplug/backend", "version": "1.0.0", "private": true, "description": "HVPPYPlug+ Appwrite Functions and Configuration", "main": "src/index.js", "scripts": {"dev": "appwrite functions createDeployment --functionId=hvppy-api --activate=true", "build": "tsc && npm run package", "package": "tar -czf code.tar.gz dist package.json", "deploy": "appwrite functions createDeployment --functionId=hvppy-api --code=./code.tar.gz --activate=true", "logs": "appwrite functions listExecutions --functionId=hvppy-api", "clean": "rm -rf dist code.tar.gz", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"@hvppyplug/common": "workspace:*", "appwrite": "^14.0.1", "node-appwrite": "^13.0.0", "stripe": "^14.21.0", "axios": "^1.6.8", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.24", "tsx": "^4.7.1", "typescript": "^5.3.3", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1"}}