# Ecommerce Components Library

This directory contains specialized components converted from a homestay booking platform to a comprehensive multi-vendor ecommerce marketplace. All components are designed to be customizable, extendable, and production-ready.

## 🏗️ Architecture Overview

The components are organized into logical groups:

- **Layout Components**: Header, Sidebar, Main content areas
- **Product Components**: Product cards, listings, details
- **Cart Components**: Shopping cart, checkout flow
- **Admin Components**: Vendor management, order management
- **Filter Components**: Category filters, price ranges, ratings

## 🎯 Key Features

### Multi-Vendor Support
- Vendor information display on products
- Vendor management dashboard
- Vendor-specific filtering and sorting

### Customizable & Extendable
- Comprehensive prop interfaces with TypeScript
- Flexible styling with Tailwind CSS classes
- Configurable behavior through callback props
- Theme support (light/dark mode)

### Ecommerce Focused
- Shopping cart functionality
- Product filtering and search
- Order management
- Rating and review systems
- Pricing and discount displays

## 📦 Component Categories

### Layout Components

#### Header
```tsx
import { Header } from '@/special';

<Header
  search={{ placeholder: "Search products..." }}
  cart={{ itemCount: 3, onCartClick: handleCartClick }}
  user={{ isLoggedIn: true, name: "<PERSON> Doe" }}
  vendorAccess={{ show: true, onVendorDashboard: handleVendorDashboard }}
/>
```

**Props:**
- `logo`: Logo configuration (src, alt, dimensions)
- `search`: Search functionality and placeholder
- `cart`: Shopping cart state and handlers
- `user`: User authentication state and profile
- `tabs`: Navigation tabs configuration
- `vendorAccess`: Vendor dashboard access

### Product Components

#### ProductCard
```tsx
import { ProductCard } from '@/special';

<ProductCard
  product={productData}
  variant="grid" // or "list", "compact"
  showVendor={true}
  showAddToCart={true}
  onProductClick={handleProductClick}
  onAddToCart={handleAddToCart}
/>
```

**Props:**
- `product`: Complete product data object
- `variant`: Display layout (grid, list, compact)
- `showVendor`: Display vendor information
- `showAddToCart`: Show add to cart button
- `onProductClick`: Product click handler
- `onAddToCart`: Add to cart handler

### Filter Components

#### ProductCategorySection
```tsx
import { ProductCategorySection } from '@/special';

<ProductCategorySection
  title="Categories"
  categories={categoryData}
  selectedValues={selectedCategories}
  onChange={handleCategoryChange}
  showCount={true}
  multiSelect={true}
/>
```

#### ProductRatingSection
```tsx
import { ProductRatingSection } from '@/special';

<ProductRatingSection
  title="Customer Ratings"
  ratings={ratingOptions}
  selectedValues={selectedRatings}
  onChange={handleRatingChange}
  showCount={true}
/>
```

#### ProductOptions
```tsx
import { ProductOptions } from '@/special';

<ProductOptions
  title="Product Options"
  options={[
    { id: "freeShipping", label: "Free Shipping", description: "..." },
    { id: "fastDelivery", label: "Fast Delivery", description: "..." }
  ]}
  values={optionValues}
  onValueChange={handleOptionChange}
/>
```

### Cart Components

#### ShoppingCartSidebar
```tsx
import { ShoppingCartSidebar } from '@/special';

<ShoppingCartSidebar
  items={cartItems}
  onUpdateQuantity={handleQuantityUpdate}
  onRemoveItem={handleRemoveItem}
  onCheckout={handleCheckout}
  subtotal={subtotal}
  total={total}
/>
```

### Admin Components

#### VendorManagement
```tsx
import { VendorManagement } from '@/special';

<VendorManagement
  vendors={vendorData}
  onApprove={handleVendorApproval}
  onSuspend={handleVendorSuspension}
  onSearch={handleVendorSearch}
  onFilter={handleVendorFilter}
/>
```

#### OrderManagement
```tsx
import { OrderManagement } from '@/special';

<OrderManagement
  orders={orderData}
  onUpdateStatus={handleStatusUpdate}
  onViewDetails={handleViewDetails}
  onSearch={handleOrderSearch}
/>
```

## 🎨 Customization

### Theme Support
All components support light/dark mode through the ThemeContext:

```tsx
import { ThemeContext } from '@/App';

const { colorMode } = useContext(ThemeContext);
```

### Styling
Components use Tailwind CSS classes and can be customized through:
- `className` props for additional styling
- CSS custom properties for theme colors
- Variant props for different layouts

### Behavior Customization
- Callback props for all user interactions
- Configurable display options (show/hide features)
- Flexible data structures with optional fields

## 🔧 TypeScript Support

All components include comprehensive TypeScript interfaces:

```tsx
export interface ProductCardProps {
  product: Product;
  variant?: "grid" | "list" | "compact";
  showVendor?: boolean;
  showAddToCart?: boolean;
  onProductClick?: (product: Product) => void;
  onAddToCart?: (product: Product) => void;
  // ... more props
}
```

## 📱 Responsive Design

Components are built with mobile-first responsive design:
- Adaptive layouts for different screen sizes
- Touch-friendly interactions
- Optimized mobile experiences
- Desktop-specific features when appropriate

## 🚀 Usage Examples

### Basic Ecommerce Layout
```tsx
import { 
  Header, 
  ProductCard, 
  ProductCategorySection,
  ShoppingCartSidebar 
} from '@/special';

function EcommercePage() {
  return (
    <div>
      <Header 
        cart={{ itemCount: cartItems.length }}
        user={{ isLoggedIn: true }}
      />
      
      <div className="flex">
        <aside className="w-64">
          <ProductCategorySection 
            categories={categories}
            onChange={handleCategoryFilter}
          />
        </aside>
        
        <main className="flex-1">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {products.map(product => (
              <ProductCard 
                key={product.id}
                product={product}
                onAddToCart={handleAddToCart}
              />
            ))}
          </div>
        </main>
        
        <aside className="w-80">
          <ShoppingCartSidebar 
            items={cartItems}
            onCheckout={handleCheckout}
          />
        </aside>
      </div>
    </div>
  );
}
```

### Admin Dashboard
```tsx
import { VendorManagement, OrderManagement } from '@/special';

function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('vendors');
  
  return (
    <div>
      {activeTab === 'vendors' && (
        <VendorManagement 
          vendors={vendors}
          onApprove={handleVendorApproval}
        />
      )}
      
      {activeTab === 'orders' && (
        <OrderManagement 
          orders={orders}
          onUpdateStatus={handleOrderStatusUpdate}
        />
      )}
    </div>
  );
}
```

## 🔄 Migration from Homestay Components

The components have been systematically converted:

| Original | Converted | Changes |
|----------|-----------|---------|
| `HomestayLogo` | `MarketplaceLogo` | Added customization props, fallback text logo |
| `BookingOptions` | `ProductOptions` | Configurable options, ecommerce-focused defaults |
| `PlaceTypeSection` | `ProductCategorySection` | Product categories, count display |
| `CustomerRatingSection` | `ProductRatingSection` | Enhanced star display, review counts |
| `PriceRangeSection` | `PriceRangeSection` | Maintained, works for product pricing |

## 🎯 Best Practices

1. **Performance**: Use React.memo for expensive components
2. **Accessibility**: All components include proper ARIA labels
3. **Error Handling**: Graceful fallbacks for missing data
4. **Loading States**: Support for loading and empty states
5. **Type Safety**: Comprehensive TypeScript interfaces

## 🔮 Future Enhancements

- Advanced filtering with faceted search
- Real-time inventory updates
- Enhanced vendor analytics
- Multi-language support
- Advanced theming system
- Performance optimizations with virtualization

## 📚 Additional Resources

- [Component API Documentation](./docs/api.md)
- [Styling Guide](./docs/styling.md)
- [Migration Guide](./docs/migration.md)
- [Examples Repository](./examples/)