import React from 'react'
import { View, Text, Switch, StyleSheet, TouchableOpacity } from 'react-native'
import { Wifi, WifiOff, MapPin, Clock } from 'lucide-react-native'

import { Card } from '../ui'
import { useTheme } from '../../providers/ThemeProvider'

export interface StatusToggleProps {
  isOnline: boolean
  onToggle: (isOnline: boolean) => void
  disabled?: boolean
  showStats?: boolean
  stats?: {
    activeTime?: string
    ordersReceived?: number
    currentLocation?: string
  }
}

export function StatusToggle({
  isOnline,
  onToggle,
  disabled = false,
  showStats = true,
  stats,
}: StatusToggleProps) {
  const { theme } = useTheme()

  const getStatusColor = () => {
    return isOnline ? theme.colors.success[600] : theme.colors.gray[500]
  }

  const getStatusIcon = () => {
    return isOnline ? Wifi : WifiOff
  }

  const getStatusText = () => {
    return isOnline ? 'You\'re Online' : 'You\'re Offline'
  }

  const getStatusSubtext = () => {
    return isOnline 
      ? 'Ready to receive orders from customers'
      : 'Turn on to start receiving orders'
  }

  const StatusIcon = getStatusIcon()

  return (
    <Card variant="elevated" size="md" style={styles.card}>
      {/* Main Status Toggle */}
      <View style={styles.statusContainer}>
        <View style={styles.statusInfo}>
          <View style={styles.statusHeader}>
            <View style={[styles.statusIcon, { backgroundColor: `${getStatusColor()}20` }]}>
              <StatusIcon size={24} color={getStatusColor()} />
            </View>
            <View style={styles.statusText}>
              <Text style={[styles.statusTitle, { color: theme.colors.gray[900] }]}>
                {getStatusText()}
              </Text>
              <Text style={[styles.statusSubtitle, { color: theme.colors.gray[600] }]}>
                {getStatusSubtext()}
              </Text>
            </View>
          </View>
        </View>

        <Switch
          value={isOnline}
          onValueChange={onToggle}
          disabled={disabled}
          trackColor={{
            false: theme.colors.gray[300],
            true: theme.colors.success[200],
          }}
          thumbColor={isOnline ? theme.colors.success[600] : theme.colors.gray[500]}
          style={styles.switch}
        />
      </View>

      {/* Stats Section */}
      {showStats && stats && isOnline && (
        <View style={styles.statsContainer}>
          <View style={styles.statsHeader}>
            <Text style={[styles.statsTitle, { color: theme.colors.gray[700] }]}>
              Session Stats
            </Text>
          </View>

          <View style={styles.statsGrid}>
            {stats.activeTime && (
              <View style={styles.statItem}>
                <Clock size={16} color={theme.colors.primary[600]} />
                <View style={styles.statText}>
                  <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                    {stats.activeTime}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                    Active Time
                  </Text>
                </View>
              </View>
            )}

            {stats.ordersReceived !== undefined && (
              <View style={styles.statItem}>
                <View style={[styles.ordersBadge, { backgroundColor: theme.colors.primary[600] }]}>
                  <Text style={[styles.ordersCount, { color: theme.colors.white }]}>
                    {stats.ordersReceived}
                  </Text>
                </View>
                <View style={styles.statText}>
                  <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
                    Orders
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                    Received
                  </Text>
                </View>
              </View>
            )}

            {stats.currentLocation && (
              <View style={[styles.statItem, styles.locationItem]}>
                <MapPin size={16} color={theme.colors.secondary[600]} />
                <View style={styles.statText}>
                  <Text style={[styles.statValue, { color: theme.colors.gray[900] }]} numberOfLines={1}>
                    {stats.currentLocation}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
                    Current Location
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Offline Message */}
      {!isOnline && (
        <View style={[styles.offlineMessage, { backgroundColor: theme.colors.gray[50] }]}>
          <Text style={[styles.offlineText, { color: theme.colors.gray[600] }]}>
            💡 Tip: Turn on your status to start earning money by delivering orders in your area.
          </Text>
        </View>
      )}
    </Card>
  )
}

// Compact version for use in headers or smaller spaces
export interface CompactStatusToggleProps {
  isOnline: boolean
  onToggle: (isOnline: boolean) => void
  disabled?: boolean
}

export function CompactStatusToggle({
  isOnline,
  onToggle,
  disabled = false,
}: CompactStatusToggleProps) {
  const { theme } = useTheme()

  const getStatusColor = () => {
    return isOnline ? theme.colors.success[600] : theme.colors.gray[500]
  }

  return (
    <TouchableOpacity
      style={[styles.compactContainer, { borderColor: getStatusColor() }]}
      onPress={() => onToggle(!isOnline)}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <View style={[styles.compactIndicator, { backgroundColor: getStatusColor() }]} />
      <Text style={[styles.compactText, { color: getStatusColor() }]}>
        {isOnline ? 'Online' : 'Offline'}
      </Text>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusInfo: {
    flex: 1,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  statusText: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  switch: {
    marginLeft: 16,
  },
  statsContainer: {
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statsHeader: {
    marginBottom: 12,
  },
  statsTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsGrid: {
    gap: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationItem: {
    flex: 1,
  },
  statText: {
    marginLeft: 12,
    flex: 1,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
  },
  ordersBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ordersCount: {
    fontSize: 12,
    fontWeight: '700',
  },
  offlineMessage: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
  },
  offlineText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  // Compact styles
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
  },
  compactIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  compactText: {
    fontSize: 14,
    fontWeight: '600',
  },
})
