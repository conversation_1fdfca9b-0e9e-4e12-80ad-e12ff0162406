import { defineConfig } from 'tsup'

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  external: [
    'react',
    'react-native',
    'expo-location',
    'expo-notifications',
    'expo-camera',
    'expo-image-picker',
    'expo-secure-store',
    'expo-local-authentication',
    'expo-task-manager',
    'expo-background-fetch',
    'expo-device',
    'expo-application',
    'expo-constants',
    'expo-network',
    'expo-file-system',
    'expo-haptics',
    'expo-linking',
    '@react-native-async-storage/async-storage',
    'appwrite'
  ],
})
