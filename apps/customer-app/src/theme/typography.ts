import { Platform } from 'react-native'

// Font Families
export const fontFamilies = {
  regular: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),
  medium: Platform.select({
    ios: 'System',
    android: 'Roboto-Medium',
    default: 'System',
  }),
  semibold: Platform.select({
    ios: 'System',
    android: 'Roboto-Medium',
    default: 'System',
  }),
  bold: Platform.select({
    ios: 'System',
    android: 'Roboto-Bold',
    default: 'System',
  }),
} as const

// Font Weights
export const fontWeights = {
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
}

// Font Sizes
export const fontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
  '6xl': 60,
} as const

// Line Heights
export const lineHeights = {
  xs: 16,
  sm: 20,
  base: 24,
  lg: 28,
  xl: 32,
  '2xl': 36,
  '3xl': 40,
  '4xl': 44,
  '5xl': 56,
  '6xl': 72,
} as const

// Letter Spacing
export const letterSpacing = {
  tighter: -0.5,
  tight: -0.25,
  normal: 0,
  wide: 0.25,
  wider: 0.5,
  widest: 1,
} as const

// Typography Styles
export const typography = {
  // Display Styles
  display: {
    large: {
      fontFamily: fontFamilies.bold,
      fontSize: fontSizes['5xl'],
      lineHeight: lineHeights['5xl'],
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.tight,
    },
    medium: {
      fontFamily: fontFamilies.bold,
      fontSize: fontSizes['4xl'],
      lineHeight: lineHeights['4xl'],
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.tight,
    },
    small: {
      fontFamily: fontFamilies.bold,
      fontSize: fontSizes['3xl'],
      lineHeight: lineHeights['3xl'],
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.normal,
    },
  },

  // Heading Styles
  heading: {
    h1: {
      fontFamily: fontFamilies.bold,
      fontSize: fontSizes['2xl'],
      lineHeight: lineHeights['2xl'],
      fontWeight: fontWeights.bold,
      letterSpacing: letterSpacing.normal,
    },
    h2: {
      fontFamily: fontFamilies.semibold,
      fontSize: fontSizes.xl,
      lineHeight: lineHeights.xl,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.normal,
    },
    h3: {
      fontFamily: fontFamilies.semibold,
      fontSize: fontSizes.lg,
      lineHeight: lineHeights.lg,
      fontWeight: fontWeights.semibold,
      letterSpacing: letterSpacing.normal,
    },
    h4: {
      fontFamily: fontFamilies.medium,
      fontSize: fontSizes.base,
      lineHeight: lineHeights.base,
      fontWeight: fontWeights.medium,
      letterSpacing: letterSpacing.normal,
    },
  },

  // Body Styles
  body: {
    large: {
      fontFamily: fontFamilies.regular,
      fontSize: fontSizes.lg,
      lineHeight: lineHeights.lg,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
    medium: {
      fontFamily: fontFamilies.regular,
      fontSize: fontSizes.base,
      lineHeight: lineHeights.base,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
    small: {
      fontFamily: fontFamilies.regular,
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.sm,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
  },

  // Label Styles
  label: {
    large: {
      fontFamily: fontFamilies.medium,
      fontSize: fontSizes.base,
      lineHeight: lineHeights.base,
      fontWeight: fontWeights.medium,
      letterSpacing: letterSpacing.normal,
    },
    medium: {
      fontFamily: fontFamilies.medium,
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.sm,
      fontWeight: fontWeights.medium,
      letterSpacing: letterSpacing.normal,
    },
    small: {
      fontFamily: fontFamilies.medium,
      fontSize: fontSizes.xs,
      lineHeight: lineHeights.xs,
      fontWeight: fontWeights.medium,
      letterSpacing: letterSpacing.wide,
    },
  },

  // Caption Styles
  caption: {
    large: {
      fontFamily: fontFamilies.regular,
      fontSize: fontSizes.sm,
      lineHeight: lineHeights.sm,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
    small: {
      fontFamily: fontFamilies.regular,
      fontSize: fontSizes.xs,
      lineHeight: lineHeights.xs,
      fontWeight: fontWeights.normal,
      letterSpacing: letterSpacing.normal,
    },
  },
} as const

export type Typography = typeof typography
