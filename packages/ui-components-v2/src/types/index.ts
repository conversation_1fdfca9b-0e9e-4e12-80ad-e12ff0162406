/**
 * Common types for compound components
 */

// Base component props
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

// Common size variants
export type SizeVariant = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

// Common color variants
export type ColorVariant = 
  | 'primary' 
  | 'secondary' 
  | 'tertiary'
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info'
  | 'neutral'

// Common status types
export type StatusType = 'success' | 'warning' | 'error' | 'info' | 'neutral'

// Loading states
export interface LoadingState {
  loading?: boolean
  loadingMessage?: string
}

// Empty states
export interface EmptyState {
  emptyMessage?: string
  emptyIcon?: React.ComponentType<any>
  emptyAction?: {
    label: string
    onPress: () => void
  }
}

// Pagination
export interface PaginationState {
  currentPage: number
  pageSize: number
  totalItems: number
  hasMore: boolean
}

export interface PaginationProps {
  pagination?: boolean
  pageSize?: number
  onLoadMore?: () => void
}

// Search and filtering
export interface SearchState {
  query: string
  filters: Record<string, any>
}

export interface SearchProps {
  searchable?: boolean
  onSearch?: (query: string) => void
  searchPlaceholder?: string
}

export interface FilterProps {
  filterable?: boolean
  filters?: Array<{
    key: string
    label: string
    type: 'text' | 'select' | 'date' | 'boolean'
    options?: Array<{ label: string; value: any }>
  }>
  onFilter?: (filters: Record<string, any>) => void
}

// Sorting
export interface SortState {
  column: string | null
  direction: 'asc' | 'desc'
}

export interface SortProps {
  sortable?: boolean
  onSort?: (column: string, direction: 'asc' | 'desc') => void
}

// Form validation
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}

export interface FieldError {
  message: string
  type: string
}

export interface FormFieldState {
  value: any
  error?: FieldError
  touched: boolean
  dirty: boolean
}

// Data table specific types
export interface TableColumn<T = any> {
  key: string
  title: string
  sortable?: boolean
  filterable?: boolean
  width?: number | string
  render?: (value: any, row: T, index: number) => React.ReactNode
  align?: 'left' | 'center' | 'right'
}

// Dashboard widget types
export interface WidgetData {
  title: string
  value: string | number
  subtitle?: string
  trend?: {
    value: number
    direction: 'up' | 'down' | 'neutral'
    label?: string
  }
  comparison?: {
    value: string | number
    label: string
  }
  status?: StatusType
}

// Action types
export interface ActionItem {
  label: string
  icon?: React.ComponentType<any>
  onPress: () => void
  disabled?: boolean
  destructive?: boolean
}

// Theme types
export interface ThemeColors {
  primary: Record<string, string>
  secondary: Record<string, string>
  tertiary: Record<string, string>
  success: Record<string, string>
  warning: Record<string, string>
  error: Record<string, string>
  info: Record<string, string>
  typography: Record<string, string>
  background: Record<string, string>
  outline: Record<string, string>
}

// Platform detection
export type Platform = 'ios' | 'android' | 'web' | 'windows' | 'macos'

// Responsive breakpoints
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

// Animation types
export interface AnimationConfig {
  duration?: number
  easing?: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out'
  delay?: number
}

// Accessibility types
export interface AccessibilityProps {
  accessibilityLabel?: string
  accessibilityHint?: string
  accessibilityRole?: string
  accessibilityState?: {
    disabled?: boolean
    selected?: boolean
    checked?: boolean | 'mixed'
    busy?: boolean
    expanded?: boolean
  }
}

// Event handler types
export type PressHandler = () => void
export type ChangeHandler<T = any> = (value: T) => void
export type SelectHandler<T = any> = (item: T, index: number) => void

// Generic list item
export interface ListItem {
  id: string | number
  [key: string]: any
}

// Component state types
export type ComponentState = 'idle' | 'loading' | 'success' | 'error'

// Export utility type helpers
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>
