# HVPPYPlug+ Monorepo

> **"Plugged Into the Vibe. Snacks. Services. Sorted."**

A hyper-local, mobile-first ecosystem built for **Soweto**, South Africa, bringing together on-demand snack delivery and real-world services in one unified platform.

## 🏗️ Architecture Overview

This monorepo contains:

- **3 Expo React Native Apps**: Customer, Vendor, Runner
- **1 Next.js Web Admin**: Management dashboard with shadcn/ui
- **1 Appwrite Backend**: Functions, Database, Auth, and Storage
- **Shared Common Package**: Types, utilities, components, and configuration

## 📁 Project Structure

```
HVPPYPlug-Monorepo/
├── packages/
│   └── common/                 # Shared code, types, utilities & components
├── apps/
│   ├── customer-app/          # Expo app for end users
│   ├── vendor-app/            # Expo app for shop & service owners
│   ├── runner-app/            # Expo app for delivery & service agents
│   └── web-admin/            # Next.js admin dashboard with shadcn/ui
├── backend/                   # Node.js Express API
├── docs/                      # Documentation, diagrams, specs
├── scripts/                   # Build, deploy, migration scripts
└── package.json              # Workspace configuration
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** 18+
- **pnpm** 8+
- **Expo CLI** (install with `npm install -g @expo/cli`)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/hvppyplug-monorepo.git
   cd hvppyplug-monorepo
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

4. **Build the common package**
   ```bash
   pnpm build:common
   ```

### Development

**Run all apps in parallel:**
```bash
pnpm dev
```

**Run individual apps:**
```bash
# Backend API
cd backend && pnpm dev

# Customer App
cd apps/customer-app && pnpm dev

# Vendor App  
cd apps/vendor-app && pnpm dev

# Runner App
cd apps/runner-app && pnpm dev

# Web Admin Dashboard
cd apps/web-admin && pnpm dev
```

## 🎯 Core Features

### Customer App
- Browse local vendors and services
- Add items to cart with customization
- Apply vouchers (1Voucher, OTT, etc.)
- Multiple payment methods (Stripe, M-Pesa, PayFast)
- Real-time order tracking with GPS
- Push notifications for order updates
- Ratings and reviews

### Vendor App
- Manage digital storefront
- Add/edit menu items and services
- Accept/decline incoming orders
- Update order status
- View earnings dashboard
- Customer communication tools

### Runner App
- Accept delivery/service assignments
- GPS navigation and route optimization
- Update job status in real-time
- Earnings tracking
- Customer communication

### Web Admin Dashboard
- Order management and monitoring
- Vendor and runner onboarding
- Analytics and reporting
- System configuration
- User management

## 🛠️ Technology Stack

| Component | Technology |
|-----------|------------|
| **Mobile Apps** | Expo SDK 53+, React Native, TypeScript |
| **Web Admin** | Next.js 15, shadcn/ui, TailwindCSS v4 |
| **Backend** | Appwrite Cloud Functions, Node.js, TypeScript |
| **Database** | Appwrite Database (NoSQL) |
| **Authentication** | Appwrite Auth (Phone/OTP) |
| **Real-time** | Appwrite Realtime |
| **State Management** | Zustand + React Query |
| **Navigation** | React Navigation v6 (Native) / Next.js App Router |
| **Styling** | NativeBase/Expo (Native) / TailwindCSS (Web) |
| **Maps** | react-native-maps / Google Maps API |
| **Payments** | Stripe, PayFast, M-Pesa, 1Voucher, OTT |
| **Push Notifications** | Expo Push API |
| **Package Manager** | pnpm (workspaces) |

## 📱 App Features Matrix

| Feature | Customer | Vendor | Runner | Web Admin |
|---------|----------|--------|--------|-----------|
| OTP Authentication | ✅ | ✅ | ✅ | ✅ |
| Browse Menu/Services | ✅ | ❌ | ❌ | ✅ |
| Manage Catalog | ❌ | ✅ | ❌ | ✅ |
| Order Management | ✅ | ✅ | ✅ | ✅ |
| Real-time Tracking | ✅ | ✅ (view) | ✅ | ✅ |
| Payment Processing | ✅ | ❌ | ❌ | ✅ |
| Earnings Dashboard | ❌ | ✅ | ✅ | ✅ |
| Push Notifications | ✅ | ✅ | ✅ | ❌ |
| Analytics | ❌ | Basic | Basic | Advanced |

## 🌍 Environment Configuration

### Required Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Core API
NODE_ENV=development
PORT=3001
API_URL=http://localhost:3001

# Database
DATABASE_URL=your_database_connection_string

# Authentication  
JWT_SECRET=your_jwt_secret
FIREBASE_PROJECT_ID=your_firebase_project

# Payments
STRIPE_SECRET_KEY=sk_test_your_stripe_secret
PAYFAST_MERCHANT_ID=your_payfast_merchant_id
PAYFAST_MERCHANT_KEY=your_payfast_merchant_key

# External APIs
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
ONE_VOUCHER_API_KEY=your_1voucher_api_key
EXPO_ACCESS_TOKEN=your_expo_push_token
```

## 🔧 Development Scripts

```bash
# Install dependencies
pnpm install

# Development (all apps)
pnpm dev

# Build all packages
pnpm build

# Type checking
pnpm type-check

# Linting
pnpm lint

# Formatting
pnpm format

# Clean all build artifacts
pnpm clean

# Test
pnpm test
```

## 📊 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/auth/login` | Send OTP to user |
| POST | `/auth/verify` | Verify OTP & issue JWT |
| GET | `/api/vendors` | Get list of vendors |
| GET | `/api/vendors/:id/menu` | Get vendor menu |
| POST | `/api/orders` | Create new order |
| GET | `/api/orders/:id` | Get order details |
| POST | `/api/payments/voucher/validate` | Validate voucher codes |
| POST | `/api/payments/stripe/intent` | Create Stripe payment |
| POST | `/api/payments/payfast/init` | Initialize PayFast payment |
| GET | `/api/runners/:id/assignments` | Get runner jobs |
| POST | `/api/notifications/push` | Send push notification |

## 🎨 Brand Guidelines

### Colors
- **Primary**: `#FFB800` (Energetic Yellow)
- **Secondary**: `#3C3C3C` (Urban Charcoal)
- **Accent**: `#00C2FF` (Digital Blue)
- **Background**: `#FFFFFF` (Clean White)

### Typography
- **Headings**: Poppins Bold
- **Body**: Inter Regular
- **Monospace**: JetBrains Mono

### Logo
Stylized "HVPPYPlug+" wordmark with electric plug icon in the "+" symbol.

## 🚀 Deployment

### Production Build
```bash
# Build all packages
pnpm build

# Build specific apps
pnpm --filter @hvppyplug/backend build
pnpm --filter @hvppyplug/web-admin build
```

### Mobile App Deployment (EAS)
```bash
# Configure EAS
cd apps/customer-app && npx eas build:configure

# Build for production
cd apps/customer-app && npx eas build --platform all
cd apps/vendor-app && npx eas build --platform all  
cd apps/runner-app && npx eas build --platform all

# Submit to stores
npx eas submit --platform ios
npx eas submit --platform android
```

## 🧪 Testing

```bash
# Run all tests
pnpm test

# Run tests in specific package
pnpm --filter @hvppyplug/common test
pnpm --filter @hvppyplug/backend test
```

## 📋 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Standards
- Use TypeScript for all new code
- Follow existing ESLint/Prettier configuration
- Write tests for new features
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Community & Support

- **Issues**: [GitHub Issues](https://github.com/your-org/hvppyplug-monorepo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/hvppyplug-monorepo/discussions)
- **Email**: <EMAIL>

---

> **Ready to Plug In?** 
> Let's ignite Soweto's on-demand scene with HVPPYPlug+—where convenience meets community. 🔌⚡
