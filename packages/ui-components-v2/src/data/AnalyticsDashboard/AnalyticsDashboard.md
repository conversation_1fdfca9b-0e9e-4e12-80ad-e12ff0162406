# AnalyticsDashboard

AnalyticsDashboard component for data functionality.

## Usage

```tsx
import { AnalyticsDashboard } from '@hvppyplug/compound-components'

function Example() {
  return (
    <AnalyticsDashboard>
      Content here
    </AnalyticsDashboard>
  )
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | - | Child components |
| className | string | - | Additional CSS classes |
| disabled | boolean | false | Whether the component is disabled |

## Examples

### Basic Usage

```tsx
<AnalyticsDashboard />
```

### Disabled State

```tsx
<AnalyticsDashboard disabled />
```

### With Custom Styling

```tsx
<AnalyticsDashboard className="custom-styles" />
```
