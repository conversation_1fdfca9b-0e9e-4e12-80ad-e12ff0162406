# 🚀 HVPPYPlug+ Mobile Services Migration

## Quick Start

To migrate your Expo services to the new dedicated package, run:

```bash
# Option 1: Using pnpm script (recommended)
pnpm migrate:mobile-services

# Option 2: Direct script execution
./scripts/run-migration.sh

# Option 3: Manual validation after migration
pnpm validate:migration
```

## What This Migration Does

### 🎯 **Architectural Improvement**
- **Separates** mobile services from UI components
- **Creates** dedicated `@hvppyplug/mobile-services` package
- **Improves** maintainability and reusability
- **Enables** independent versioning

### 📦 **Package Structure**
```
packages/mobile-services/
├── src/
│   ├── services/          # Core services
│   │   ├── LocationService/
│   │   ├── NotificationService/
│   │   ├── CameraService/
│   │   └── AppwriteService/
│   └── hooks/             # React hooks
│       ├── useLocation.ts
│       ├── useNotifications.ts
│       └── useCamera.ts
├── package.json           # Expo dependencies
├── tsconfig.json         # TypeScript config
└── README.md             # Documentation
```

### 🔄 **Import Changes**
```typescript
// Before
import { LocationService } from '@hvppyplug/compound-components'

// After
import { LocationService } from '@hvppyplug/mobile-services'
```

## Migration Scripts

### 1. **Complete Migration** (`run-migration.sh`)
- Creates mobile-services package
- Moves all service files
- Updates import statements
- Installs dependencies
- Builds and validates

### 2. **Import Updater** (`update-imports.js`)
- Scans entire codebase
- Updates import statements
- Handles complex import scenarios
- Updates package.json dependencies

### 3. **Migration Validator** (`validate-migration.js`)
- Validates package structure
- Checks TypeScript configuration
- Verifies exports and imports
- Tests build process

## Benefits

### ✅ **Before Migration Issues**
- Services mixed with UI components
- Platform dependencies in UI library
- Tight coupling
- Difficult independent versioning

### 🎉 **After Migration Benefits**
- Clear separation of concerns
- Isolated platform dependencies
- Loose coupling
- Independent maintenance
- Better tree-shaking
- Cross-project reusability

## Usage After Migration

```typescript
import { 
  LocationService,
  NotificationService,
  CameraService,
  AppwriteService,
  useLocation,
  useNotifications,
  useCamera
} from '@hvppyplug/mobile-services'

// Initialize Appwrite
const appwriteService = AppwriteService.getInstance({
  endpoint: 'https://your-appwrite-endpoint',
  projectId: 'your-project-id',
  databaseId: 'your-database-id',
  storageId: 'your-storage-id',
})

// Use location tracking
const { currentLocation, startTracking } = useLocation({
  enableBackgroundLocation: true,
  enableAppwriteSync: true,
})

// Handle notifications
const { sendOrderNotification } = useNotifications({
  enableAppwriteSync: true,
})

// Camera operations
const { takePhoto, uploadImage } = useCamera({
  enableAppwriteUpload: true,
})
```

## Troubleshooting

### Common Issues
1. **Import errors**: Run `pnpm install` and rebuild
2. **TypeScript errors**: Regenerate declarations with `pnpm build`
3. **Missing dependencies**: Check mobile-services package.json

### Support
- See [Migration Guide](docs/MOBILE_SERVICES_MIGRATION.md)
- Check [Expo Plugins Guide](docs/EXPO_PLUGINS_GUIDE.md)
- Review package README files

---

**Ready to migrate?** Run `pnpm migrate:mobile-services` now! 🚀
