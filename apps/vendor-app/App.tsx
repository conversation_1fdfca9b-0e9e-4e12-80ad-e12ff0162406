import React, { useEffect } from 'react'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { SafeAreaProvider } from 'react-native-safe-area-context'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { ThemeProvider } from './src/providers/ThemeProvider'
import { AppwriteProvider } from './src/providers/AppwriteProvider'
import { ErrorBoundary } from './src/components/ErrorBoundary'
import { RootNavigator } from './src/navigation/RootNavigator'
import { notificationService } from './src/services/notificationService'
import { offlineService } from './src/services/offlineService'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
})

export default function App() {
  useEffect(() => {
    // Initialize services
    const initializeServices = async () => {
      try {
        await Promise.all([
          notificationService.initialize(),
          offlineService.initialize()
        ])
        console.log('✅ Vendor app services initialized successfully')
      } catch (error) {
        console.error('❌ Failed to initialize vendor app services:', error)
      }
    }

    initializeServices()
  }, [])

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <QueryClientProvider client={queryClient}>
            <AppwriteProvider>
              <ThemeProvider>
                <RootNavigator />
              </ThemeProvider>
            </AppwriteProvider>
          </QueryClientProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  )
}
