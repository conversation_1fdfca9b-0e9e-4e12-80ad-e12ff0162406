#!/usr/bin/env node

/**
 * Quick Setup Script for Compound Components Library
 * This script provides a fast way to set up the library with Gluestack UI CLI
 */

const chalk = require('chalk')
const { execSync } = require('child_process')

const log = {
  info: (msg) => console.log(chalk.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(chalk.green(`✓ ${msg}`)),
  warning: (msg) => console.log(chalk.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(chalk.red(`✗ ${msg}`)),
  title: (msg) => console.log(chalk.bold.cyan(`\n🚀 ${msg}\n`)),
}

function runCommand(command) {
  try {
    log.info(`Running: ${command}`)
    execSync(command, { stdio: 'inherit' })
    log.success('✓ Command completed')
  } catch (error) {
    log.error(`Command failed: ${command}`)
    throw error
  }
}

async function quickSetup() {
  log.title('Quick Setup for Compound Components Library')
  
  try {
    // Step 1: Install dependencies
    log.info('Installing dependencies...')
    runCommand('pnpm install')
    
    // Step 2: Run enhanced setup
    log.info('Running enhanced Gluestack UI setup...')
    runCommand('pnpm setup:enhanced')
    
    // Step 3: Build the library
    log.info('Building the library...')
    runCommand('pnpm build')
    
    log.title('🎉 Quick Setup Complete!')
    log.success('Your compound components library is ready!')
    log.info('Available commands:')
    log.info('  pnpm gluestack:add <component>  - Add specific Gluestack UI component')
    log.info('  pnpm gluestack:add-all          - Add all available components')
    log.info('  pnpm generate:component         - Generate new compound component')
    log.info('  pnpm extract:patterns           - Extract patterns from examples')
    log.info('  pnpm build                      - Build the library')
    log.info('  pnpm test                       - Run tests')
    
  } catch (error) {
    log.error('Quick setup failed!')
    log.error(error.message)
    log.info('Try running individual commands manually:')
    log.info('  1. pnpm install')
    log.info('  2. pnpm setup:gluestack')
    log.info('  3. pnpm gluestack:add box text button')
    process.exit(1)
  }
}

if (require.main === module) {
  quickSetup()
}

module.exports = { quickSetup }
