import { create } from 'zustand'

export interface VendorService {
  id: string
  name: string
  description: string
  category: string
  subcategory?: string
  basePrice: number
  priceType: 'fixed' | 'hourly' | 'quote'
  duration: number // in minutes
  isActive: boolean
  availability: {
    days: string[]
    timeSlots: {
      start: string
      end: string
    }[]
  }
  serviceArea: {
    radius: number // in km
    locations: string[]
  }
  requirements: string[]
  images: string[]
  tags: string[]
  addOns: {
    id: string
    name: string
    description: string
    price: number
    required: boolean
  }[]
  bookingSettings: {
    advanceBooking: number // hours
    cancellationPolicy: string
    requiresApproval: boolean
  }
  stats: {
    totalBookings: number
    averageRating: number
    reviewCount: number
    revenue: number
  }
  createdAt: string
  updatedAt: string
}

interface ServicesState {
  // State
  services: VendorService[]
  activeServices: VendorService[]
  inactiveServices: VendorService[]
  isLoading: boolean
  error: string | null

  // Actions
  fetchServices: () => Promise<void>
  addService: (service: Omit<VendorService, 'id' | 'stats' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateService: (serviceId: string, updates: Partial<VendorService>) => Promise<void>
  deleteService: (serviceId: string) => Promise<void>
  toggleServiceStatus: (serviceId: string) => Promise<void>
  duplicateService: (serviceId: string) => Promise<void>
  
  // Utilities
  getServiceById: (serviceId: string) => VendorService | undefined
  getServicesByCategory: (category: string) => VendorService[]
  getServicesStats: () => {
    total: number
    active: number
    inactive: number
    totalRevenue: number
    averageRating: number
  }
  clearError: () => void
}

export const useServicesStore = create<ServicesState>((set, get) => ({
  // Initial state
  services: [],
  activeServices: [],
  inactiveServices: [],
  isLoading: false,
  error: null,

  // Actions
  fetchServices: async () => {
    try {
      set({ isLoading: true, error: null })
      
      // Mock services data
      const mockServices: VendorService[] = [
        {
          id: 'service_1',
          name: 'Plumbing Repair',
          description: 'Professional plumbing repair services including leak fixes, pipe repairs, and drain cleaning.',
          category: 'Home Maintenance',
          subcategory: 'Plumbing',
          basePrice: 350,
          priceType: 'fixed',
          duration: 120,
          isActive: true,
          availability: {
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
            timeSlots: [
              { start: '08:00', end: '17:00' }
            ]
          },
          serviceArea: {
            radius: 15,
            locations: ['Johannesburg', 'Sandton', 'Randburg']
          },
          requirements: ['Access to water supply', 'Clear workspace'],
          images: [],
          tags: ['plumbing', 'repair', 'emergency'],
          addOns: [
            {
              id: 'addon_1',
              name: 'Emergency Service',
              description: 'Same-day emergency service',
              price: 100,
              required: false
            }
          ],
          bookingSettings: {
            advanceBooking: 2,
            cancellationPolicy: '24 hours notice required',
            requiresApproval: false
          },
          stats: {
            totalBookings: 45,
            averageRating: 4.8,
            reviewCount: 38,
            revenue: 15750
          },
          createdAt: '2024-01-15T00:00:00.000Z',
          updatedAt: new Date().toISOString()
        },
        {
          id: 'service_2',
          name: 'Electrical Installation',
          description: 'Safe and professional electrical installation services for homes and offices.',
          category: 'Electrical',
          subcategory: 'Installation',
          basePrice: 450,
          priceType: 'hourly',
          duration: 180,
          isActive: true,
          availability: {
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            timeSlots: [
              { start: '08:00', end: '16:00' }
            ]
          },
          serviceArea: {
            radius: 20,
            locations: ['Johannesburg', 'Pretoria', 'Midrand']
          },
          requirements: ['Power supply access', 'Safety clearance'],
          images: [],
          tags: ['electrical', 'installation', 'certified'],
          addOns: [
            {
              id: 'addon_2',
              name: 'Certificate of Compliance',
              description: 'Official electrical compliance certificate',
              price: 200,
              required: true
            }
          ],
          bookingSettings: {
            advanceBooking: 24,
            cancellationPolicy: '48 hours notice required',
            requiresApproval: true
          },
          stats: {
            totalBookings: 28,
            averageRating: 4.9,
            reviewCount: 25,
            revenue: 12600
          },
          createdAt: '2024-02-01T00:00:00.000Z',
          updatedAt: new Date().toISOString()
        },
        {
          id: 'service_3',
          name: 'Garden Maintenance',
          description: 'Complete garden maintenance including lawn mowing, pruning, and landscaping.',
          category: 'Garden & Outdoor',
          subcategory: 'Maintenance',
          basePrice: 250,
          priceType: 'fixed',
          duration: 240,
          isActive: false,
          availability: {
            days: ['saturday', 'sunday'],
            timeSlots: [
              { start: '07:00', end: '15:00' }
            ]
          },
          serviceArea: {
            radius: 10,
            locations: ['Johannesburg', 'Sandton']
          },
          requirements: ['Garden access', 'Water supply'],
          images: [],
          tags: ['garden', 'maintenance', 'landscaping'],
          addOns: [],
          bookingSettings: {
            advanceBooking: 48,
            cancellationPolicy: '72 hours notice required',
            requiresApproval: false
          },
          stats: {
            totalBookings: 12,
            averageRating: 4.5,
            reviewCount: 10,
            revenue: 3000
          },
          createdAt: '2024-03-01T00:00:00.000Z',
          updatedAt: new Date().toISOString()
        }
      ]

      const activeServices = mockServices.filter(service => service.isActive)
      const inactiveServices = mockServices.filter(service => !service.isActive)

      set({ 
        services: mockServices,
        activeServices,
        inactiveServices,
        isLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to fetch services:', error)
      set({ 
        error: error.message || 'Failed to fetch services', 
        isLoading: false 
      })
    }
  },

  addService: async (serviceData) => {
    try {
      set({ isLoading: true, error: null })
      
      const newService: VendorService = {
        ...serviceData,
        id: `service_${Date.now()}`,
        stats: {
          totalBookings: 0,
          averageRating: 0,
          reviewCount: 0,
          revenue: 0
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const { services } = get()
      const updatedServices = [...services, newService]
      const activeServices = updatedServices.filter(service => service.isActive)
      const inactiveServices = updatedServices.filter(service => !service.isActive)

      set({ 
        services: updatedServices,
        activeServices,
        inactiveServices,
        isLoading: false 
      })
    } catch (error: any) {
      console.error('Failed to add service:', error)
      set({ 
        error: error.message || 'Failed to add service', 
        isLoading: false 
      })
      throw error
    }
  },

  updateService: async (serviceId: string, updates: Partial<VendorService>) => {
    try {
      const { services } = get()
      const updatedServices = services.map(service => 
        service.id === serviceId 
          ? { ...service, ...updates, updatedAt: new Date().toISOString() }
          : service
      )

      const activeServices = updatedServices.filter(service => service.isActive)
      const inactiveServices = updatedServices.filter(service => !service.isActive)

      set({ 
        services: updatedServices,
        activeServices,
        inactiveServices
      })
    } catch (error: any) {
      console.error('Failed to update service:', error)
      set({ error: error.message || 'Failed to update service' })
      throw error
    }
  },

  deleteService: async (serviceId: string) => {
    try {
      const { services } = get()
      const updatedServices = services.filter(service => service.id !== serviceId)
      const activeServices = updatedServices.filter(service => service.isActive)
      const inactiveServices = updatedServices.filter(service => !service.isActive)

      set({ 
        services: updatedServices,
        activeServices,
        inactiveServices
      })
    } catch (error: any) {
      console.error('Failed to delete service:', error)
      set({ error: error.message || 'Failed to delete service' })
      throw error
    }
  },

  toggleServiceStatus: async (serviceId: string) => {
    try {
      const { services } = get()
      const service = services.find(s => s.id === serviceId)
      if (!service) return

      await get().updateService(serviceId, { isActive: !service.isActive })
    } catch (error: any) {
      console.error('Failed to toggle service status:', error)
      throw error
    }
  },

  duplicateService: async (serviceId: string) => {
    try {
      const { services } = get()
      const service = services.find(s => s.id === serviceId)
      if (!service) return

      const duplicatedService = {
        ...service,
        name: `${service.name} (Copy)`,
        isActive: false
      }

      // Remove id, stats, and timestamps to create new service
      const { id, stats, createdAt, updatedAt, ...serviceData } = duplicatedService
      await get().addService(serviceData)
    } catch (error: any) {
      console.error('Failed to duplicate service:', error)
      throw error
    }
  },

  // Utilities
  getServiceById: (serviceId: string) => {
    const { services } = get()
    return services.find(service => service.id === serviceId)
  },

  getServicesByCategory: (category: string) => {
    const { services } = get()
    return services.filter(service => service.category === category)
  },

  getServicesStats: () => {
    const { services } = get()
    return {
      total: services.length,
      active: services.filter(s => s.isActive).length,
      inactive: services.filter(s => !s.isActive).length,
      totalRevenue: services.reduce((sum, s) => sum + s.stats.revenue, 0),
      averageRating: services.length > 0 
        ? services.reduce((sum, s) => sum + s.stats.averageRating, 0) / services.length 
        : 0
    }
  },

  clearError: () => set({ error: null }),
}))
