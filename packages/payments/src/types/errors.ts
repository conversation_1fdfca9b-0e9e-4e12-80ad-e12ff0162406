// Payment Error Types and Error Handling

export type PaymentErrorCode =
  // Generic errors
  | 'UNKNOWN_ERROR'
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'CONFIGURATION_ERROR'

  // Validation errors
  | 'INVALID_AMOUNT'
  | 'INVALID_CURRENCY'
  | 'INVALID_PAYMENT_METHOD'
  | 'INVALID_CARD_NUMBER'
  | 'INVALID_CARD_EXPIRY'
  | 'INVALID_CARD_CVC'
  | 'INVALID_PHONE_NUMBER'
  | 'INVALID_VOUCHER_CODE'
  | 'MISSING_REQUIRED_FIELD'

  // Payment processing errors
  | 'PAYMENT_DECLINED'
  | 'INSUFFICIENT_FUNDS'
  | 'CARD_EXPIRED'
  | 'CARD_BLOCKED'
  | 'PAYMENT_CANCELLED'
  | 'PAYMENT_TIMEOUT'
  | 'PAYMENT_ALREADY_PROCESSED'
  | 'PAYMENT_NOT_FOUND'
  | 'PAYMENT_INTENT_EXPIRED'

  // Provider-specific errors
  | 'POLAR_ERROR'
  | 'PAYFAST_ERROR'
  | 'MPESA_ERROR'
  | 'VOUCHER_ERROR'

  // Security errors
  | 'AUTHENTICATION_FAILED'
  | 'AUTHORIZATION_FAILED'
  | 'SIGNATURE_VERIFICATION_FAILED'
  | 'WEBHOOK_VERIFICATION_FAILED'
  | 'ENCRYPTION_ERROR'
  | 'DECRYPTION_ERROR'

  // Business logic errors
  | 'AMOUNT_TOO_SMALL'
  | 'AMOUNT_TOO_LARGE'
  | 'CURRENCY_NOT_SUPPORTED'
  | 'PAYMENT_METHOD_NOT_SUPPORTED'
  | 'MERCHANT_NOT_CONFIGURED'
  | 'REFUND_NOT_ALLOWED'
  | 'REFUND_AMOUNT_EXCEEDS_ORIGINAL'

  // Voucher-specific errors
  | 'VOUCHER_EXPIRED'
  | 'VOUCHER_ALREADY_USED'
  | 'VOUCHER_INSUFFICIENT_BALANCE'
  | 'VOUCHER_NOT_FOUND'
  | 'VOUCHER_INVALID_TYPE'

  // M-Pesa specific errors
  | 'MPESA_INVALID_PHONE'
  | 'MPESA_TRANSACTION_FAILED'
  | 'MPESA_USER_CANCELLED'
  | 'MPESA_INSUFFICIENT_BALANCE'
  | 'MPESA_TIMEOUT'

  // Additional validation errors
  | 'INVALID_CUSTOMER_ID'
  | 'INVALID_ORDER_ID'
  | 'INVALID_URL'
  | 'INVALID_DESCRIPTION'
  | 'INVALID_EMAIL'
  | 'NAME_TOO_LONG'
  | 'INVALID_COUNTRY_CODE'
  | 'METADATA_TOO_MANY_KEYS'
  | 'METADATA_KEY_TOO_LONG'
  | 'METADATA_VALUE_TOO_LONG'
  | 'METADATA_RESERVED_KEY';

export type PaymentErrorType = 
  | 'validation'
  | 'network'
  | 'security'
  | 'business'
  | 'provider'
  | 'system';

export interface PaymentError extends Error {
  code: PaymentErrorCode;
  type: PaymentErrorType;
  message: string;
  details?: Record<string, any>;
  originalError?: Error;
  timestamp: Date;
  paymentIntentId?: string;
  transactionId?: string;
  provider?: string;
  retryable: boolean;
  userMessage?: string; // User-friendly message for display
}

export interface ValidationError extends PaymentError {
  type: 'validation';
  field?: string;
  value?: any;
  constraints?: string[];
}

export interface NetworkError extends PaymentError {
  type: 'network';
  statusCode?: number;
  response?: any;
  timeout?: boolean;
}

export interface SecurityError extends PaymentError {
  type: 'security';
  securityLevel: 'low' | 'medium' | 'high' | 'critical';
  action?: 'block' | 'warn' | 'log';
}

export interface BusinessError extends PaymentError {
  type: 'business';
  businessRule: string;
  allowedValues?: any[];
  currentValue?: any;
}

export interface ProviderError extends PaymentError {
  type: 'provider';
  providerCode?: string;
  providerMessage?: string;
  providerResponse?: any;
}

// Error Factory Functions
export class PaymentErrorFactory {
  static createValidationError(
    code: PaymentErrorCode,
    message: string,
    field?: string,
    value?: any
  ): ValidationError {
    return {
      name: 'ValidationError',
      code,
      type: 'validation',
      message,
      field: field || '',
      value,
      timestamp: new Date(),
      retryable: false
    };
  }

  static createNetworkError(
    code: PaymentErrorCode,
    message: string,
    statusCode?: number,
    response?: any
  ): NetworkError {
    return {
      name: 'NetworkError',
      code,
      type: 'network',
      message,
      statusCode: statusCode || 0,
      response,
      timestamp: new Date(),
      retryable: true
    };
  }

  static createSecurityError(
    code: PaymentErrorCode,
    message: string,
    securityLevel: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): SecurityError {
    return {
      name: 'SecurityError',
      code,
      type: 'security',
      message,
      securityLevel,
      timestamp: new Date(),
      retryable: false
    };
  }

  static createBusinessError(
    code: PaymentErrorCode,
    message: string,
    businessRule: string,
    currentValue?: any,
    allowedValues?: any[]
  ): BusinessError {
    return {
      name: 'BusinessError',
      code,
      type: 'business',
      message,
      businessRule,
      currentValue,
      allowedValues: allowedValues || [],
      timestamp: new Date(),
      retryable: false
    };
  }

  static createProviderError(
    code: PaymentErrorCode,
    message: string,
    provider: string,
    providerCode?: string,
    providerMessage?: string,
    providerResponse?: any
  ): ProviderError {
    return {
      name: 'ProviderError',
      code,
      type: 'provider',
      message,
      provider,
      providerCode: providerCode || '',
      providerMessage: providerMessage || '',
      providerResponse,
      timestamp: new Date(),
      retryable: false
    };
  }
}

// Error Handler Interface
export interface ErrorHandler {
  handle(error: PaymentError): Promise<void>;
  shouldRetry(error: PaymentError): boolean;
  getUserMessage(error: PaymentError): string;
  logError(error: PaymentError): void;
}

// Error Recovery Strategy
export interface ErrorRecoveryStrategy {
  canRecover(error: PaymentError): boolean;
  recover(error: PaymentError): Promise<any>;
  getRecoveryOptions(error: PaymentError): string[];
}

// Error Reporting
export interface ErrorReport {
  errorId: string;
  error: PaymentError;
  context: {
    userId?: string;
    paymentIntentId?: string;
    provider?: string;
    userAgent?: string;
    timestamp: Date;
  };
  stackTrace?: string;
  additionalData?: Record<string, any>;
}
