import React, { useState, useEffect } from 'react'
import { View, Text, TouchableOpacity, Alert, ScrollView } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { ArrowLeftIcon, BellIcon, CheckIcon, TrashIcon } from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { notificationService } from '../../services/notificationService'
import { LoadingSpinner } from '../../components/LoadingSpinner'

// Import UI components from the library
// import { NotificationCenter } from '@hvppyplug/ui-components-v2'

// Temporary placeholder component
const NotificationCenter = ({ notifications, onMarkAsRead, onMarkAllAsRead }: any) => {
  return (
    <View className="flex-1 bg-white">
      <Text className="text-center p-4">Notification Center Placeholder</Text>
    </View>
  )
}

import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Notifications'>

export function NotificationsScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const [notifications, setNotifications] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadNotifications()
    setupNotificationListeners()
  }, [])

  const loadNotifications = async () => {
    try {
      setIsLoading(true)
      
      // Get delivered notifications
      const deliveredNotifications = await notificationService.getDeliveredNotifications()
      
      // Mock additional notifications for demo
      const mockNotifications = [
        {
          id: '1',
          title: 'Order Confirmed',
          body: 'Your plumbing service has been confirmed for tomorrow at 2 PM',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          type: 'order_update',
          read: false,
          data: { orderId: 'order_123' }
        },
        {
          id: '2',
          title: 'New Message',
          body: 'John\'s Home Services: I\'ll be there in 15 minutes',
          timestamp: new Date(Date.now() - 1800000).toISOString(),
          type: 'message',
          read: false,
          data: { chatId: 'chat_456' }
        },
        {
          id: '3',
          title: 'Service Available',
          body: 'Electrical services are now available in your area',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          type: 'service_available',
          read: true,
          data: { serviceId: 'service_789' }
        },
        {
          id: '4',
          title: 'Order Completed',
          body: 'Your cleaning service has been completed. Please rate your experience.',
          timestamp: new Date(Date.now() - 86400000).toISOString(),
          type: 'order_update',
          read: true,
          data: { orderId: 'order_456' }
        }
      ]

      // Combine delivered and mock notifications
      const allNotifications = [
        ...deliveredNotifications.map(n => ({
          id: n.request.identifier,
          title: n.request.content.title || 'Notification',
          body: n.request.content.body || '',
          timestamp: new Date(n.date).toISOString(),
          type: n.request.content.data?.type || 'general',
          read: false,
          data: n.request.content.data || {}
        })),
        ...mockNotifications
      ]

      // Sort by timestamp (newest first)
      allNotifications.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

      setNotifications(allNotifications)
    } catch (error) {
      console.error('Failed to load notifications:', error)
      Alert.alert('Error', 'Failed to load notifications')
    } finally {
      setIsLoading(false)
    }
  }

  const setupNotificationListeners = () => {
    // Listen for new notifications while app is open
    const subscription = notificationService.addNotificationReceivedListener((notification) => {
      const newNotification = {
        id: Date.now().toString(),
        title: notification.request.content.title || 'New Notification',
        body: notification.request.content.body || '',
        timestamp: new Date().toISOString(),
        type: notification.request.content.data?.type || 'general',
        read: false,
        data: notification.request.content.data || {}
      }

      setNotifications(prev => [newNotification, ...prev])
    })

    return () => subscription.remove()
  }

  const handleNotificationPress = (notification: any) => {
    // Mark as read
    setNotifications(prev => 
      prev.map(n => n.id === notification.id ? { ...n, read: true } : n)
    )

    // Navigate based on notification type
    switch (notification.type) {
      case 'order_update':
        navigation.navigate('OrderTracking', { orderId: notification.data.orderId })
        break
      case 'message':
        navigation.navigate('Chat', { 
          orderId: 'temp',
          providerId: notification.data.providerId || 'provider_123'
        })
        break
      case 'service_available':
        navigation.navigate('ServiceDetail', { serviceId: notification.data.serviceId })
        break
      default:
        break
    }
  }

  const handleMarkAllRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })))
  }

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            setNotifications([])
            notificationService.dismissAllNotifications()
          }
        }
      ]
    )
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <ArrowLeftIcon size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, fontWeight: '600', marginLeft: 16 }
          ]}>
            Notifications
          </Text>
        </View>

        <View style={{ flexDirection: 'row', gap: 12 }}>
          <TouchableOpacity
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 20,
              padding: 8,
            }}
            onPress={handleMarkAllRead}
          >
            <CheckIcon size={20} color={colors.text.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.secondary,
              borderRadius: 20,
              padding: 8,
            }}
            onPress={handleClearAll}
          >
            <TrashIcon size={20} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Notification count */}
      <View style={{ marginTop: 12 }}>
        <Text style={[
          typography.body.small,
          { color: colors.text.secondary }
        ]}>
          {notifications.filter(n => !n.read).length} unread notifications
        </Text>
      </View>
    </View>
  )

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <LoadingSpinner message="Loading notifications..." />
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      {renderHeader()}
      
      <NotificationCenter
        data={notifications}
        loading={false}
        onAction={(action, data) => {
          if (action === 'notification_press') {
            handleNotificationPress(data)
          }
        }}
        variant="default"
      />
    </SafeAreaView>
  )
}
