import type { theme } from './index'

export type Theme = typeof theme

export type ColorPalette = {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string
  600: string
  700: string
  800: string
  900: string
}

export type ThemeColors = {
  primary: ColorPalette
  secondary: ColorPalette
  success: ColorPalette
  warning: ColorPalette
  error: ColorPalette
  info: ColorPalette
  gray: ColorPalette
  
  light: {
    background: {
      primary: string
      secondary: string
      tertiary: string
    }
    text: {
      primary: string
      secondary: string
      tertiary: string
      inverse: string
      disabled: string
    }
    border: {
      primary: string
      secondary: string
      focus: string
      error: string
    }
    shadow: {
      light: string
      medium: string
      heavy: string
    }
  }
  
  dark: {
    background: {
      primary: string
      secondary: string
      tertiary: string
    }
    text: {
      primary: string
      secondary: string
      tertiary: string
      inverse: string
      disabled: string
    }
    border: {
      primary: string
      secondary: string
      focus: string
      error: string
    }
    shadow: {
      light: string
      medium: string
      heavy: string
    }
  }
  
  // Legacy support
  background: {
    primary: string
    secondary: string
    tertiary: string
  }
  text: {
    primary: string
    secondary: string
    tertiary: string
    inverse: string
    disabled: string
  }
  border: {
    primary: string
    secondary: string
    focus: string
    error: string
  }
  shadow: {
    light: string
    medium: string
    heavy: string
  }
  
  white: string
  black: string
  transparent: string
}

export type TypographyVariant = {
  fontSize: number
  lineHeight: number
  fontWeight: '400' | '500' | '600' | '700'
  letterSpacing: number
  textTransform?: 'uppercase' | 'lowercase' | 'capitalize' | 'none'
}

export type ThemeTypography = {
  display: {
    large: TypographyVariant
    medium: TypographyVariant
    small: TypographyVariant
  }
  headline: {
    large: TypographyVariant
    medium: TypographyVariant
    small: TypographyVariant
  }
  title: {
    large: TypographyVariant
    medium: TypographyVariant
    small: TypographyVariant
  }
  body: {
    large: TypographyVariant
    medium: TypographyVariant
    small: TypographyVariant
  }
  label: {
    large: TypographyVariant
    medium: TypographyVariant
    small: TypographyVariant
  }
  caption: {
    large: TypographyVariant
    medium: TypographyVariant
    small: TypographyVariant
  }
  overline: TypographyVariant
}

export type ThemeSpacing = {
  xs: number
  sm: number
  md: number
  lg: number
  xl: number
  '2xl': number
  '3xl': number
  '4xl': number
}

export type ThemeBorderRadius = {
  none: number
  sm: number
  md: number
  lg: number
  xl: number
  '2xl': number
  full: number
}

export type ThemeShadow = {
  shadowColor: string
  shadowOffset: { width: number; height: number }
  shadowOpacity: number
  shadowRadius: number
  elevation: number
}

export type ThemeShadows = {
  sm: ThemeShadow
  md: ThemeShadow
  lg: ThemeShadow
  xl: ThemeShadow
}

export type ThemeLayout = {
  headerHeight: number
  tabBarHeight: number
  screenPadding: number
  cardPadding: number
  sectionSpacing: number
  breakpoints: {
    sm: number
    md: number
    lg: number
    xl: number
  }
}
