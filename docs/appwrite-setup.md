# Appwrite Setup Guide for HVPPYPlug+

This guide will walk you through setting up Appwrite as the backend for HVPPYPlug+.

## 🚀 Quick Start

### Option 1: Appwrite Cloud (Recommended)
1. Visit [cloud.appwrite.io](https://cloud.appwrite.io)
2. Create an account and new project
3. Copy your Project ID and API endpoint

### Option 2: Self-Hosted Appwrite
```bash
# Install Appwrite with Docker
docker run -it --rm \
    --volume /var/run/docker.sock:/var/run/docker.sock \
    --volume "$(pwd)"/appwrite:/usr/src/code/appwrite:rw \
    --entrypoint="install" \
    appwrite/appwrite:1.4.0

# Start Appwrite
cd appwrite
docker compose up -d
```

## 📋 Project Configuration

### 1. Install Appwrite CLI
```bash
npm install -g appwrite-cli

# Login to your Appwrite instance
appwrite login

# Initialize project
appwrite init project
```

### 2. Deploy Database Schema
```bash
cd backend
appwrite deploy --force
```

This will create:
- **Database**: `hvppyplug-main`
- **Collections**: users, vendors, menu-items, orders, otp-codes
- **Storage Bucket**: images
- **Functions**: hvppy-api

### 3. Configure Authentication

#### Phone Authentication Setup
1. Go to **Auth** → **Settings** in Appwrite Console
2. Enable **Phone** authentication
3. Configure SMS provider (Twilio, MSG91, etc.)
4. Add your SMS credentials

#### Security Settings
```json
{
  "authDuration": 31536000,
  "authLimit": 100,
  "passwordHistory": 0,
  "passwordDictionary": false,
  "personalDataCheck": true
}
```

### 4. Set Environment Variables

Update your `.env` file:
```bash
# Appwrite Configuration
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your_project_id_here
APPWRITE_API_KEY=your_api_key_here

# Public variables for frontend
EXPO_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
EXPO_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
```

## 🗄️ Database Collections

### Users Collection
```typescript
{
  id: "users",
  name: "Users",
  permissions: ["read(\"any\")", "create(\"any\")", "update(\"any\")"],
  attributes: [
    { key: "name", type: "string", size: 255, required: true },
    { key: "phone", type: "string", size: 20, required: true },
    { key: "role", type: "string", size: 20, required: true },
    { key: "avatarUrl", type: "string", size: 2048, required: false },
    { key: "pushToken", type: "string", size: 255, required: false },
    { key: "status", type: "string", size: 50, required: false },
    { key: "available", type: "boolean", required: false }
  ],
  indexes: [
    { key: "phone_index", type: "unique", attributes: ["phone"] },
    { key: "role_index", type: "key", attributes: ["role"] }
  ]
}
```

### Vendors Collection
```typescript
{
  id: "vendors",
  name: "Vendors", 
  permissions: ["read(\"any\")", "create(\"any\")", "update(\"any\")"],
  attributes: [
    { key: "name", type: "string", size: 255, required: true },
    { key: "description", type: "string", size: 1000, required: false },
    { key: "ownerId", type: "string", size: 255, required: true },
    { key: "location", type: "string", size: 1000, required: true },
    { key: "coordinates", type: "string", size: 100, required: true },
    { key: "status", type: "string", size: 20, required: true },
    { key: "categories", type: "string", size: 1000, required: false, array: true },
    { key: "imageUrl", type: "string", size: 2048, required: false },
    { key: "rating", type: "double", required: false },
    { key: "deliveryFee", type: "double", required: false }
  ]
}
```

### Menu Items Collection
```typescript
{
  id: "menu-items",
  name: "Menu Items",
  permissions: ["read(\"any\")", "create(\"any\")", "update(\"any\")"],
  attributes: [
    { key: "vendorId", type: "string", size: 255, required: true },
    { key: "name", type: "string", size: 255, required: true },
    { key: "description", type: "string", size: 1000, required: false },
    { key: "price", type: "double", required: true },
    { key: "imageUrl", type: "string", size: 2048, required: false },
    { key: "category", type: "string", size: 50, required: true },
    { key: "available", type: "boolean", required: true },
    { key: "preparationTime", type: "integer", required: false },
    { key: "customizations", type: "string", size: 2000, required: false }
  ]
}
```

### Orders Collection
```typescript
{
  id: "orders",
  name: "Orders",
  permissions: ["read(\"any\")", "create(\"any\")", "update(\"any\")"],
  attributes: [
    { key: "customerId", type: "string", size: 255, required: true },
    { key: "vendorId", type: "string", size: 255, required: true },
    { key: "runnerId", type: "string", size: 255, required: false },
    { key: "items", type: "string", size: 10000, required: true },
    { key: "subtotal", type: "double", required: true },
    { key: "discount", type: "double", required: false },
    { key: "deliveryFee", type: "double", required: false },
    { key: "total", type: "double", required: true },
    { key: "status", type: "string", size: 50, required: true },
    { key: "paymentMethod", type: "string", size: 50, required: true },
    { key: "paymentStatus", type: "string", size: 50, required: true },
    { key: "deliveryAddress", type: "string", size: 1000, required: true },
    { key: "notes", type: "string", size: 500, required: false },
    { key: "estimatedDelivery", type: "datetime", required: false }
  ]
}
```

## 🔐 Security Rules

### User Permissions
```javascript
// Users can read/update their own data
[
  "read(\"user:self\")",
  "update(\"user:self\")",
  "create(\"any\")" // Allow registration
]
```

### Vendor Permissions  
```javascript
// Vendors can manage their own data
[
  "read(\"any\")", // Public read
  "create(\"role:vendor\")",
  "update(\"role:vendor\")",
  "delete(\"role:vendor\")"
]
```

### Order Permissions
```javascript
// Complex permissions based on user role
[
  "read(\"role:customer\")", // Customers can read orders
  "read(\"role:vendor\")",   // Vendors can read orders
  "read(\"role:runner\")",   // Runners can read orders
  "create(\"role:customer\")", // Only customers create orders
  "update(\"role:vendor\")",   // Vendors can update status
  "update(\"role:runner\")"    // Runners can update status  
]
```

## 🔧 Functions Deployment

### Deploy the API Function
```bash
cd backend

# Deploy function
appwrite functions createDeployment \
  --functionId=hvppy-api \
  --code=./src \
  --activate=true

# Check logs
appwrite functions listExecutions --functionId=hvppy-api
```

### Function Environment Variables
```bash
appwrite functions updateVariable \
  --functionId=hvppy-api \
  --key="STRIPE_SECRET_KEY" \
  --value="sk_test_your_key_here"

appwrite functions updateVariable \
  --functionId=hvppy-api \
  --key="PAYFAST_MERCHANT_ID" \
  --value="your_merchant_id"
```

## 📱 Client Integration

### Initialize in React Native
```typescript
// packages/common/src/services/appwrite.ts
import { Client, Account, Databases } from 'appwrite';

const client = new Client()
  .setEndpoint(process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT!)
  .setProject(process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID!);

export const account = new Account(client);
export const databases = new Databases(client);
```

### Usage Example
```typescript
// Login with phone
const session = await account.createPhoneSession(
  'unique_user_id',
  '+***********'
);

// Verify OTP
await account.updatePhoneSession(
  session.userId,
  '123456' // OTP code
);

// Create order
const order = await databases.createDocument(
  'hvppyplug-main',
  'orders',
  'unique()',
  {
    customerId: user.$id,
    vendorId: 'vendor123',
    total: 89.50,
    status: 'pending'
  }
);
```

## 🔄 Real-time Features

### Subscribe to Order Updates
```typescript
import { client } from '@hvppyplug/common';

// Subscribe to order changes
client.subscribe('databases.hvppyplug-main.collections.orders.documents', response => {
  console.log('Order updated:', response.payload);
  // Update UI with new order status
});
```

## 🛠️ Development Workflow

1. **Local Development**
   ```bash
   # Start local Appwrite (if self-hosted)
   cd appwrite && docker compose up -d
   
   # Deploy schema changes
   cd backend && appwrite deploy
   
   # Start apps
   pnpm dev
   ```

2. **Testing Functions**
   ```bash
   # Execute function directly
   appwrite functions createExecution \
     --functionId=hvppy-api \
     --data='{"path":"/vendors","method":"GET"}'
   ```

3. **Database Queries**
   ```bash
   # List documents
   appwrite databases listDocuments \
     --databaseId=hvppyplug-main \
     --collectionId=vendors
   ```

## 🚀 Production Deployment

### Environment Setup
```bash
# Production environment variables
APPWRITE_ENDPOINT=https://your-production-domain.com/v1
APPWRITE_PROJECT_ID=production_project_id
APPWRITE_API_KEY=production_api_key

# Deploy to production
appwrite deploy --env=production
```

### Monitoring & Logs
- Monitor function executions in Appwrite Console
- Set up error tracking with Sentry
- Configure log aggregation for production debugging

## 📚 Resources

- [Appwrite Documentation](https://appwrite.io/docs)
- [React Native SDK](https://appwrite.io/docs/client/react-native)
- [Functions Documentation](https://appwrite.io/docs/functions)
- [Database Queries](https://appwrite.io/docs/databases)

---

With this setup, your HVPPYPlug+ backend will be fully powered by Appwrite, providing authentication, database, storage, and serverless functions all in one platform! 🚀
