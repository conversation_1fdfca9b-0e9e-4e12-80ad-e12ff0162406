{"name": "@hvppyplug/web-admin", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "clean": "rm -rf .next", "type-check": "tsc --noEmit", "lint": "next lint"}, "dependencies": {"@hvppyplug/common": "workspace:*", "react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.3", "@tanstack/react-query": "^5.28.4", "zustand": "^4.5.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.2", "lucide-react": "^0.363.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.3", "@eslint/eslintrc": "^3"}}