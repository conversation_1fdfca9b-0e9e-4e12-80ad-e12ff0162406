{"expo": {"name": "HVPPYPlug+ Customer", "slug": "hvppyplug-customer", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow HVPPYPlug+ to use your location to find nearby vendors and track deliveries.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true, "isAndroidForegroundServiceEnabled": true}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#FF6B35", "defaultChannel": "orders", "sounds": ["./assets/sounds/order-notification.wav"], "enableBackgroundRemoteNotifications": true}], ["expo-camera", {"cameraPermission": "Allow HVPPYPlug+ to access your camera to take photos for orders and profile.", "microphonePermission": "Allow HVPPYPlug+ to access your microphone for video messages.", "recordAudioAndroid": true}], "expo-secure-store", "expo-local-authentication", "expo-task-manager", "expo-background-fetch", ["@react-native-async-storage/async-storage", {"AsyncStorageExcludeFromBackup": false}], ["@react-native-community/netinfo", {"shouldFetchWiFiSSID": true}], "expo-dev-client"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.hvppyplug.customer", "infoPlist": {"UIBackgroundModes": ["location", "remote-notification", "background-fetch"], "NSLocationAlwaysAndWhenInUseUsageDescription": "Allow HVPPYPlug+ to use your location to find nearby vendors and track deliveries.", "NSLocationWhenInUseUsageDescription": "Allow HVPPYPlug+ to use your location to find nearby vendors.", "NSCameraUsageDescription": "Allow HVPPYPlug+ to access your camera to take photos for orders and profile.", "NSMicrophoneUsageDescription": "Allow HVPPYPlug+ to access your microphone for video messages.", "NSPhotoLibraryUsageDescription": "Allow HVPPYPlug+ to access your photo library to select images for orders and profile.", "NSFaceIDUsageDescription": "Use Face ID to securely access your account."}}, "android": {"package": "com.hvppyplug.customer", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "USE_FINGERPRINT", "USE_BIOMETRIC", "FOREGROUND_SERVICE", "FOREGROUND_SERVICE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png"}, "scheme": "hvppyplug-customer", "extra": {"appwriteEndpoint": "https://cloud.appwrite.io/v1", "appwriteProjectId": "your-appwrite-project-id", "appwriteDatabaseId": "hvppyplug-main", "appwriteStorageId": "images"}, "runtimeVersion": {"policy": "appVersion"}}}