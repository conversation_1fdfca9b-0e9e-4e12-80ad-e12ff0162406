#!/usr/bin/env node

/**
 * Final Extraction Script
 * Extracts remaining valuable components and patterns from ui-example-nativewind
 * before removing the directory
 */

const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')

const ROOT_DIR = path.resolve(__dirname, '..')
const EXAMPLE_DIR = path.resolve(__dirname, '../../../examples-and-refs/ui-example-nativewind')
const SRC_DIR = path.join(ROOT_DIR, 'src')

const log = {
  info: (msg) => console.log(chalk.blue(`ℹ ${msg}`)),
  success: (msg) => console.log(chalk.green(`✓ ${msg}`)),
  warning: (msg) => console.log(chalk.yellow(`⚠ ${msg}`)),
  error: (msg) => console.log(chalk.red(`✗ ${msg}`)),
  title: (msg) => console.log(chalk.bold.cyan(`\n🚀 ${msg}\n`)),
}

// Essential UI components to extract
const ESSENTIAL_UI_COMPONENTS = [
  'image',
  'skeleton', 
  'table',
  'avatar',
  'badge',
  'card',
  'accordion',
  'alert',
  'modal',
  'toast',
  'popover',
  'drawer',
  'fab',
  'progress',
  'slider',
  'switch',
  'select',
  'linear-gradient',
  'grid'
]

// Configuration files to copy
const CONFIG_FILES = [
  {
    source: 'metro.config.js',
    target: 'config/metro.config.example.js',
    description: 'Metro configuration with NativeWind'
  },
  {
    source: 'babel.config.js', 
    target: 'config/babel.config.example.js',
    description: 'Babel configuration'
  },
  {
    source: 'eas.json',
    target: 'config/eas.example.json',
    description: 'EAS build configuration'
  },
  {
    source: 'app.json',
    target: 'config/app.example.json',
    description: 'Expo app configuration'
  }
]

// Advanced patterns to extract
const ADVANCED_PATTERNS = [
  {
    source: 'kitchensink-components/Header.tsx',
    target: 'examples/patterns/HeaderPattern.tsx',
    description: 'Advanced header with responsive design'
  },
  {
    source: 'kitchensink-components/Sidebar.tsx',
    target: 'examples/patterns/SidebarPattern.tsx', 
    description: 'Responsive sidebar with filters'
  },
  {
    source: 'kitchensink-components/MobileBottomTabs.tsx',
    target: 'examples/patterns/BottomTabsPattern.tsx',
    description: 'Mobile bottom navigation tabs'
  },
  {
    source: 'kitchensink-components/main-content',
    target: 'examples/patterns/MainContentPatterns',
    description: 'Main content layout patterns'
  }
]

// Extract UI component
async function extractUIComponent(componentName) {
  try {
    log.info(`Extracting ${componentName} component...`)
    
    const sourcePath = path.join(EXAMPLE_DIR, 'components/ui', componentName)
    const targetPath = path.join(SRC_DIR, 'gluestack-components/ui', componentName)
    
    if (!await fs.pathExists(sourcePath)) {
      log.warning(`Component ${componentName} not found`)
      return false
    }
    
    // Copy entire component directory
    await fs.copy(sourcePath, targetPath)
    
    // Add header comment to main file
    const mainFile = path.join(targetPath, 'index.tsx')
    if (await fs.pathExists(mainFile)) {
      let content = await fs.readFile(mainFile, 'utf8')
      const header = `/**
 * ${componentName.charAt(0).toUpperCase() + componentName.slice(1)} Component
 * Extracted from ui-example-nativewind
 * Enhanced for @hvppyplug/compound-components
 */

`
      content = header + content
      await fs.writeFile(mainFile, content)
    }
    
    log.success(`✓ Extracted ${componentName}`)
    return true
    
  } catch (error) {
    log.error(`Failed to extract ${componentName}: ${error.message}`)
    return false
  }
}

// Extract configuration file
async function extractConfigFile(config) {
  try {
    log.info(`Extracting ${config.description}...`)
    
    const sourcePath = path.join(EXAMPLE_DIR, config.source)
    const targetPath = path.join(ROOT_DIR, config.target)
    
    if (!await fs.pathExists(sourcePath)) {
      log.warning(`Config file ${config.source} not found`)
      return false
    }
    
    // Ensure target directory exists
    await fs.ensureDir(path.dirname(targetPath))
    
    // Copy file with header comment
    let content = await fs.readFile(sourcePath, 'utf8')
    const header = `/**
 * ${config.description}
 * Extracted from ui-example-nativewind
 * 
 * This is an example configuration - adapt for your needs
 */

`
    
    if (config.source.endsWith('.js')) {
      content = header + content
    } else {
      // For JSON files, add comment differently
      const parsed = JSON.parse(content)
      parsed._comment = config.description + ' - Extracted from ui-example-nativewind'
      content = JSON.stringify(parsed, null, 2)
    }
    
    await fs.writeFile(targetPath, content)
    
    log.success(`✓ Extracted ${config.description}`)
    return true
    
  } catch (error) {
    log.error(`Failed to extract ${config.source}: ${error.message}`)
    return false
  }
}

// Extract advanced pattern
async function extractAdvancedPattern(pattern) {
  try {
    log.info(`Extracting ${pattern.description}...`)
    
    const sourcePath = path.join(EXAMPLE_DIR, pattern.source)
    const targetPath = path.join(SRC_DIR, pattern.target)
    
    if (!await fs.pathExists(sourcePath)) {
      log.warning(`Pattern ${pattern.source} not found`)
      return false
    }
    
    // Ensure target directory exists
    await fs.ensureDir(path.dirname(targetPath))
    
    const stat = await fs.stat(sourcePath)
    
    if (stat.isDirectory()) {
      // Copy directory and process files
      await fs.copy(sourcePath, targetPath)
      
      // Process all TypeScript files in the directory
      const files = await fs.readdir(targetPath)
      for (const file of files) {
        if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          const filePath = path.join(targetPath, file)
          let content = await fs.readFile(filePath, 'utf8')
          
          const header = `/**
 * ${pattern.description}
 * Extracted from ui-example-nativewind
 * 
 * This is a pattern example - adapt for your compound components
 */

`
          content = header + content
          await fs.writeFile(filePath, content)
        }
      }
    } else {
      // Copy single file
      let content = await fs.readFile(sourcePath, 'utf8')
      
      const header = `/**
 * ${pattern.description}
 * Extracted from ui-example-nativewind
 * 
 * This is a pattern example - adapt for your compound components
 */

`
      content = header + content
      await fs.writeFile(targetPath, content)
    }
    
    log.success(`✓ Extracted ${pattern.description}`)
    return true
    
  } catch (error) {
    log.error(`Failed to extract ${pattern.source}: ${error.message}`)
    return false
  }
}

// Create extraction summary
async function createExtractionSummary(results) {
  const summaryPath = path.join(SRC_DIR, 'EXTRACTION_SUMMARY.md')
  
  const summary = `# Extraction Summary from ui-example-nativewind

This document summarizes what was extracted from the ui-example-nativewind project.

## UI Components Extracted (${results.components.success}/${results.components.total})

${ESSENTIAL_UI_COMPONENTS.map(comp => 
  results.components.extracted.includes(comp) ? `✅ ${comp}` : `❌ ${comp}`
).join('\n')}

## Configuration Files Extracted (${results.configs.success}/${results.configs.total})

${CONFIG_FILES.map(config => 
  results.configs.extracted.includes(config.source) ? `✅ ${config.description}` : `❌ ${config.description}`
).join('\n')}

## Advanced Patterns Extracted (${results.patterns.success}/${results.patterns.total})

${ADVANCED_PATTERNS.map(pattern => 
  results.patterns.extracted.includes(pattern.source) ? `✅ ${pattern.description}` : `❌ ${pattern.description}`
).join('\n')}

## What to do next:

1. **Review extracted components** in \`src/gluestack-components/ui/\`
2. **Adapt configuration files** in \`config/\` for your project
3. **Study pattern examples** in \`src/examples/patterns/\`
4. **Integrate useful patterns** into your compound components
5. **Remove the ui-example-nativewind directory** if no longer needed

## Key Benefits Extracted:

- ✅ Complete UI component library with NativeWind integration
- ✅ Advanced responsive patterns and layouts
- ✅ Production-ready configuration examples
- ✅ Cross-platform optimization patterns
- ✅ Accessibility and performance best practices

Generated on: ${new Date().toISOString()}
`

  await fs.writeFile(summaryPath, summary)
  log.success('✓ Created extraction summary')
}

// Main extraction function
async function performFinalExtraction() {
  log.title('Final Extraction from ui-example-nativewind')
  
  if (!await fs.pathExists(EXAMPLE_DIR)) {
    log.error('ui-example-nativewind directory not found')
    return false
  }
  
  const results = {
    components: { success: 0, total: ESSENTIAL_UI_COMPONENTS.length, extracted: [] },
    configs: { success: 0, total: CONFIG_FILES.length, extracted: [] },
    patterns: { success: 0, total: ADVANCED_PATTERNS.length, extracted: [] }
  }
  
  // Extract UI components
  log.title('Extracting Essential UI Components')
  for (const component of ESSENTIAL_UI_COMPONENTS) {
    const success = await extractUIComponent(component)
    if (success) {
      results.components.success++
      results.components.extracted.push(component)
    }
  }
  
  // Extract configuration files
  log.title('Extracting Configuration Files')
  for (const config of CONFIG_FILES) {
    const success = await extractConfigFile(config)
    if (success) {
      results.configs.success++
      results.configs.extracted.push(config.source)
    }
  }
  
  // Extract advanced patterns
  log.title('Extracting Advanced Patterns')
  for (const pattern of ADVANCED_PATTERNS) {
    const success = await extractAdvancedPattern(pattern)
    if (success) {
      results.patterns.success++
      results.patterns.extracted.push(pattern.source)
    }
  }
  
  // Create summary
  await createExtractionSummary(results)
  
  // Final summary
  log.title('🎉 Final Extraction Complete!')
  log.success(`UI Components: ${results.components.success}/${results.components.total}`)
  log.success(`Config Files: ${results.configs.success}/${results.configs.total}`)
  log.success(`Patterns: ${results.patterns.success}/${results.patterns.total}`)
  
  const totalSuccess = results.components.success + results.configs.success + results.patterns.success
  const totalItems = results.components.total + results.configs.total + results.patterns.total
  
  log.info(`Total extracted: ${totalSuccess}/${totalItems} items`)
  
  if (totalSuccess === totalItems) {
    log.success('🎯 All items extracted successfully!')
    log.info('The ui-example-nativewind directory can now be safely removed.')
    return true
  } else {
    log.warning('Some items could not be extracted. Review the logs above.')
    return false
  }
}

// Run the extraction
if (require.main === module) {
  performFinalExtraction().catch(error => {
    log.error(`Final extraction failed: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { performFinalExtraction }
