"use client"

import React, { useState, useEffect } from 'react'
import { View, Dimensions, ScrollView } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { usePlatform } from '../../hooks'

// Types
export interface GridItem {
  id: string | number
  content: React.ReactNode
  span?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  order?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  offset?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
}

export interface ResponsiveGridProps {
  items: GridItem[]
  columns?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: number
  padding?: number
  className?: string
  scrollable?: boolean
  autoHeight?: boolean
}

// Breakpoints (in pixels)
const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
} as const

type Breakpoint = keyof typeof BREAKPOINTS

// Styles
const containerStyles = tva({
  base: "w-full",
  variants: {
    scrollable: {
      true: "flex-1",
      false: "",
    },
  },
})

const gridStyles = tva({
  base: "flex-row flex-wrap",
})

const itemStyles = tva({
  base: "mb-4",
})

export function ResponsiveGrid({
  items,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 6 },
  gap = 16,
  padding = 16,
  className,
  scrollable = false,
  autoHeight = true,
}: ResponsiveGridProps) {
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width)
  const { isWeb } = usePlatform()

  // Update screen width on resize
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenWidth(window.width)
    })

    return () => subscription?.remove()
  }, [])

  // Get current breakpoint
  const getCurrentBreakpoint = (): Breakpoint => {
    if (screenWidth >= BREAKPOINTS.xl) return 'xl'
    if (screenWidth >= BREAKPOINTS.lg) return 'lg'
    if (screenWidth >= BREAKPOINTS.md) return 'md'
    if (screenWidth >= BREAKPOINTS.sm) return 'sm'
    return 'xs'
  }

  // Get value for current breakpoint
  const getResponsiveValue = <T,>(
    values: Partial<Record<Breakpoint, T>>,
    fallback: T
  ): T => {
    const currentBreakpoint = getCurrentBreakpoint()
    const breakpointOrder: Breakpoint[] = ['xl', 'lg', 'md', 'sm', 'xs']
    
    // Find the first available value at or below current breakpoint
    const startIndex = breakpointOrder.indexOf(currentBreakpoint)
    for (let i = startIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (values[bp] !== undefined) {
        return values[bp]!
      }
    }
    
    return fallback
  }

  // Calculate item width
  const calculateItemWidth = (item: GridItem): number => {
    const totalColumns = getResponsiveValue(columns, 1)
    const itemSpan = getResponsiveValue(item.span || {}, 1)
    const itemOffset = getResponsiveValue(item.offset || {}, 0)
    
    const availableWidth = screenWidth - (padding * 2)
    const gapTotal = (totalColumns - 1) * gap
    const columnWidth = (availableWidth - gapTotal) / totalColumns
    
    return (columnWidth * itemSpan) + (gap * (itemSpan - 1))
  }

  // Calculate item margin left (for offset)
  const calculateItemMarginLeft = (item: GridItem): number => {
    const totalColumns = getResponsiveValue(columns, 1)
    const itemOffset = getResponsiveValue(item.offset || {}, 0)
    
    if (itemOffset === 0) return 0
    
    const availableWidth = screenWidth - (padding * 2)
    const gapTotal = (totalColumns - 1) * gap
    const columnWidth = (availableWidth - gapTotal) / totalColumns
    
    return (columnWidth * itemOffset) + (gap * itemOffset)
  }

  // Sort items by order
  const sortedItems = React.useMemo(() => {
    return [...items].sort((a, b) => {
      const aOrder = getResponsiveValue(a.order || {}, 0)
      const bOrder = getResponsiveValue(b.order || {}, 0)
      return aOrder - bOrder
    })
  }, [items, screenWidth])

  // Render grid item
  const renderItem = (item: GridItem, index: number) => {
    const itemWidth = calculateItemWidth(item)
    const marginLeft = calculateItemMarginLeft(item)
    
    return (
      <View
        key={item.id}
        className={itemStyles()}
        style={{
          width: itemWidth,
          marginLeft: index === 0 ? 0 : marginLeft,
          marginRight: gap / 2,
        }}
      >
        {item.content}
      </View>
    )
  }

  // Container component
  const Container = scrollable ? ScrollView : View

  return (
    <Container
      className={containerStyles({ scrollable, class: className })}
      style={{ padding }}
      showsVerticalScrollIndicator={false}
      {...(autoHeight && !scrollable ? { style: { minHeight: 'auto' } } : {})}
    >
      <View className={gridStyles()}>
        {sortedItems.map(renderItem)}
      </View>
    </Container>
  )
}

// Predefined grid layouts
export const TwoColumnGrid = (props: Omit<ResponsiveGridProps, 'columns'>) => (
  <ResponsiveGrid
    {...props}
    columns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}
  />
)

export const ThreeColumnGrid = (props: Omit<ResponsiveGridProps, 'columns'>) => (
  <ResponsiveGrid
    {...props}
    columns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}
  />
)

export const FourColumnGrid = (props: Omit<ResponsiveGridProps, 'columns'>) => (
  <ResponsiveGrid
    {...props}
    columns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
  />
)

export const DashboardGrid = (props: Omit<ResponsiveGridProps, 'columns'>) => (
  <ResponsiveGrid
    {...props}
    columns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 6 }}
  />
)

// Grid item helper component
export interface GridItemProps {
  children: React.ReactNode
  span?: ResponsiveGridProps['columns']
  order?: ResponsiveGridProps['columns']
  offset?: ResponsiveGridProps['columns']
  className?: string
}

export const GridItem: React.FC<GridItemProps> = ({
  children,
  span,
  order,
  offset,
  className,
}) => (
  <View className={className}>
    {children}
  </View>
)

// Utility functions for grid calculations
export const gridUtils = {
  // Calculate responsive columns
  getColumns: (breakpoint: Breakpoint, columns: ResponsiveGridProps['columns'] = {}) => {
    const breakpointOrder: Breakpoint[] = ['xl', 'lg', 'md', 'sm', 'xs']
    const startIndex = breakpointOrder.indexOf(breakpoint)
    
    for (let i = startIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (columns[bp] !== undefined) {
        return columns[bp]!
      }
    }
    
    return 1
  },

  // Calculate item width percentage
  getItemWidthPercentage: (span: number, totalColumns: number): string => {
    return `${(span / totalColumns) * 100}%`
  },

  // Calculate offset percentage
  getOffsetPercentage: (offset: number, totalColumns: number): string => {
    return `${(offset / totalColumns) * 100}%`
  },

  // Generate CSS Grid classes (for web)
  generateGridClasses: (columns: ResponsiveGridProps['columns'] = {}) => {
    const classes: string[] = []
    
    Object.entries(columns).forEach(([breakpoint, cols]) => {
      const prefix = breakpoint === 'xs' ? '' : `${breakpoint}:`
      classes.push(`${prefix}grid-cols-${cols}`)
    })
    
    return classes.join(' ')
  },
}

// CSS Grid version for web (alternative implementation)
export const CSSGrid: React.FC<ResponsiveGridProps> = ({
  items,
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 6 },
  gap = 16,
  padding = 16,
  className,
  scrollable = false,
}) => {
  const { isWeb } = usePlatform()
  
  if (!isWeb) {
    return <ResponsiveGrid {...{ items, columns, gap, padding, className, scrollable }} />
  }

  const gridClasses = gridUtils.generateGridClasses(columns)
  
  return (
    <div
      className={`grid ${gridClasses} gap-${gap / 4} p-${padding / 4} ${className || ''}`}
      style={{ gap: `${gap}px`, padding: `${padding}px` }}
    >
      {items.map((item) => (
        <div key={item.id} className="w-full">
          {item.content}
        </div>
      ))}
    </div>
  )
}
