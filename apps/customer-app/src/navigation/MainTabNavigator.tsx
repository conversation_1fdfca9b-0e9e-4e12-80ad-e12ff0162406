import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { Platform } from 'react-native'
import { 
  HomeIcon, 
  SearchIcon, 
  ClipboardListIcon, 
  HeartIcon, 
  UserIcon 
} from 'lucide-react-native'

import type { MainTabParamList } from '../types/navigation'
import { useTheme } from '../hooks/useTheme'

// Import Stack Navigators
import { HomeStackNavigator } from './HomeStackNavigator'
import { SearchStackNavigator } from './SearchStackNavigator'
import { OrdersStackNavigator } from './OrdersStackNavigator'
import { FavoritesScreen } from '../screens/favorites/FavoritesScreen'
import { ProfileStackNavigator } from './ProfileStackNavigator'

const Tab = createBottomTabNavigator<MainTabParamList>()

export function MainTabNavigator() {
  const { colors } = useTheme()

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let IconComponent

          switch (route.name) {
            case 'Home':
              IconComponent = HomeIcon
              break
            case 'Search':
              IconComponent = SearchIcon
              break
            case 'Orders':
              IconComponent = ClipboardListIcon
              break
            case 'Favorites':
              IconComponent = HeartIcon
              break
            case 'Profile':
              IconComponent = UserIcon
              break
            default:
              IconComponent = HomeIcon
          }

          return <IconComponent size={size} color={color} />
        },
        tabBarActiveTintColor: colors.primary[600],
        tabBarInactiveTintColor: colors.gray[500],
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: colors.gray[200],
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: Platform.OS === 'ios' ? 20 : 8,
          height: Platform.OS === 'ios' ? 88 : 64,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
        },
        tabBarItemStyle: {
          paddingVertical: 4,
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeStackNavigator}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen
        name="Search"
        component={SearchStackNavigator}
        options={{
          tabBarLabel: 'Search',
        }}
      />
      <Tab.Screen
        name="Orders"
        component={OrdersStackNavigator}
        options={{
          tabBarLabel: 'Orders',
        }}
      />
      <Tab.Screen
        name="Favorites"
        component={FavoritesScreen}
        options={{
          tabBarLabel: 'Favorites',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  )
}
