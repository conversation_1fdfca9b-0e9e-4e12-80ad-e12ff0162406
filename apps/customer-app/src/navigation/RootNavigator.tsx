import React from 'react'
import { NavigationContainer } from '@react-navigation/native'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import { StatusBar } from 'expo-status-bar'

import type { RootStackParamList } from '../types/navigation'
import { useAuthStore } from '../stores/authStore'

// Import Navigators
import { MainTabNavigator } from './MainTabNavigator'

// Import Screens
import { OnboardingScreen } from '../screens/auth/OnboardingScreen'
import { LoginScreen } from '../screens/auth/LoginScreen'
import { RegisterScreen } from '../screens/auth/RegisterScreen'
import { ForgotPasswordScreen } from '../screens/auth/ForgotPasswordScreen'
import { OTPVerificationScreen } from '../screens/auth/OTPVerificationScreen'

// Import Modal Screens
import { ServiceDetailScreen } from '../screens/services/ServiceDetailScreen'
import { ProviderProfileScreen } from '../screens/providers/ProviderProfileScreen'
import { BookingScreen } from '../screens/booking/BookingScreen'
import { OrderTrackingScreen } from '../screens/orders/OrderTrackingScreen'
import { ChatScreen } from '../screens/chat/ChatScreen'
import { PaymentScreen } from '../screens/payment/PaymentScreen'
import { LocationPickerScreen } from '../screens/location/LocationPickerScreen'
import { NotificationDetailScreen } from '../screens/notifications/NotificationDetailScreen'
import { ReviewOrderScreen } from '../screens/orders/ReviewOrderScreen'
import { HelpScreen } from '../screens/support/HelpScreen'
import { SupportScreen } from '../screens/support/SupportScreen'

const Stack = createNativeStackNavigator<RootStackParamList>()

export function RootNavigator() {
  const { isAuthenticated, hasCompletedOnboarding } = useAuthStore()

  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
        }}
      >
        {!hasCompletedOnboarding ? (
          // Onboarding Flow
          <Stack.Screen
            name="Onboarding"
            component={OnboardingScreen}
            options={{ gestureEnabled: false }}
          />
        ) : !isAuthenticated ? (
          // Auth Flow
          <Stack.Group>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ gestureEnabled: false }}
            />
            <Stack.Screen
              name="Register"
              component={RegisterScreen}
            />
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
            />
            <Stack.Screen
              name="OTPVerification"
              component={OTPVerificationScreen}
            />
          </Stack.Group>
        ) : (
          // Main App Flow
          <Stack.Group>
            <Stack.Screen
              name="MainTabs"
              component={MainTabNavigator}
              options={{ gestureEnabled: false }}
            />
            
            {/* Modal Screens */}
            <Stack.Group screenOptions={{ presentation: 'modal' }}>
              <Stack.Screen
                name="ServiceDetail"
                component={ServiceDetailScreen}
                options={{
                  headerShown: true,
                  title: 'Service Details',
                }}
              />
              <Stack.Screen
                name="ProviderProfile"
                component={ProviderProfileScreen}
                options={{
                  headerShown: true,
                  title: 'Provider Profile',
                }}
              />
              <Stack.Screen
                name="Booking"
                component={BookingScreen}
                options={{
                  headerShown: true,
                  title: 'Book Service',
                }}
              />
              <Stack.Screen
                name="OrderTracking"
                component={OrderTrackingScreen}
                options={{
                  headerShown: true,
                  title: 'Track Order',
                }}
              />
              <Stack.Screen
                name="Chat"
                component={ChatScreen}
                options={{
                  headerShown: true,
                  title: 'Chat',
                }}
              />
              <Stack.Screen
                name="Payment"
                component={PaymentScreen}
                options={{
                  headerShown: true,
                  title: 'Payment',
                }}
              />
              <Stack.Screen
                name="LocationPicker"
                component={LocationPickerScreen}
                options={{
                  headerShown: true,
                  title: 'Select Location',
                }}
              />
              <Stack.Screen
                name="NotificationDetail"
                component={NotificationDetailScreen}
                options={{
                  headerShown: true,
                  title: 'Notification',
                }}
              />
              <Stack.Screen
                name="ReviewOrder"
                component={ReviewOrderScreen}
                options={{
                  headerShown: true,
                  title: 'Review Order',
                }}
              />
              <Stack.Screen
                name="Help"
                component={HelpScreen}
                options={{
                  headerShown: true,
                  title: 'Help',
                }}
              />
              <Stack.Screen
                name="Support"
                component={SupportScreen}
                options={{
                  headerShown: true,
                  title: 'Support',
                }}
              />
            </Stack.Group>
          </Stack.Group>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  )
}
