import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'

interface OfflineAction {
  id: string
  type: string
  data: any
  timestamp: string
  retryCount: number
}

class OfflineService {
  private isInitialized = false
  private isOnline = true
  private pendingActions: OfflineAction[] = []
  private readonly STORAGE_KEY = '@hvppyplug_runner_offline_actions'
  private readonly MAX_RETRY_COUNT = 3

  async initialize() {
    if (this.isInitialized) return

    try {
      // Load pending actions from storage
      await this.loadPendingActions()

      // Set up network state listener
      NetInfo.addEventListener(state => {
        const wasOffline = !this.isOnline
        this.isOnline = state.isConnected ?? false

        console.log('📶 Network state changed:', {
          isConnected: this.isOnline,
          type: state.type,
        })

        // If we just came back online, process pending actions
        if (wasOffline && this.isOnline) {
          this.processPendingActions()
        }
      })

      // Get initial network state
      const netInfo = await NetInfo.fetch()
      this.isOnline = netInfo.isConnected ?? false

      this.isInitialized = true
      console.log('✅ Offline service initialized')

      // Process any pending actions if we're online
      if (this.isOnline && this.pendingActions.length > 0) {
        this.processPendingActions()
      }
    } catch (error) {
      console.error('❌ Failed to initialize offline service:', error)
      throw error
    }
  }

  private async loadPendingActions() {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.pendingActions = JSON.parse(stored)
        console.log(`📱 Loaded ${this.pendingActions.length} pending offline actions`)
      }
    } catch (error) {
      console.error('Failed to load pending actions:', error)
    }
  }

  private async savePendingActions() {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.pendingActions))
    } catch (error) {
      console.error('Failed to save pending actions:', error)
    }
  }

  // Queue an action to be executed when online
  async queueAction(type: string, data: any) {
    const action: OfflineAction = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date().toISOString(),
      retryCount: 0,
    }

    this.pendingActions.push(action)
    await this.savePendingActions()

    console.log(`📝 Queued offline action: ${type}`)

    // If we're online, try to process immediately
    if (this.isOnline) {
      this.processPendingActions()
    }
  }

  private async processPendingActions() {
    if (!this.isOnline || this.pendingActions.length === 0) return

    console.log(`🔄 Processing ${this.pendingActions.length} pending actions`)

    const actionsToProcess = [...this.pendingActions]
    
    for (const action of actionsToProcess) {
      try {
        await this.executeAction(action)
        
        // Remove successful action
        this.pendingActions = this.pendingActions.filter(a => a.id !== action.id)
        console.log(`✅ Successfully executed action: ${action.type}`)
      } catch (error) {
        console.error(`❌ Failed to execute action ${action.type}:`, error)
        
        // Increment retry count
        const actionIndex = this.pendingActions.findIndex(a => a.id === action.id)
        if (actionIndex !== -1) {
          this.pendingActions[actionIndex].retryCount++
          
          // Remove action if max retries exceeded
          if (this.pendingActions[actionIndex].retryCount >= this.MAX_RETRY_COUNT) {
            this.pendingActions.splice(actionIndex, 1)
            console.log(`🗑️ Removed action after ${this.MAX_RETRY_COUNT} failed attempts: ${action.type}`)
          }
        }
      }
    }

    await this.savePendingActions()
  }

  private async executeAction(action: OfflineAction) {
    // This would integrate with your API service
    // For now, we'll just simulate the execution
    switch (action.type) {
      case 'update_location':
        console.log('📍 Executing location update:', action.data)
        // await locationService.updateLocation(action.data)
        break
      case 'update_order_status':
        console.log('📦 Executing order status update:', action.data)
        // await orderService.updateOrderStatus(action.data.orderId, action.data.status)
        break
      case 'upload_delivery_proof':
        console.log('📷 Executing delivery proof upload:', action.data)
        // await deliveryService.uploadProof(action.data)
        break
      default:
        console.log(`Unknown action type: ${action.type}`)
    }
  }

  // Check if device is online
  isDeviceOnline(): boolean {
    return this.isOnline
  }

  // Get pending actions count
  getPendingActionsCount(): number {
    return this.pendingActions.length
  }

  // Clear all pending actions (use with caution)
  async clearPendingActions() {
    this.pendingActions = []
    await this.savePendingActions()
    console.log('🗑️ Cleared all pending actions')
  }
}

export const offlineService = new OfflineService()
