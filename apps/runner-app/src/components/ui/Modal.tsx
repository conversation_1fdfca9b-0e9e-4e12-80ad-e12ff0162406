import React from 'react'
import {
  Modal as RNModal,
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Dimensions,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { X } from 'lucide-react-native'
import { useTheme } from '../../providers/ThemeProvider'

const { width: screenWidth, height: screenHeight } = Dimensions.get('window')

export interface ModalProps {
  visible: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'full'
  position?: 'center' | 'bottom' | 'top'
  showCloseButton?: boolean
  closeOnBackdrop?: boolean
  animationType?: 'slide' | 'fade' | 'none'
  style?: ViewStyle
  contentStyle?: ViewStyle
  titleStyle?: TextStyle
  testID?: string
}

export function Modal({
  visible,
  onClose,
  title,
  children,
  size = 'md',
  position = 'center',
  showCloseButton = true,
  closeOnBackdrop = true,
  animationType = 'fade',
  style,
  contentStyle,
  titleStyle,
  testID,
}: ModalProps) {
  const { theme } = useTheme()

  const getModalStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    }

    const positionStyles: Record<string, ViewStyle> = {
      center: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.spacing.lg,
      },
      bottom: {
        justifyContent: 'flex-end',
      },
      top: {
        justifyContent: 'flex-start',
        paddingTop: theme.spacing['3xl'],
      },
    }

    return {
      ...baseStyles,
      ...positionStyles[position],
      ...style,
    }
  }

  const getContentStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      backgroundColor: theme.colors.white,
      borderRadius: position === 'bottom' ? 0 : theme.borderRadius.lg,
      shadowColor: theme.colors.black,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 8,
    }

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        maxWidth: screenWidth * 0.8,
        maxHeight: screenHeight * 0.6,
      },
      md: {
        maxWidth: screenWidth * 0.9,
        maxHeight: screenHeight * 0.8,
      },
      lg: {
        maxWidth: screenWidth * 0.95,
        maxHeight: screenHeight * 0.9,
      },
      full: {
        width: screenWidth,
        height: screenHeight,
        borderRadius: 0,
      },
    }

    // Position-specific styles
    const positionStyles: Record<string, ViewStyle> = {
      center: {},
      bottom: {
        width: screenWidth,
        borderTopLeftRadius: theme.borderRadius.lg,
        borderTopRightRadius: theme.borderRadius.lg,
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
      },
      top: {
        width: screenWidth,
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        borderBottomLeftRadius: theme.borderRadius.lg,
        borderBottomRightRadius: theme.borderRadius.lg,
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...positionStyles[position],
      ...contentStyle,
    }
  }

  const getHeaderStyles = (): ViewStyle => {
    return {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: theme.spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.gray[200],
    }
  }

  const getTitleStyles = (): TextStyle => {
    return {
      fontSize: theme.fontSize.xl,
      fontWeight: theme.fontWeight.semibold,
      color: theme.colors.gray[900],
      flex: 1,
      ...titleStyle,
    }
  }

  const getBodyStyles = (): ViewStyle => {
    return {
      flex: 1,
      padding: theme.spacing.lg,
    }
  }

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose()
    }
  }

  return (
    <RNModal
      visible={visible}
      transparent
      animationType={animationType}
      onRequestClose={onClose}
      testID={testID}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={getModalStyles()}>
          <TouchableWithoutFeedback>
            <View style={getContentStyles()}>
              {(title || showCloseButton) && (
                <View style={getHeaderStyles()}>
                  {title && (
                    <Text style={getTitleStyles()}>
                      {title}
                    </Text>
                  )}
                  {showCloseButton && (
                    <TouchableOpacity
                      onPress={onClose}
                      style={{
                        padding: theme.spacing.xs,
                        marginLeft: theme.spacing.md,
                      }}
                    >
                      <X size={24} color={theme.colors.gray[600]} />
                    </TouchableOpacity>
                  )}
                </View>
              )}
              
              <View style={getBodyStyles()}>
                {children}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  )
}

// Confirmation Modal
export interface ConfirmationModalProps {
  visible: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  confirmVariant?: 'primary' | 'danger'
}

export function ConfirmationModal({
  visible,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
}: ConfirmationModalProps) {
  const { theme } = useTheme()

  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title={title}
      size="sm"
      showCloseButton={false}
    >
      <View style={{ marginBottom: theme.spacing.lg }}>
        <Text style={{
          fontSize: theme.fontSize.base,
          color: theme.colors.gray[700],
          lineHeight: 24,
        }}>
          {message}
        </Text>
      </View>
      
      <View style={{
        flexDirection: 'row',
        justifyContent: 'flex-end',
        gap: theme.spacing.md,
      }}>
        <TouchableOpacity
          onPress={onClose}
          style={{
            paddingHorizontal: theme.spacing.lg,
            paddingVertical: theme.spacing.sm,
            borderRadius: theme.borderRadius.md,
            borderWidth: 1,
            borderColor: theme.colors.gray[300],
          }}
        >
          <Text style={{
            fontSize: theme.fontSize.base,
            color: theme.colors.gray[700],
            fontWeight: theme.fontWeight.medium,
          }}>
            {cancelText}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={handleConfirm}
          style={{
            paddingHorizontal: theme.spacing.lg,
            paddingVertical: theme.spacing.sm,
            borderRadius: theme.borderRadius.md,
            backgroundColor: confirmVariant === 'danger' 
              ? theme.colors.error[600] 
              : theme.colors.primary[600],
          }}
        >
          <Text style={{
            fontSize: theme.fontSize.base,
            color: theme.colors.white,
            fontWeight: theme.fontWeight.medium,
          }}>
            {confirmText}
          </Text>
        </TouchableOpacity>
      </View>
    </Modal>
  )
}
