"use client"

import React from 'react'
import { View, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button } from '@gluestack-ui/themed'
import { ClockIcon, MapPinIcon, PhoneIcon, CheckIcon, XIcon } from 'lucide-react-native'
import { format } from '../../utils'

// Types
export interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  customizations?: string[]
}

export interface Order {
  id: string
  customerId: string
  vendorId: string
  runnerId?: string
  items: OrderItem[]
  subtotal: number
  discount: number
  total: number
  status: 'pending' | 'accepted' | 'preparing' | 'ready' | 'picked_up' | 'en_route' | 'delivered' | 'cancelled'
  paymentMethod: 'card' | 'mpesa' | 'voucher' | 'payfast'
  paymentStatus: 'pending' | 'paid' | 'failed'
  deliveryAddress: {
    street: string
    city: string
    postalCode: string
    coordinates: {
      lat: number
      lng: number
    }
  }
  customer?: {
    id: string
    name: string
    phone: string
    avatarUrl?: string
  }
  vendor?: {
    id: string
    name: string
    phone: string
    imageUrl?: string
  }
  runner?: {
    id: string
    name: string
    phone: string
    avatarUrl?: string
    rating: number
  }
  estimatedDelivery?: string
  createdAt: string
  updatedAt: string
  notes?: string
}

export interface OrderCardProps {
  order: Order
  onPress?: (order: Order) => void
  onAccept?: (orderId: string) => void
  onDecline?: (orderId: string) => void
  onUpdateStatus?: (orderId: string, status: Order['status']) => void
  onCall?: (phone: string) => void
  variant?: 'customer' | 'vendor' | 'runner'
  showActions?: boolean
  className?: string
}

// Styles
const cardStyles = tva({
  base: "bg-background-0 border border-outline-200 rounded-lg p-4 mb-4",
  variants: {
    pressable: {
      true: "active:bg-background-50",
    },
    status: {
      pending: "border-l-4 border-l-warning-500",
      accepted: "border-l-4 border-l-info-500",
      preparing: "border-l-4 border-l-info-500",
      ready: "border-l-4 border-l-success-500",
      picked_up: "border-l-4 border-l-success-500",
      en_route: "border-l-4 border-l-success-500",
      delivered: "border-l-4 border-l-success-500",
      cancelled: "border-l-4 border-l-error-500",
    },
  },
})

const statusBadgeStyles = tva({
  base: "px-2 py-1 rounded-full text-xs font-medium",
  variants: {
    status: {
      pending: "bg-warning-100 text-warning-700",
      accepted: "bg-info-100 text-info-700",
      preparing: "bg-info-100 text-info-700",
      ready: "bg-success-100 text-success-700",
      picked_up: "bg-success-100 text-success-700",
      en_route: "bg-success-100 text-success-700",
      delivered: "bg-success-100 text-success-700",
      cancelled: "bg-error-100 text-error-700",
    },
  },
})

/**
 * OrderCard component for displaying order information
 * 
 * Features:
 * - Different variants for customer, vendor, and runner views
 * - Order status indication with color coding
 * - Action buttons for order management
 * - Customer/vendor/runner information display
 * - Order items summary
 * - Delivery information
 */
export function OrderCard({
  order,
  onPress,
  onAccept,
  onDecline,
  onUpdateStatus,
  onCall,
  variant = 'customer',
  showActions = true,
  className,
}: OrderCardProps) {
  const handlePress = () => {
    onPress?.(order)
  }

  const handleAccept = () => {
    onAccept?.(order.id)
  }

  const handleDecline = () => {
    onDecline?.(order.id)
  }

  const handleCall = (phone: string) => {
    onCall?.(phone)
  }

  const getStatusLabel = (status: Order['status']) => {
    const labels = {
      pending: 'Pending',
      accepted: 'Accepted',
      preparing: 'Preparing',
      ready: 'Ready',
      picked_up: 'Picked Up',
      en_route: 'On the Way',
      delivered: 'Delivered',
      cancelled: 'Cancelled',
    }
    return labels[status]
  }

  const renderHeader = () => (
    <View className="flex-row items-center justify-between mb-3">
      <View className="flex-row items-center">
        <Text className="text-lg font-bold text-typography-900">
          #{order.id.slice(-6)}
        </Text>
        <View className={statusBadgeStyles({ status: order.status })}>
          <Text>{getStatusLabel(order.status)}</Text>
        </View>
      </View>
      <Text className="text-sm text-typography-600">
        {format.formatRelativeTime(new Date(order.createdAt))}
      </Text>
    </View>
  )

  const renderPersonInfo = () => {
    let person, label, showPhone = false

    switch (variant) {
      case 'customer':
        person = order.vendor
        label = 'Vendor'
        showPhone = true
        break
      case 'vendor':
        person = order.customer
        label = 'Customer'
        showPhone = true
        break
      case 'runner':
        person = order.vendor
        label = 'Pickup from'
        showPhone = true
        break
    }

    if (!person) return null

    return (
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-1">
          <Text className="text-sm text-typography-600">{label}</Text>
          <Text className="font-medium text-typography-900">{person.name}</Text>
        </View>
        {showPhone && (
          <Button
            variant="outline"
            size="sm"
            onPress={() => handleCall(person.phone)}
          >
            <PhoneIcon size={16} />
          </Button>
        )}
      </View>
    )
  }

  const renderOrderItems = () => (
    <View className="mb-3">
      <Text className="text-sm font-medium text-typography-900 mb-2">
        Items ({order.items.length})
      </Text>
      {order.items.slice(0, 3).map((item, index) => (
        <View key={index} className="flex-row justify-between mb-1">
          <Text className="text-sm text-typography-600 flex-1">
            {item.quantity}x {item.name}
          </Text>
          <Text className="text-sm text-typography-900">
            R{(item.price * item.quantity).toFixed(2)}
          </Text>
        </View>
      ))}
      {order.items.length > 3 && (
        <Text className="text-sm text-typography-500">
          +{order.items.length - 3} more items
        </Text>
      )}
    </View>
  )

  const renderDeliveryInfo = () => {
    if (variant === 'vendor' && order.status === 'pending') return null

    return (
      <View className="mb-3">
        <View className="flex-row items-start">
          <MapPinIcon size={16} className="text-typography-500 mt-0.5" />
          <View className="ml-2 flex-1">
            <Text className="text-sm text-typography-600">Delivery to</Text>
            <Text className="text-sm text-typography-900">
              {order.deliveryAddress.street}
            </Text>
            <Text className="text-sm text-typography-600">
              {order.deliveryAddress.city}, {order.deliveryAddress.postalCode}
            </Text>
          </View>
        </View>
        {order.estimatedDelivery && (
          <View className="flex-row items-center mt-2">
            <ClockIcon size={16} className="text-typography-500" />
            <Text className="ml-2 text-sm text-typography-600">
              Est. delivery: {order.estimatedDelivery}
            </Text>
          </View>
        )}
      </View>
    )
  }

  const renderTotal = () => (
    <View className="border-t border-outline-200 pt-3 mb-3">
      <View className="flex-row justify-between">
        <Text className="font-bold text-typography-900">Total</Text>
        <Text className="font-bold text-typography-900">
          R{order.total.toFixed(2)}
        </Text>
      </View>
      <Text className="text-sm text-typography-600">
        via {order.paymentMethod.toUpperCase()}
      </Text>
    </View>
  )

  const renderActions = () => {
    if (!showActions) return null

    if (variant === 'vendor' && order.status === 'pending') {
      return (
        <View className="flex-row space-x-2">
          <Button
            variant="outline"
            className="flex-1"
            onPress={handleDecline}
          >
            <XIcon size={16} />
            <Text className="ml-2">Decline</Text>
          </Button>
          <Button
            className="flex-1"
            onPress={handleAccept}
          >
            <CheckIcon size={16} />
            <Text className="ml-2">Accept</Text>
          </Button>
        </View>
      )
    }

    if (variant === 'vendor' && ['accepted', 'preparing'].includes(order.status)) {
      return (
        <Button
          onPress={() => onUpdateStatus?.(order.id, 'ready')}
        >
          <Text>Mark as Ready</Text>
        </Button>
      )
    }

    if (variant === 'runner' && order.status === 'ready') {
      return (
        <Button
          onPress={() => onUpdateStatus?.(order.id, 'picked_up')}
        >
          <Text>Mark as Picked Up</Text>
        </Button>
      )
    }

    if (variant === 'runner' && order.status === 'picked_up') {
      return (
        <Button
          onPress={() => onUpdateStatus?.(order.id, 'en_route')}
        >
          <Text>Start Delivery</Text>
        </Button>
      )
    }

    if (variant === 'runner' && order.status === 'en_route') {
      return (
        <Button
          onPress={() => onUpdateStatus?.(order.id, 'delivered')}
        >
          <Text>Mark as Delivered</Text>
        </Button>
      )
    }

    return null
  }

  return (
    <Pressable
      onPress={handlePress}
      className={cardStyles({ 
        pressable: !!onPress, 
        status: order.status,
        class: className 
      })}
    >
      {renderHeader()}
      {renderPersonInfo()}
      {renderOrderItems()}
      {renderDeliveryInfo()}
      {renderTotal()}
      {renderActions()}
    </Pressable>
  )
}

// Export variants
export const CustomerOrderCard = (props: Omit<OrderCardProps, 'variant'>) => (
  <OrderCard {...props} variant="customer" />
)

export const VendorOrderCard = (props: Omit<OrderCardProps, 'variant'>) => (
  <OrderCard {...props} variant="vendor" />
)

export const RunnerOrderCard = (props: Omit<OrderCardProps, 'variant'>) => (
  <OrderCard {...props} variant="runner" />
)
