#!/usr/bin/env node

/**
 * HVPPYPlug+ Migration Validation Script
 * Validates that the mobile-services migration was successful
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

const log = (color, message) => console.log(`${colors[color]}${message}${colors.reset}`)

const ROOT_DIR = path.resolve(__dirname, '..')
const MOBILE_SERVICES_DIR = path.join(ROOT_DIR, 'packages', 'mobile-services')

/**
 * Check if mobile-services package exists and has correct structure
 */
function validatePackageStructure() {
  log('blue', '📁 Validating package structure...')
  
  const requiredFiles = [
    'package.json',
    'tsconfig.json',
    'tsup.config.ts',
    'src/index.ts',
    'src/services/LocationService/LocationService.ts',
    'src/services/NotificationService/NotificationService.ts',
    'src/services/CameraService/CameraService.ts',
    'src/services/AppwriteService/AppwriteService.ts',
    'src/hooks/useLocation.ts',
    'src/hooks/useNotifications.ts',
    'src/hooks/useCamera.ts',
  ]
  
  const missingFiles = []
  
  requiredFiles.forEach(file => {
    const filePath = path.join(MOBILE_SERVICES_DIR, file)
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file)
    }
  })
  
  if (missingFiles.length === 0) {
    log('green', '  ✅ All required files present')
    return true
  } else {
    log('red', '  ❌ Missing files:')
    missingFiles.forEach(file => log('red', `    - ${file}`))
    return false
  }
}

/**
 * Validate package.json configuration
 */
function validatePackageJson() {
  log('blue', '📦 Validating package.json...')
  
  try {
    const packageJsonPath = path.join(MOBILE_SERVICES_DIR, 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    
    const requiredFields = ['name', 'version', 'main', 'types', 'dependencies']
    const missingFields = requiredFields.filter(field => !packageJson[field])
    
    if (missingFields.length > 0) {
      log('red', '  ❌ Missing required fields:')
      missingFields.forEach(field => log('red', `    - ${field}`))
      return false
    }
    
    // Check for required dependencies
    const requiredDeps = [
      'expo-location',
      'expo-notifications',
      'expo-camera',
      'expo-image-picker',
      'appwrite'
    ]
    
    const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep])
    
    if (missingDeps.length > 0) {
      log('red', '  ❌ Missing required dependencies:')
      missingDeps.forEach(dep => log('red', `    - ${dep}`))
      return false
    }
    
    log('green', '  ✅ Package.json is valid')
    return true
  } catch (error) {
    log('red', `  ❌ Error reading package.json: ${error.message}`)
    return false
  }
}

/**
 * Validate TypeScript configuration
 */
function validateTypeScript() {
  log('blue', '🔧 Validating TypeScript configuration...')
  
  try {
    const tsconfigPath = path.join(MOBILE_SERVICES_DIR, 'tsconfig.json')
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'))
    
    if (!tsconfig.compilerOptions) {
      log('red', '  ❌ Missing compilerOptions')
      return false
    }
    
    const requiredOptions = ['outDir', 'declaration', 'jsx']
    const missingOptions = requiredOptions.filter(option => !tsconfig.compilerOptions[option])
    
    if (missingOptions.length > 0) {
      log('red', '  ❌ Missing compiler options:')
      missingOptions.forEach(option => log('red', `    - ${option}`))
      return false
    }
    
    log('green', '  ✅ TypeScript configuration is valid')
    return true
  } catch (error) {
    log('red', `  ❌ Error reading tsconfig.json: ${error.message}`)
    return false
  }
}

/**
 * Validate exports in main index file
 */
function validateExports() {
  log('blue', '📤 Validating exports...')
  
  try {
    const indexPath = path.join(MOBILE_SERVICES_DIR, 'src', 'index.ts')
    const content = fs.readFileSync(indexPath, 'utf8')
    
    const requiredExports = [
      'LocationService',
      'NotificationService',
      'CameraService',
      'AppwriteService',
      'useLocation',
      'useNotifications',
      'useCamera'
    ]
    
    const missingExports = requiredExports.filter(exp => !content.includes(exp))
    
    if (missingExports.length > 0) {
      log('red', '  ❌ Missing exports:')
      missingExports.forEach(exp => log('red', `    - ${exp}`))
      return false
    }
    
    log('green', '  ✅ All required exports present')
    return true
  } catch (error) {
    log('red', `  ❌ Error reading index.ts: ${error.message}`)
    return false
  }
}

/**
 * Check if old service files were removed from ui-components-v2
 */
function validateCleanup() {
  log('blue', '🧹 Validating cleanup...')
  
  const uiComponentsDir = path.join(ROOT_DIR, 'packages', 'ui-components-v2', 'src')
  const oldServicePaths = [
    path.join(uiComponentsDir, 'services', 'LocationService'),
    path.join(uiComponentsDir, 'services', 'NotificationService'),
    path.join(uiComponentsDir, 'services', 'CameraService'),
    path.join(uiComponentsDir, 'services', 'AppwriteService'),
    path.join(uiComponentsDir, 'hooks', 'useLocation.ts'),
    path.join(uiComponentsDir, 'hooks', 'useNotifications.ts'),
    path.join(uiComponentsDir, 'hooks', 'useCamera.ts'),
  ]
  
  const remainingFiles = oldServicePaths.filter(filePath => fs.existsSync(filePath))
  
  if (remainingFiles.length > 0) {
    log('yellow', '  ⚠️ Old service files still present:')
    remainingFiles.forEach(file => log('yellow', `    - ${path.relative(ROOT_DIR, file)}`))
    log('yellow', '  Consider removing these manually if they are no longer needed')
  } else {
    log('green', '  ✅ Old service files cleaned up')
  }
  
  return true
}

/**
 * Validate that apps can import from the new package
 */
function validateImports() {
  log('blue', '📥 Validating import statements...')
  
  const appsDir = path.join(ROOT_DIR, 'apps')
  const apps = ['customer-app', 'vendor-app', 'runner-app']
  
  let hasIssues = false
  
  apps.forEach(appName => {
    const appDir = path.join(appsDir, appName)
    if (fs.existsSync(appDir)) {
      const packageJsonPath = path.join(appDir, 'package.json')
      
      if (fs.existsSync(packageJsonPath)) {
        try {
          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
          
          if (!packageJson.dependencies || !packageJson.dependencies['@hvppyplug/mobile-services']) {
            log('yellow', `  ⚠️ ${appName} missing mobile-services dependency`)
            hasIssues = true
          }
        } catch (error) {
          log('red', `  ❌ Error reading ${appName}/package.json: ${error.message}`)
          hasIssues = true
        }
      }
    }
  })
  
  if (!hasIssues) {
    log('green', '  ✅ Import configuration looks good')
  }
  
  return !hasIssues
}

/**
 * Try to build the package
 */
function validateBuild() {
  log('blue', '🔨 Validating package build...')
  
  try {
    process.chdir(MOBILE_SERVICES_DIR)
    
    // Check if dependencies are installed
    if (!fs.existsSync(path.join(MOBILE_SERVICES_DIR, 'node_modules'))) {
      log('yellow', '  ⚠️ Dependencies not installed, installing...')
      execSync('pnpm install', { stdio: 'pipe' })
    }
    
    // Try to build
    execSync('pnpm build', { stdio: 'pipe' })
    
    // Check if dist directory was created
    const distDir = path.join(MOBILE_SERVICES_DIR, 'dist')
    if (fs.existsSync(distDir)) {
      const distFiles = fs.readdirSync(distDir)
      if (distFiles.length > 0) {
        log('green', '  ✅ Package builds successfully')
        return true
      }
    }
    
    log('red', '  ❌ Build completed but no output files found')
    return false
  } catch (error) {
    log('red', `  ❌ Build failed: ${error.message}`)
    return false
  } finally {
    process.chdir(ROOT_DIR)
  }
}

/**
 * Main validation function
 */
function main() {
  log('cyan', '🔍 HVPPYPlug+ Mobile Services Migration Validation')
  log('cyan', '================================================\n')
  
  const validations = [
    { name: 'Package Structure', fn: validatePackageStructure },
    { name: 'Package.json', fn: validatePackageJson },
    { name: 'TypeScript Config', fn: validateTypeScript },
    { name: 'Exports', fn: validateExports },
    { name: 'Cleanup', fn: validateCleanup },
    { name: 'Imports', fn: validateImports },
    { name: 'Build', fn: validateBuild },
  ]
  
  let passedCount = 0
  let totalCount = validations.length
  
  validations.forEach(({ name, fn }) => {
    try {
      if (fn()) {
        passedCount++
      }
    } catch (error) {
      log('red', `❌ ${name} validation failed: ${error.message}`)
    }
    console.log() // Add spacing
  })
  
  // Summary
  log('cyan', '📊 Validation Summary')
  log('cyan', '===================')
  log('blue', `Passed: ${passedCount}/${totalCount}`)
  
  if (passedCount === totalCount) {
    log('green', '🎉 All validations passed! Migration was successful.')
    log('blue', '\n📝 Next steps:')
    log('blue', '  1. Test your applications')
    log('blue', '  2. Update any remaining import statements manually if needed')
    log('blue', '  3. Commit your changes')
    process.exit(0)
  } else {
    log('red', '❌ Some validations failed. Please review and fix the issues.')
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  validatePackageStructure,
  validatePackageJson,
  validateTypeScript,
  validateExports,
  validateCleanup,
  validateImports,
  validateBuild
}
