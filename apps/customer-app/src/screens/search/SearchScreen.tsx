import React, { useState, useEffect } from 'react'
import { View, Text, TextInput, TouchableOpacity, ScrollView, FlatList } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { SearchIcon, FilterIcon, XIcon, MapPinIcon } from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useServicesStore } from '../../stores/servicesStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'
import { SkeletonList } from '../../components/SkeletonLoader'
import type { SearchStackScreenProps } from '../../types/navigation'

type Props = SearchStackScreenProps<'SearchMain'>

export function SearchScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const {
    searchResults,
    categories,
    isLoading,
    searchServices,
    clearSearchResults,
    getCategories,
  } = useServicesStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('All')
  const [showFilters, setShowFilters] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'Plumbing services',
    'Food delivery',
    'Cleaning services',
    'Electrical repair'
  ])

  useEffect(() => {
    getCategories()
  }, [])

  const handleSearch = (query: string) => {
    if (query.trim()) {
      searchServices(query, {
        categoryId: selectedCategory !== 'All' ? selectedCategory : undefined,
        limit: 20
      })

      // Add to recent searches
      setRecentSearches(prev => {
        const filtered = prev.filter(item => item !== query)
        return [query, ...filtered].slice(0, 5)
      })
    } else {
      clearSearchResults()
    }
  }

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId)
    if (searchQuery.trim()) {
      handleSearch(searchQuery)
    }
  }

  const handleRecentSearchPress = (query: string) => {
    setSearchQuery(query)
    handleSearch(query)
  }

  const handleServicePress = (serviceId: string) => {
    navigation.navigate('ServiceDetail', { serviceId })
  }

  const handleProviderPress = (providerId: string) => {
    navigation.navigate('ProviderProfile', { providerId })
  }

  const renderSearchHeader = () => (
    <View style={{
      padding: 16,
      backgroundColor: colors.background.primary,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      {/* Search Input */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        marginBottom: 12,
      }}>
        <SearchIcon size={20} color={colors.text.tertiary} />
        <TextInput
          style={[
            typography.body.medium,
            {
              flex: 1,
              marginLeft: 12,
              color: colors.text.primary,
            }
          ]}
          placeholder="Search services, vendors, or categories..."
          placeholderTextColor={colors.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={() => handleSearch(searchQuery)}
          returnKeyType="search"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity
            onPress={() => {
              setSearchQuery('')
              clearSearchResults()
            }}
            style={{ padding: 4 }}
          >
            <XIcon size={16} color={colors.text.tertiary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Button */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: showFilters ? colors.primary[500] : colors.background.tertiary,
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 20,
          }}
          onPress={() => setShowFilters(!showFilters)}
        >
          <FilterIcon
            size={16}
            color={showFilters ? colors.white : colors.text.primary}
          />
          <Text style={[
            typography.label.small,
            {
              marginLeft: 8,
              color: showFilters ? colors.white : colors.text.primary,
            }
          ]}>
            Filters
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 12,
            paddingVertical: 6,
          }}
          onPress={() => navigation.navigate('LocationPicker')}
        >
          <MapPinIcon size={14} color={colors.primary[500]} />
          <Text style={[
            typography.body.small,
            { marginLeft: 4, color: colors.primary[500] }
          ]}>
            Soweto, JHB
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  const renderFilters = () => {
    if (!showFilters) return null

    return (
      <View style={{
        backgroundColor: colors.background.secondary,
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.primary,
      }}>
        <Text style={[
          typography.body.medium,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
        ]}>
          Categories
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            {['All', ...categories.map(cat => cat.name)].map((category) => (
              <TouchableOpacity
                key={category}
                style={{
                  backgroundColor: selectedCategory === category ? colors.primary[500] : colors.background.primary,
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderRadius: 20,
                  borderWidth: 1,
                  borderColor: selectedCategory === category ? colors.primary[500] : colors.border.primary,
                }}
                onPress={() => handleCategorySelect(category)}
              >
                <Text style={[
                  typography.label.small,
                  {
                    color: selectedCategory === category ? colors.white : colors.text.primary,
                    fontWeight: selectedCategory === category ? '600' : '400'
                  }
                ]}>
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    )
  }

  const renderRecentSearches = () => {
    if (searchQuery.length > 0 || searchResults.length > 0) return null

    return (
      <View style={{ padding: 16 }}>
        <Text style={[
          typography.body.large,
          { color: colors.text.primary, fontWeight: '600', marginBottom: 12 }
        ]}>
          Recent Searches
        </Text>
        {recentSearches.map((search, index) => (
          <TouchableOpacity
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 12,
              borderBottomWidth: index < recentSearches.length - 1 ? 1 : 0,
              borderBottomColor: colors.border.primary,
            }}
            onPress={() => handleRecentSearchPress(search)}
          >
            <SearchIcon size={16} color={colors.text.tertiary} />
            <Text style={[
              typography.body.medium,
              { marginLeft: 12, color: colors.text.primary, flex: 1 }
            ]}>
              {search}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    )
  }

  const renderSearchResults = () => {
    if (searchQuery.length === 0) return null

    if (isLoading) {
      return <SkeletonList count={5} style={{ padding: 16 }} />
    }

    if (searchResults.length === 0) {
      return (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: 32,
        }}>
          <Text style={[
            typography.body.large,
            { color: colors.text.primary, textAlign: 'center', marginBottom: 8 }
          ]}>
            No results found
          </Text>
          <Text style={[
            typography.body.medium,
            { color: colors.text.secondary, textAlign: 'center' }
          ]}>
            Try adjusting your search terms or filters
          </Text>
        </View>
      )
    }

    return (
      <FlatList
        data={searchResults}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={{
              backgroundColor: colors.background.primary,
              borderRadius: 12,
              padding: 16,
              marginBottom: 12,
              borderWidth: 1,
              borderColor: colors.border.primary,
              shadowColor: colors.shadow.light,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3,
            }}
            onPress={() => handleServicePress(item.id)}
          >
            <View style={{ flexDirection: 'row' }}>
              <View style={{
                width: 60,
                height: 60,
                borderRadius: 8,
                backgroundColor: colors.background.tertiary,
                marginRight: 12,
              }} />
              <View style={{ flex: 1 }}>
                <Text style={[
                  typography.body.medium,
                  { color: colors.text.primary, fontWeight: '600' }
                ]}>
                  {item.name}
                </Text>
                <Text style={[
                  typography.body.small,
                  { color: colors.text.secondary, marginTop: 2 }
                ]}>
                  {item.description}
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 4 }}>
                  <Text style={[
                    typography.body.small,
                    { color: colors.text.tertiary }
                  ]}>
                    ⭐ {item.rating.average} • {item.duration} min • R{item.price.amount}
                  </Text>
                </View>
                <TouchableOpacity
                  style={{ marginTop: 8 }}
                  onPress={() => handleProviderPress(item.providerId)}
                >
                  <Text style={[
                    typography.body.small,
                    { color: colors.primary[500], fontWeight: '600' }
                  ]}>
                    View Provider →
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        )}
      />
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      {renderSearchHeader()}
      {renderFilters()}
      {renderRecentSearches()}
      {renderSearchResults()}
    </SafeAreaView>
  )
}
