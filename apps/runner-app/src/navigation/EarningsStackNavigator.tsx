import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { EarningsStackParamList } from '../types/navigation'
import { useTheme } from '../providers/ThemeProvider'

// Import Earnings Screens (placeholders for now)
import { EarningsOverviewScreen } from '../screens/earnings/EarningsOverviewScreen'

const Stack = createNativeStackNavigator<EarningsStackParamList>()

export function EarningsStackNavigator() {
  const { theme } = useTheme()

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.white,
        },
        headerTintColor: theme.colors.gray[900],
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerShadowVisible: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="EarningsOverview"
        component={EarningsOverviewScreen}
        options={{
          title: 'Earnings',
          headerShown: false, // Custom header in the screen
        }}
      />
    </Stack.Navigator>
  )
}
