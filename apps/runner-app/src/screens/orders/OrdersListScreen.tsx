import React from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { Package } from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'
import { OrderCard } from '../../components/business'
import type { Order } from '../../types'

export function OrdersListScreen() {
  const { theme } = useTheme()

  // Mock orders data
  const orders: Order[] = [
    {
      id: '1',
      customerId: 'cust1',
      customerName: '<PERSON>',
      customerPhone: '+27 71 234 5678',
      vendorId: 'vendor1',
      vendorName: 'KFC Soweto',
      vendorAddress: '123 Main Road, Soweto',
      items: [
        { id: 'item1', name: 'Zinger Burger', quantity: 2, price: 15.50, total: 31.00 },
        { id: 'item2', name: 'Regular Chips', quantity: 1, price: 12.50, total: 12.50 },
        { id: 'item3', name: 'Coke 330ml', quantity: 1, price: 8.00, total: 8.00 },
      ],
      subtotal: 51.50,
      deliveryFee: 15.00,
      total: 66.50,
      status: 'pending',
      paymentMethod: 'card',
      paymentStatus: 'paid',
      deliveryAddress: {
        street: '123 Vilakazi Street, Orlando West',
        city: 'Soweto',
        province: 'Gauteng',
        postalCode: '1804',
        coordinates: { latitude: -26.2041, longitude: 27.8650 },
        instructions: 'Ring the bell twice',
      },
      estimatedDeliveryTime: '15 min',
      specialInstructions: 'Please call when you arrive',
      createdAt: '2025-01-23T10:30:00Z',
      updatedAt: '2025-01-23T10:30:00Z',
    },
    {
      id: '2',
      customerId: 'cust2',
      customerName: 'Michael Dlamini',
      customerPhone: '+27 82 345 6789',
      vendorId: 'vendor2',
      vendorName: 'Nandos Maponya Mall',
      vendorAddress: '456 Mall Road, Soweto',
      items: [
        { id: 'item4', name: 'Quarter Chicken', quantity: 1, price: 45.00, total: 45.00 },
        { id: 'item5', name: 'Peri Chips', quantity: 1, price: 18.00, total: 18.00 },
      ],
      subtotal: 63.00,
      deliveryFee: 12.00,
      total: 75.00,
      status: 'accepted',
      paymentMethod: 'cash',
      paymentStatus: 'pending',
      deliveryAddress: {
        street: '456 Klipspruit Valley Road',
        city: 'Soweto',
        province: 'Gauteng',
        postalCode: '1809',
        coordinates: { latitude: -26.2341, longitude: 27.8750 },
      },
      estimatedDeliveryTime: '12 min',
      createdAt: '2025-01-23T11:15:00Z',
      updatedAt: '2025-01-23T11:20:00Z',
    },
  ]



  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Orders
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.gray[600] }]}>
          {orders.length} orders available
        </Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {orders.length > 0 ? (
          orders.map((order) => (
            <OrderCard
              key={order.id}
              order={order}
              onAccept={(orderId) => console.log('Accept order:', orderId)}
              onDecline={(orderId) => console.log('Decline order:', orderId)}
              onViewDetails={(orderId) => console.log('View details:', orderId)}
              onStartPickup={(orderId) => console.log('Start pickup:', orderId)}
              onCompleteDelivery={(orderId) => console.log('Complete delivery:', orderId)}
            />
          ))
        ) : (
          <View style={[styles.emptyState, { backgroundColor: theme.colors.white }]}>
            <Package size={64} color={theme.colors.gray[400]} />
            <Text style={[styles.emptyTitle, { color: theme.colors.gray[900] }]}>
              No Orders Available
            </Text>
            <Text style={[styles.emptySubtitle, { color: theme.colors.gray[600] }]}>
              Turn on your online status to start receiving orders from customers in your area.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
})
