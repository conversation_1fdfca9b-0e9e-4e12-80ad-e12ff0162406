import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  TextInput
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  FilterIcon,
  SearchIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlayIcon,
  PauseIcon,
  PhoneIcon,
  MessageSquareIcon
} from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useOrdersStore } from '../../stores/ordersStore'
import type { VendorOrder } from '../../stores/ordersStore'
import type { OrdersStackScreenProps } from '../../types/navigation'

type Props = OrdersStackScreenProps<'OrdersList'>

export function OrdersScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const {
    orders,
    pendingOrders,
    activeOrders,
    completedOrders,
    fetchOrders,
    acceptOrder,
    declineOrder,
    startOrder,
    completeOrder,
    statusFilter,
    setStatusFilter,
    isLoading
  } = useOrdersStore()

  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTab, setSelectedTab] = useState<'pending' | 'active' | 'completed' | 'all'>('pending')

  useEffect(() => {
    loadOrders()
  }, [])

  const loadOrders = async () => {
    try {
      await fetchOrders()
    } catch (error) {
      console.error('Failed to load orders:', error)
    }
  }

  const onRefresh = async () => {
    setRefreshing(true)
    await loadOrders()
    setRefreshing(false)
  }

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await acceptOrder(orderId)
      Alert.alert('Success', 'Order accepted successfully!')
    } catch (error) {
      Alert.alert('Error', 'Failed to accept order')
    }
  }

  const handleDeclineOrder = async (orderId: string) => {
    Alert.alert(
      'Decline Order',
      'Are you sure you want to decline this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              await declineOrder(orderId, 'Unable to fulfill this order')
              Alert.alert('Order Declined', 'The order has been declined.')
            } catch (error) {
              Alert.alert('Error', 'Failed to decline order')
            }
          }
        }
      ]
    )
  }

  const handleStartOrder = async (orderId: string) => {
    try {
      await startOrder(orderId)
      Alert.alert('Success', 'Order started!')
    } catch (error) {
      Alert.alert('Error', 'Failed to start order')
    }
  }

  const handleCompleteOrder = async (orderId: string) => {
    Alert.alert(
      'Complete Order',
      'Mark this order as completed?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          onPress: async () => {
            try {
              await completeOrder(orderId, 'Order completed successfully')
              Alert.alert('Success', 'Order completed!')
            } catch (error) {
              Alert.alert('Error', 'Failed to complete order')
            }
          }
        }
      ]
    )
  }

  const handleOrderPress = (orderId: string) => {
    navigation.navigate('OrderDetail', { orderId })
  }

  const getFilteredOrders = () => {
    let filteredOrders: VendorOrder[] = []

    switch (selectedTab) {
      case 'pending':
        filteredOrders = pendingOrders
        break
      case 'active':
        filteredOrders = activeOrders
        break
      case 'completed':
        filteredOrders = completedOrders
        break
      default:
        filteredOrders = orders
    }

    if (searchQuery.trim()) {
      filteredOrders = filteredOrders.filter(order =>
        order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.serviceName.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filteredOrders
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    }}>
      <Text style={[typography.headline.large, { color: colors.text.primary, marginBottom: 16 }]}>
        Orders
      </Text>

      {/* Search Bar */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <SearchIcon size={20} color={colors.text.secondary} />
        <TextInput
          style={[
            typography.body.medium,
            {
              flex: 1,
              marginLeft: 12,
              color: colors.text.primary,
            }
          ]}
          placeholder="Search orders..."
          placeholderTextColor={colors.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Tab Navigation */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          {[
            { key: 'pending', label: 'Pending', count: pendingOrders.length },
            { key: 'active', label: 'Active', count: activeOrders.length },
            { key: 'completed', label: 'Completed', count: completedOrders.length },
            { key: 'all', label: 'All', count: orders.length },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={{
                backgroundColor: selectedTab === tab.key ? colors.primary[500] : colors.background.secondary,
                borderRadius: 20,
                paddingHorizontal: 16,
                paddingVertical: 8,
                flexDirection: 'row',
                alignItems: 'center',
              }}
              onPress={() => setSelectedTab(tab.key as any)}
            >
              <Text style={[
                typography.body.small,
                {
                  color: selectedTab === tab.key ? colors.white : colors.text.primary,
                  fontWeight: '600'
                }
              ]}>
                {tab.label}
              </Text>
              {tab.count > 0 && (
                <View style={{
                  backgroundColor: selectedTab === tab.key ? colors.white : colors.primary[500],
                  borderRadius: 10,
                  paddingHorizontal: 6,
                  paddingVertical: 2,
                  marginLeft: 6,
                }}>
                  <Text style={[
                    typography.body.small,
                    {
                      color: selectedTab === tab.key ? colors.primary[500] : colors.white,
                      fontSize: 10,
                      fontWeight: '600'
                    }
                  ]}>
                    {tab.count}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  )

  const renderOrderCard = (order: VendorOrder) => (
    <TouchableOpacity
      key={order.id}
      style={{
        backgroundColor: colors.background.secondary,
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: colors.border.primary,
      }}
      onPress={() => handleOrderPress(order.id)}
    >
      {/* Order Header */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
        <View style={{ flex: 1 }}>
          <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '600' }]}>
            {order.customerName}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.secondary, marginTop: 2 }]}>
            {order.serviceName}
          </Text>
          <Text style={[typography.body.small, { color: colors.text.tertiary, marginTop: 2 }]}>
            {new Date(order.scheduledDate).toLocaleDateString()} at {order.scheduledTime}
          </Text>
        </View>

        <View style={{ alignItems: 'flex-end' }}>
          <View style={{
            backgroundColor: order.status === 'pending' ? colors.warning[100] :
                           order.status === 'accepted' ? colors.info[100] :
                           order.status === 'in_progress' ? colors.primary[100] :
                           order.status === 'completed' ? colors.success[100] : colors.error[100],
            paddingHorizontal: 8,
            paddingVertical: 4,
            borderRadius: 6,
            marginBottom: 4,
          }}>
            <Text style={[
              typography.body.small,
              {
                color: order.status === 'pending' ? colors.warning[700] :
                       order.status === 'accepted' ? colors.info[700] :
                       order.status === 'in_progress' ? colors.primary[700] :
                       order.status === 'completed' ? colors.success[700] : colors.error[700],
                fontWeight: '600'
              }
            ]}>
              {order.status.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
          <Text style={[typography.body.medium, { color: colors.text.primary, fontWeight: '700' }]}>
            R{order.totalAmount}
          </Text>
        </View>
      </View>

      {/* Order Details */}
      <View style={{ marginBottom: 12 }}>
        <Text style={[typography.body.small, { color: colors.text.secondary }]}>
          📍 {order.serviceAddress.street}, {order.serviceAddress.city}
        </Text>
        {order.description && (
          <Text style={[typography.body.small, { color: colors.text.tertiary, marginTop: 4 }]}>
            💬 {order.description}
          </Text>
        )}
      </View>

      {/* Action Buttons */}
      {order.status === 'pending' && (
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: colors.success[500],
              borderRadius: 8,
              padding: 12,
              alignItems: 'center',
            }}
            onPress={() => handleAcceptOrder(order.id)}
          >
            <Text style={[typography.label.small, { color: colors.white, fontWeight: '600' }]}>
              Accept Order
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: colors.background.primary,
              borderRadius: 8,
              padding: 12,
              alignItems: 'center',
              borderWidth: 1,
              borderColor: colors.border.primary,
            }}
            onPress={() => handleDeclineOrder(order.id)}
          >
            <Text style={[typography.label.small, { color: colors.text.primary, fontWeight: '600' }]}>
              Decline
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {order.status === 'accepted' && (
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: colors.primary[500],
              borderRadius: 8,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => handleStartOrder(order.id)}
          >
            <PlayIcon size={16} color={colors.white} />
            <Text style={[typography.label.small, { color: colors.white, marginLeft: 6, fontWeight: '600' }]}>
              Start Work
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.primary,
              borderRadius: 8,
              padding: 12,
              borderWidth: 1,
              borderColor: colors.border.primary,
            }}
          >
            <PhoneIcon size={16} color={colors.text.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.primary,
              borderRadius: 8,
              padding: 12,
              borderWidth: 1,
              borderColor: colors.border.primary,
            }}
          >
            <MessageSquareIcon size={16} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      )}

      {order.status === 'in_progress' && (
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <TouchableOpacity
            style={{
              flex: 1,
              backgroundColor: colors.success[500],
              borderRadius: 8,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => handleCompleteOrder(order.id)}
          >
            <CheckCircleIcon size={16} color={colors.white} />
            <Text style={[typography.label.small, { color: colors.white, marginLeft: 6, fontWeight: '600' }]}>
              Complete
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.primary,
              borderRadius: 8,
              padding: 12,
              borderWidth: 1,
              borderColor: colors.border.primary,
            }}
          >
            <PhoneIcon size={16} color={colors.text.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: colors.background.primary,
              borderRadius: 8,
              padding: 12,
              borderWidth: 1,
              borderColor: colors.border.primary,
            }}
          >
            <MessageSquareIcon size={16} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      )}
    </TouchableOpacity>
  )

  const renderOrdersList = () => {
    const filteredOrders = getFilteredOrders()

    if (filteredOrders.length === 0) {
      return (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 24,
          paddingVertical: 48,
        }}>
          <Text style={[typography.body.large, { color: colors.text.secondary, textAlign: 'center' }]}>
            No orders found
          </Text>
          <Text style={[typography.body.medium, { color: colors.text.tertiary, textAlign: 'center', marginTop: 8 }]}>
            {selectedTab === 'pending' ? 'No pending orders at the moment' :
             selectedTab === 'active' ? 'No active orders right now' :
             selectedTab === 'completed' ? 'No completed orders yet' :
             'No orders available'}
          </Text>
        </View>
      )
    }

    return (
      <View style={{ padding: 16 }}>
        {filteredOrders.map(renderOrderCard)}
      </View>
    )
  }

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      {renderHeader()}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
      >
        {renderOrdersList()}
      </ScrollView>
    </SafeAreaView>
  )
}
