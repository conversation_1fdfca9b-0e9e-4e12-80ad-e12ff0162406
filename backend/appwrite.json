{"projectId": "hvppyplug", "projectName": "HVPPYPlug+", "functions": [{"id": "hvppy-api", "name": "HVPPYPlug+ API", "runtime": "node-18.0", "execute": ["any"], "events": [], "schedule": "", "timeout": 15, "enabled": true, "logging": true, "entrypoint": "src/index.js", "commands": "npm install && npm run build", "ignore": ["node_modules", ".git", ".appwrite"], "path": "backend"}], "databases": [{"id": "hvppyplug-main", "name": "HVPPYPlug+ Main Database", "enabled": true}], "collections": [{"id": "users", "name": "Users", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "attributes": [{"key": "name", "type": "string", "size": 255, "required": true}, {"key": "phone", "type": "string", "size": 20, "required": true}, {"key": "role", "type": "string", "size": 20, "required": true}, {"key": "avatarUrl", "type": "string", "size": 2048, "required": false}], "indexes": [{"key": "phone_index", "type": "unique", "attributes": ["phone"]}, {"key": "role_index", "type": "key", "attributes": ["role"]}]}, {"id": "vendors", "name": "Vend<PERSON>", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "attributes": [{"key": "name", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 1000, "required": false}, {"key": "ownerId", "type": "string", "size": 255, "required": true}, {"key": "location", "type": "string", "size": 1000, "required": true}, {"key": "status", "type": "string", "size": 20, "required": true}, {"key": "categories", "type": "string", "size": 1000, "required": false, "array": true}], "indexes": [{"key": "owner_index", "type": "key", "attributes": ["ownerId"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}]}, {"id": "menu-items", "name": "Menu Items", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "attributes": [{"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "name", "type": "string", "size": 255, "required": true}, {"key": "description", "type": "string", "size": 1000, "required": false}, {"key": "price", "type": "double", "required": true}, {"key": "imageUrl", "type": "string", "size": 2048, "required": false}, {"key": "category", "type": "string", "size": 50, "required": true}, {"key": "available", "type": "boolean", "required": true}], "indexes": [{"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "category_index", "type": "key", "attributes": ["category"]}, {"key": "available_index", "type": "key", "attributes": ["available"]}]}, {"id": "orders", "name": "Orders", "databaseId": "hvppyplug-main", "enabled": true, "documentSecurity": true, "attributes": [{"key": "customerId", "type": "string", "size": 255, "required": true}, {"key": "vendorId", "type": "string", "size": 255, "required": true}, {"key": "runnerId", "type": "string", "size": 255, "required": false}, {"key": "items", "type": "string", "size": 10000, "required": true}, {"key": "subtotal", "type": "double", "required": true}, {"key": "discount", "type": "double", "required": false}, {"key": "total", "type": "double", "required": true}, {"key": "status", "type": "string", "size": 50, "required": true}, {"key": "paymentMethod", "type": "string", "size": 50, "required": true}, {"key": "paymentStatus", "type": "string", "size": 50, "required": true}], "indexes": [{"key": "customer_index", "type": "key", "attributes": ["customerId"]}, {"key": "vendor_index", "type": "key", "attributes": ["vendorId"]}, {"key": "runner_index", "type": "key", "attributes": ["runnerId"]}, {"key": "status_index", "type": "key", "attributes": ["status"]}]}], "buckets": [{"id": "images", "name": "Images", "permissions": ["read(\"any\")"], "fileSecurity": true, "enabled": true, "maximumFileSize": 50000000, "allowedFileExtensions": ["jpg", "jpeg", "png", "webp"], "compression": "gzip", "encryption": true, "antivirus": true}]}