// Polar Payment Provider Implementation

import type {
  PaymentProvider,
  PaymentRequest,
  PaymentResult,
  RefundRequest,
  RefundResult,
  PaymentIntent,
  PaymentStatus,
  Currency,
  PolarConfig
} from '../../types';
import { PaymentErrorFactory } from '../../types/errors';
import { PolarAPI } from './api';
import { PolarWebhookHandler } from './webhooks';
import { PolarValidator } from './validator';

export class PolarPaymentProvider implements PaymentProvider {
  readonly type = 'polar' as const;
  readonly name = 'Polar';
  readonly supportedCurrencies: Currency[] = ['USD', 'EUR', 'GBP', 'ZAR', 'CAD', 'AUD'];
  readonly testMode: boolean;

  private api: PolarAPI;
  private webhookHandler: PolarWebhookHandler;
  private validator: PolarValidator;

  constructor(private config: PolarConfig) {
    this.testMode = config.testMode;
    this.api = new PolarAPI(config);
    this.webhookHandler = new PolarWebhookHandler(config);
    this.validator = new PolarValidator();
  }

  async createPaymentIntent(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // Validate request
      const validation = this.validator.validatePaymentRequest(request);
      if (!validation.isValid) {
        throw PaymentErrorFactory.createValidationError(
          'INVALID_PAYMENT_METHOD',
          `Invalid payment request: ${validation.errors.map(e => e.message).join(', ')}`
        );
      }

      // Create or get customer
      let customer: any;
      try {
        customer = await this.api.getCustomer(request.customerId);
      } catch {
        // Customer doesn't exist, create one
        customer = await this.api.createCustomer({
          id: request.customerId,
          metadata: {
            orderId: request.orderId,
            source: 'hvppyplug'
          }
        });
      }

      // Create checkout session
      const checkoutSession = await this.api.createCheckoutSession({
        customer_id: customer.id,
        success_url: request.returnUrl || this.config.successUrl || 'https://hvppyplug.co.za/payment/success',
        cancel_url: request.cancelUrl || this.config.cancelUrl || 'https://hvppyplug.co.za/payment/cancel',
        metadata: {
          customerId: request.customerId,
          orderId: request.orderId,
          amount: request.amount.value.toString(),
          currency: request.amount.currency,
          ...request.metadata
        }
      });

      // Convert to our PaymentIntent format
      const paymentIntent: PaymentIntent = {
        id: checkoutSession.id,
        amount: request.amount,
        status: 'pending',
        paymentMethod: 'polar_checkout',
        customerId: request.customerId,
        orderId: request.orderId,
        description: request.description || undefined,
        metadata: request.metadata || undefined,
        createdAt: new Date(checkoutSession.created_at),
        updatedAt: new Date(checkoutSession.updated_at)
      };

      return {
        success: true,
        paymentIntent,
        requiresAction: true,
        actionUrl: checkoutSession.url,
        providerResponse: checkoutSession
      };

    } catch (error: any) {
      return {
        success: false,
        paymentIntent: this.createFailedIntent(request),
        error: error.message || 'Failed to create payment intent'
      };
    }
  }

  async confirmPayment(paymentIntentId: string): Promise<PaymentResult> {
    try {
      // For Polar, confirmation happens via the checkout URL
      // We check the order status instead
      const order = await this.api.getOrderByCheckoutId(paymentIntentId);

      const paymentIntent: PaymentIntent = {
        id: order.checkout_id || paymentIntentId,
        amount: {
          value: order.amount / 100, // Polar stores in cents
          currency: order.currency.toUpperCase() as any,
          formatted: this.formatAmount(order.amount, order.currency)
        },
        status: this.mapPolarStatus(order.status),
        paymentMethod: 'polar_checkout',
        customerId: order.customer_id,
        orderId: order.metadata?.orderId || '',
        description: `Order ${order.id}`,
        metadata: order.metadata || undefined,
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at)
      };

      return {
        success: order.status === 'succeeded',
        paymentIntent,
        transactionId: order.id,
        providerResponse: order
      };

    } catch (error: any) {
      throw PaymentErrorFactory.createProviderError(
        'POLAR_ERROR',
        error.message || 'Failed to confirm payment',
        'polar',
        error.code,
        error.message,
        error
      );
    }
  }

  async cancelPayment(paymentIntentId: string): Promise<PaymentResult> {
    try {
      // Polar doesn't have a direct cancel API for checkout sessions
      // We'll mark it as cancelled in our system
      const paymentIntent: PaymentIntent = {
        id: paymentIntentId,
        amount: { value: 0, currency: 'ZAR', formatted: 'R0.00' },
        status: 'cancelled',
        paymentMethod: 'polar_checkout',
        customerId: '',
        orderId: '',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return {
        success: true,
        paymentIntent,
        transactionId: paymentIntentId
      };

    } catch (error: any) {
      throw PaymentErrorFactory.createProviderError(
        'POLAR_ERROR',
        error.message || 'Failed to cancel payment',
        'polar',
        error.code,
        error.message,
        error
      );
    }
  }

  async refundPayment(request: RefundRequest): Promise<RefundResult> {
    try {
      // Get the order first
      const order = await this.api.getOrderByCheckoutId(request.paymentIntentId);
      
      // Create refund
      const refundAmount = request.amount ? Math.round(request.amount.value * 100) : undefined;
      const refund = await this.api.createRefund({
        order_id: order.id,
        ...(refundAmount !== undefined && { amount: refundAmount }),
        reason: request.reason || 'requested_by_customer'
      });

      return {
        success: true,
        refundId: refund.id,
        amount: request.amount || {
          value: order.amount / 100,
          currency: order.currency.toUpperCase() as any,
          formatted: this.formatAmount(order.amount, order.currency)
        },
        status: 'succeeded'
      };

    } catch (error: any) {
      return {
        success: false,
        refundId: '',
        amount: request.amount || { value: 0, currency: 'ZAR', formatted: 'R0.00' },
        status: 'failed',
        error: error.message || 'Failed to process refund'
      };
    }
  }

  async getPaymentStatus(paymentIntentId: string): Promise<PaymentIntent> {
    try {
      const order = await this.api.getOrderByCheckoutId(paymentIntentId);

      return {
        id: order.checkout_id || paymentIntentId,
        amount: {
          value: order.amount / 100,
          currency: order.currency.toUpperCase() as any,
          formatted: this.formatAmount(order.amount, order.currency)
        },
        status: this.mapPolarStatus(order.status),
        paymentMethod: 'polar_checkout',
        customerId: order.customer_id,
        orderId: order.metadata?.orderId || '',
        description: `Order ${order.id}`,
        metadata: order.metadata || undefined,
        createdAt: new Date(order.created_at),
        updatedAt: new Date(order.updated_at)
      };

    } catch (error: any) {
      throw PaymentErrorFactory.createProviderError(
        'PAYMENT_NOT_FOUND',
        `Payment intent not found: ${paymentIntentId}`,
        'polar',
        error.code,
        error.message,
        error
      );
    }
  }

  verifyWebhook(payload: string, signature: string): boolean {
    return this.webhookHandler.verifySignature(payload, signature);
  }

  async handleWebhook(payload: any): Promise<void> {
    await this.webhookHandler.handleEvent(payload);
  }

  async validatePaymentMethod(): Promise<boolean> {
    // Polar handles payment method validation in their checkout
    return true;
  }

  async getPaymentMethodDetails(): Promise<any> {
    // Polar doesn't expose payment method details for security
    return null;
  }

  // Private helper methods
  private mapPolarStatus(polarStatus: string): PaymentStatus {
    const statusMap: Record<string, PaymentStatus> = {
      'pending': 'pending',
      'processing': 'processing',
      'succeeded': 'succeeded',
      'failed': 'failed',
      'canceled': 'cancelled'
    };

    return statusMap[polarStatus] || 'failed';
  }

  private formatAmount(amount: number, currency: string): string {
    const value = amount / 100;
    const formatter = new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency.toUpperCase()
    });
    return formatter.format(value);
  }

  private createFailedIntent(request: PaymentRequest): PaymentIntent {
    return {
      id: '',
      amount: request.amount,
      status: 'failed',
      paymentMethod: 'polar_checkout',
      customerId: request.customerId,
      orderId: request.orderId,
      description: request.description || undefined,
      metadata: request.metadata || undefined,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}

export * from './api';
export * from './webhooks';
export * from './validator';
