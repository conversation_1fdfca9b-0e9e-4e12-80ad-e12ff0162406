import React from 'react'
import { View, Text, ViewStyle, TextStyle } from 'react-native'
import { useTheme } from '../../providers/ThemeProvider'

export interface BadgeProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'neutral'
  size?: 'sm' | 'md' | 'lg'
  shape?: 'rounded' | 'pill' | 'square'
  style?: ViewStyle
  textStyle?: TextStyle
  testID?: string
}

export function Badge({
  children,
  variant = 'primary',
  size = 'md',
  shape = 'rounded',
  style,
  textStyle,
  testID,
}: BadgeProps) {
  const { theme } = useTheme()

  const getBadgeStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      alignSelf: 'flex-start',
      alignItems: 'center',
      justifyContent: 'center',
    }

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        paddingHorizontal: theme.spacing.xs,
        paddingVertical: 2,
        minHeight: 20,
      },
      md: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        minHeight: 24,
      },
      lg: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        minHeight: 32,
      },
    }

    // Shape styles
    const shapeStyles: Record<string, ViewStyle> = {
      rounded: {
        borderRadius: theme.borderRadius.sm,
      },
      pill: {
        borderRadius: theme.borderRadius.full,
      },
      square: {
        borderRadius: 0,
      },
    }

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: theme.colors.primary[100],
      },
      secondary: {
        backgroundColor: theme.colors.secondary[100],
      },
      success: {
        backgroundColor: theme.colors.success[100],
      },
      warning: {
        backgroundColor: theme.colors.warning[100],
      },
      error: {
        backgroundColor: theme.colors.error[100],
      },
      info: {
        backgroundColor: theme.colors.secondary[100],
      },
      neutral: {
        backgroundColor: theme.colors.gray[100],
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...shapeStyles[shape],
      ...variantStyles[variant],
      ...style,
    }
  }

  const getTextStyles = (): TextStyle => {
    const baseStyles: TextStyle = {
      fontWeight: theme.fontWeight.medium,
      textAlign: 'center',
    }

    // Size styles
    const sizeStyles: Record<string, TextStyle> = {
      sm: {
        fontSize: theme.fontSize.xs,
      },
      md: {
        fontSize: theme.fontSize.sm,
      },
      lg: {
        fontSize: theme.fontSize.base,
      },
    }

    // Variant styles
    const variantStyles: Record<string, TextStyle> = {
      primary: {
        color: theme.colors.primary[700],
      },
      secondary: {
        color: theme.colors.secondary[700],
      },
      success: {
        color: theme.colors.success[700],
      },
      warning: {
        color: theme.colors.warning[700],
      },
      error: {
        color: theme.colors.error[700],
      },
      info: {
        color: theme.colors.secondary[700],
      },
      neutral: {
        color: theme.colors.gray[700],
      },
    }

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...textStyle,
    }
  }

  return (
    <View style={getBadgeStyles()} testID={testID}>
      {typeof children === 'string' ? (
        <Text style={getTextStyles()}>{children}</Text>
      ) : (
        children
      )}
    </View>
  )
}

// Predefined status badges for common use cases
export interface StatusBadgeProps {
  status: 'pending' | 'accepted' | 'picked_up' | 'en_route' | 'delivered' | 'cancelled'
  size?: 'sm' | 'md' | 'lg'
  style?: ViewStyle
}

export function StatusBadge({ status, size = 'md', style }: StatusBadgeProps) {
  const statusConfig = {
    pending: { variant: 'warning' as const, label: 'Pending' },
    accepted: { variant: 'info' as const, label: 'Accepted' },
    picked_up: { variant: 'primary' as const, label: 'Picked Up' },
    en_route: { variant: 'secondary' as const, label: 'En Route' },
    delivered: { variant: 'success' as const, label: 'Delivered' },
    cancelled: { variant: 'error' as const, label: 'Cancelled' },
  }

  const config = statusConfig[status]

  return (
    <Badge variant={config.variant} size={size} shape="pill" style={style}>
      {config.label}
    </Badge>
  )
}

// Priority badge for orders
export interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high' | 'urgent'
  size?: 'sm' | 'md' | 'lg'
  style?: ViewStyle
}

export function PriorityBadge({ priority, size = 'sm', style }: PriorityBadgeProps) {
  const priorityConfig = {
    low: { variant: 'neutral' as const, label: 'Low' },
    medium: { variant: 'info' as const, label: 'Medium' },
    high: { variant: 'warning' as const, label: 'High' },
    urgent: { variant: 'error' as const, label: 'Urgent' },
  }

  const config = priorityConfig[priority]

  return (
    <Badge variant={config.variant} size={size} shape="pill" style={style}>
      {config.label}
    </Badge>
  )
}
