#!/usr/bin/env node

const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const inquirer = require('inquirer')

const ROOT_DIR = path.resolve(__dirname, '..')

// Colors for console output
const colors = {
  info: chalk.blue,
  success: chalk.green,
  warning: chalk.yellow,
  error: chalk.red,
  dim: chalk.dim,
}

// Log with timestamp
function log(message, color = 'info') {
  const timestamp = new Date().toLocaleTimeString()
  console.log(`${colors.dim(`[${timestamp}]`)} ${colors[color](message)}`)
}

// Component templates
const templates = {
  component: (name, category) => `"use client"

import React from 'react'
import { View } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text } from '@gluestack-ui/themed'

// Types
export interface ${name}Props {
  children?: React.ReactNode
  className?: string
  disabled?: boolean
}

// Styles
const containerStyles = tva({
  base: "bg-background-0",
  variants: {
    disabled: {
      true: "opacity-50",
    },
  },
})

export function ${name}({
  children,
  className,
  disabled = false,
}: ${name}Props) {
  return (
    <View className={containerStyles({ disabled, class: className })}>
      <Text className="text-typography-900">
        ${name} Component
      </Text>
      {children}
    </View>
  )
}
`,

  test: (name, category) => `import React from 'react'
import { render } from '@testing-library/react-native'
import { ${name} } from '../${name}'

describe('${name}', () => {
  it('renders correctly', () => {
    const { getByText } = render(<${name} />)
    expect(getByText('${name} Component')).toBeTruthy()
  })

  it('applies custom className', () => {
    const { getByTestId } = render(
      <${name} className="custom-class" testID="${name.toLowerCase()}" />
    )
    expect(getByTestId('${name.toLowerCase()}')).toBeTruthy()
  })

  it('handles disabled state', () => {
    const { getByTestId } = render(
      <${name} disabled testID="${name.toLowerCase()}" />
    )
    expect(getByTestId('${name.toLowerCase()}')).toBeTruthy()
  })
})
`,

  story: (name, category) => `import type { Meta, StoryObj } from '@storybook/react-native'
import { ${name} } from './${name}'

const meta: Meta<typeof ${name}> = {
  title: '${category}/${name}',
  component: ${name},
  parameters: {
    docs: {
      description: {
        component: '${name} component description.',
      },
    },
  },
  argTypes: {
    disabled: {
      control: 'boolean',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
}

export const WithChildren: Story = {
  args: {
    children: 'Custom content',
  },
}
`,

  index: (name) => `export * from './${name}'
`,

  docs: (name, category) => `# ${name}

${name} component for ${category.toLowerCase()} functionality.

## Usage

\`\`\`tsx
import { ${name} } from '@hvppyplug/mobile-services'

function Example() {
  return (
    <${name}>
      Content here
    </${name}>
  )
}
\`\`\`

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| children | ReactNode | - | Child components |
| className | string | - | Additional CSS classes |
| disabled | boolean | false | Whether the component is disabled |

## Examples

### Basic Usage

\`\`\`tsx
<${name} />
\`\`\`

### Disabled State

\`\`\`tsx
<${name} disabled />
\`\`\`

### With Custom Styling

\`\`\`tsx
<${name} className="custom-styles" />
\`\`\`
`
}

// Prompt for component details
async function promptComponentDetails() {
  const answers = await inquirer.prompt([
    {
      type: 'input',
      name: 'name',
      message: 'Component name (PascalCase):',
      validate: (input) => {
        if (!input) return 'Component name is required'
        if (!/^[A-Z][a-zA-Z0-9]*$/.test(input)) {
          return 'Component name must be in PascalCase (e.g., MyComponent)'
        }
        return true
      }
    },
    {
      type: 'list',
      name: 'category',
      message: 'Component category:',
      choices: [
        { name: 'Data Management', value: 'data' },
        { name: 'Forms', value: 'forms' },
        { name: 'Layouts', value: 'layouts' },
        { name: 'Business Logic', value: 'business' },
        { name: 'Interactive', value: 'interactive' },
        { name: 'Feedback', value: 'feedback' },
        { name: 'Navigation', value: 'navigation' },
      ]
    },
    {
      type: 'checkbox',
      name: 'files',
      message: 'Files to generate:',
      choices: [
        { name: 'Component file', value: 'component', checked: true },
        { name: 'Test file', value: 'test', checked: true },
        { name: 'Storybook story', value: 'story', checked: true },
        { name: 'Documentation', value: 'docs', checked: true },
        { name: 'Index file', value: 'index', checked: true },
      ]
    },
    {
      type: 'confirm',
      name: 'updateExports',
      message: 'Update main index.ts exports?',
      default: true
    }
  ])

  return answers
}

// Generate component files
async function generateComponent(details) {
  const { name, category, files, updateExports } = details
  const componentDir = path.join(ROOT_DIR, 'src', category, name)
  
  log(`Creating component directory: ${componentDir}`, 'info')
  await fs.ensureDir(componentDir)
  
  // Generate requested files
  for (const fileType of files) {
    const fileName = getFileName(fileType, name)
    const filePath = path.join(componentDir, fileName)
    const content = templates[fileType](name, category)
    
    await fs.writeFile(filePath, content)
    log(`✓ Created ${fileName}`, 'success')
  }
  
  // Create __tests__ directory if test file was generated
  if (files.includes('test')) {
    const testDir = path.join(componentDir, '__tests__')
    await fs.ensureDir(testDir)
    
    const testFile = path.join(testDir, `${name}.test.tsx`)
    const testContent = templates.test(name, category)
    
    await fs.writeFile(testFile, testContent)
    log(`✓ Created __tests__/${name}.test.tsx`, 'success')
  }
  
  // Update category index file
  await updateCategoryIndex(category, name)
  
  // Update main index file
  if (updateExports) {
    await updateMainIndex(category, name)
  }
  
  log(`🎉 Component ${name} generated successfully!`, 'success')
}

// Get file name for file type
function getFileName(fileType, name) {
  switch (fileType) {
    case 'component':
      return `${name}.tsx`
    case 'test':
      return `${name}.test.tsx`
    case 'story':
      return `${name}.stories.tsx`
    case 'docs':
      return `${name}.md`
    case 'index':
      return 'index.ts'
    default:
      return `${name}.tsx`
  }
}

// Update category index file
async function updateCategoryIndex(category, name) {
  const categoryDir = path.join(ROOT_DIR, 'src', category)
  const indexPath = path.join(categoryDir, 'index.ts')
  
  let indexContent = ''
  if (await fs.pathExists(indexPath)) {
    indexContent = await fs.readFile(indexPath, 'utf8')
  }
  
  const exportLine = `export * from './${name}/${name}'\n`
  
  if (!indexContent.includes(exportLine)) {
    indexContent += exportLine
    await fs.writeFile(indexPath, indexContent)
    log(`✓ Updated ${category}/index.ts`, 'success')
  }
}

// Update main index file
async function updateMainIndex(category, name) {
  const mainIndexPath = path.join(ROOT_DIR, 'src', 'index.ts')
  
  if (!(await fs.pathExists(mainIndexPath))) {
    log('Main index.ts not found, skipping update', 'warning')
    return
  }
  
  let content = await fs.readFile(mainIndexPath, 'utf8')
  
  // Check if category export already exists
  const categoryExportLine = `export * from './${category}'`
  
  if (!content.includes(categoryExportLine)) {
    // Add category export
    const categoryComments = {
      data: '// Data Management Components',
      forms: '// Form Components',
      layouts: '// Layout Components', 
      business: '// Business Logic Components',
      interactive: '// Interactive Components',
      feedback: '// Feedback Components',
      navigation: '// Navigation Components',
    }
    
    const comment = categoryComments[category] || `// ${category} Components`
    const newExport = `${comment}\n${categoryExportLine}\n\n`
    
    // Insert before the last section (usually provider/hooks/utils)
    const insertIndex = content.lastIndexOf('// Provider')
    if (insertIndex !== -1) {
      content = content.slice(0, insertIndex) + newExport + content.slice(insertIndex)
    } else {
      content += newExport
    }
    
    await fs.writeFile(mainIndexPath, content)
    log('✓ Updated main index.ts', 'success')
  }
}

// Main function
async function main() {
  try {
    log('🚀 Component Generator', 'info')
    log('This will help you create a new compound component', 'dim')
    
    const details = await promptComponentDetails()
    await generateComponent(details)
    
    log('', 'info')
    log('Next steps:', 'info')
    log('1. Implement your component logic', 'dim')
    log('2. Write comprehensive tests', 'dim')
    log('3. Add Storybook stories', 'dim')
    log('4. Update documentation', 'dim')
    log('5. Run tests: npm run test', 'dim')
    log('6. Build: npm run build', 'dim')
    
  } catch (error) {
    log(`❌ Error: ${error.message}`, 'error')
    process.exit(1)
  }
}

main()
