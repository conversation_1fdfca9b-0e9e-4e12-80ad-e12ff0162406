import React, { useState, useEffect, ReactNode } from 'react'
import { useColorScheme } from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage'

import { ThemeContext } from '../hooks/useTheme'
import { theme } from '../theme'

interface ThemeProviderProps {
  children: ReactNode
}

const THEME_STORAGE_KEY = '@hvppyplug_theme_preference'

export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme()
  const [isDark, setIsDark] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Load theme preference on mount
  useEffect(() => {
    loadThemePreference()
  }, [])

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY)
      if (savedTheme !== null) {
        setIsDark(savedTheme === 'dark')
      } else {
        // Use system preference if no saved preference
        setIsDark(systemColorScheme === 'dark')
      }
    } catch (error) {
      console.error('Error loading theme preference:', error)
      setIsDark(systemColorScheme === 'dark')
    } finally {
      setIsLoading(false)
    }
  }

  const toggleTheme = async () => {
    try {
      const newTheme = !isDark
      setIsDark(newTheme)
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme ? 'dark' : 'light')
    } catch (error) {
      console.error('Error saving theme preference:', error)
    }
  }

  // Don't render children until theme is loaded
  if (isLoading) {
    return null
  }

  const contextValue = {
    theme,
    isDark,
    toggleTheme,
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}
