import { NotificationService } from '@hvppyplug/mobile-services'

class RunnerNotificationService {
  private notificationService: NotificationService
  private isInitialized = false

  constructor() {
    this.notificationService = NotificationService.getInstance()
  }

  async initialize() {
    if (this.isInitialized) return

    try {
      await this.notificationService.initialize({
        enableAppwriteSync: true,
        onNotificationReceived: this.handleNotificationReceived,
        onNotificationOpened: this.handleNotificationOpened,
      })

      this.isInitialized = true
      console.log('✅ Runner notification service initialized')
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error)
      throw error
    }
  }

  private handleNotificationReceived = (notification: any) => {
    console.log('📱 Notification received:', notification)
    
    // Handle different notification types
    switch (notification.data?.type) {
      case 'new_order':
        this.handleNewOrderNotification(notification)
        break
      case 'order_cancelled':
        this.handleOrderCancelledNotification(notification)
        break
      case 'payment_received':
        this.handlePaymentReceivedNotification(notification)
        break
      default:
        console.log('📱 General notification received')
    }
  }

  private handleNotificationOpened = (notification: any) => {
    console.log('👆 Notification opened:', notification)
    
    // Navigate to appropriate screen based on notification type
    // This will be implemented when navigation is set up
  }

  private handleNewOrderNotification = (notification: any) => {
    // Play special sound for new orders
    // Show high-priority notification
    console.log('🆕 New order notification:', notification.data?.orderId)
  }

  private handleOrderCancelledNotification = (notification: any) => {
    console.log('❌ Order cancelled notification:', notification.data?.orderId)
  }

  private handlePaymentReceivedNotification = (notification: any) => {
    console.log('💰 Payment received notification:', notification.data?.amount)
  }

  // Send notification for order status updates
  async sendOrderStatusNotification(orderId: string, status: string, customerName: string) {
    const statusMessages = {
      accepted: `✅ Order accepted! Heading to pick up from vendor.`,
      picked_up: `📦 Order picked up! On the way to ${customerName}.`,
      en_route: `🚚 Order is on the way to ${customerName}.`,
      delivered: `🎉 Order delivered successfully to ${customerName}!`,
    }

    const message = statusMessages[status as keyof typeof statusMessages] || `Order status updated: ${status}`

    return this.notificationService.scheduleLocalNotification({
      title: 'Order Update',
      body: message,
      data: {
        type: 'order_status_update',
        orderId,
        status,
      },
      categoryId: 'order',
      priority: 'high',
    })
  }

  // Send notification for earnings update
  async sendEarningsNotification(amount: number, orderId: string) {
    return this.notificationService.scheduleLocalNotification({
      title: 'Payment Received! 💰',
      body: `You earned R${amount.toFixed(2)} for order #${orderId.slice(-6)}`,
      data: {
        type: 'earnings_update',
        amount,
        orderId,
      },
      categoryId: 'earnings',
      priority: 'normal',
    })
  }

  // Get push token for Appwrite integration
  async getPushToken() {
    return this.notificationService.getPushToken()
  }

  // Request notification permissions
  async requestPermissions() {
    return this.notificationService.requestPermissions()
  }
}

export const notificationService = new RunnerNotificationService()
