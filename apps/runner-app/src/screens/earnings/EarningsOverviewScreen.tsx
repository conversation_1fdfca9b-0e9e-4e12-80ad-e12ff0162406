import React, { useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { DollarSign, TrendingUp, Calendar, Clock } from 'lucide-react-native'

import { useTheme } from '../../providers/ThemeProvider'

type TimePeriod = 'today' | 'week' | 'month'

export function EarningsOverviewScreen() {
  const { theme } = useTheme()
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('today')

  // Mock earnings data
  const earningsData = {
    today: {
      total: 245.50,
      deliveries: 8,
      hours: 6.5,
      avgPerDelivery: 30.69,
    },
    week: {
      total: 1456.80,
      deliveries: 42,
      hours: 28.5,
      avgPerDelivery: 34.69,
    },
    month: {
      total: 5234.20,
      deliveries: 156,
      hours: 98.2,
      avgPerDelivery: 33.55,
    },
  }

  const currentData = earningsData[selectedPeriod]

  const periods = [
    { key: 'today' as TimePeriod, label: 'Today' },
    { key: 'week' as TimePeriod, label: 'This Week' },
    { key: 'month' as TimePeriod, label: 'This Month' },
  ]

  const recentEarnings = [
    {
      id: '1',
      orderId: '#ORD-001',
      customerName: 'Sarah Johnson',
      amount: 45.50,
      tip: 5.00,
      date: '2025-01-23',
      time: '14:30',
    },
    {
      id: '2',
      orderId: '#ORD-002',
      customerName: 'Michael Dlamini',
      amount: 67.20,
      tip: 8.00,
      date: '2025-01-23',
      time: '13:15',
    },
    {
      id: '3',
      orderId: '#ORD-003',
      customerName: 'Nomsa Mthembu',
      amount: 32.80,
      tip: 3.50,
      date: '2025-01-23',
      time: '12:45',
    },
  ]

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.gray[50] }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.white }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.gray[900] }]}>
          Earnings
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.gray[600] }]}>
          Track your delivery income
        </Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {periods.map((period) => (
            <TouchableOpacity
              key={period.key}
              style={[
                styles.periodButton,
                {
                  backgroundColor: selectedPeriod === period.key
                    ? theme.colors.primary[600]
                    : theme.colors.white,
                  borderColor: selectedPeriod === period.key
                    ? theme.colors.primary[600]
                    : theme.colors.gray[300],
                }
              ]}
              onPress={() => setSelectedPeriod(period.key)}
            >
              <Text style={[
                styles.periodButtonText,
                {
                  color: selectedPeriod === period.key
                    ? theme.colors.white
                    : theme.colors.gray[700],
                }
              ]}>
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Earnings Summary */}
        <View style={[styles.summaryCard, { backgroundColor: theme.colors.primary[600] }]}>
          <View style={styles.summaryHeader}>
            <Text style={[styles.summaryTitle, { color: theme.colors.white }]}>
              Total Earnings
            </Text>
            <TrendingUp size={24} color={theme.colors.white} />
          </View>
          
          <Text style={[styles.summaryAmount, { color: theme.colors.white }]}>
            R{currentData.total.toFixed(2)}
          </Text>
          
          <Text style={[styles.summaryPeriod, { color: theme.colors.primary[100] }]}>
            {periods.find(p => p.key === selectedPeriod)?.label}
          </Text>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
            <DollarSign size={24} color={theme.colors.success[600]} />
            <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
              R{currentData.avgPerDelivery.toFixed(2)}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
              Avg per Delivery
            </Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
            <Calendar size={24} color={theme.colors.primary[600]} />
            <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
              {currentData.deliveries}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
              Deliveries
            </Text>
          </View>

          <View style={[styles.statCard, { backgroundColor: theme.colors.white }]}>
            <Clock size={24} color={theme.colors.secondary[600]} />
            <Text style={[styles.statValue, { color: theme.colors.gray[900] }]}>
              {currentData.hours}h
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.gray[600] }]}>
              Hours Worked
            </Text>
          </View>
        </View>

        {/* Recent Earnings */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.gray[900] }]}>
              Recent Earnings
            </Text>
            <TouchableOpacity>
              <Text style={[styles.seeAllText, { color: theme.colors.primary[600] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>

          {recentEarnings.map((earning) => (
            <View
              key={earning.id}
              style={[styles.earningCard, { backgroundColor: theme.colors.white }]}
            >
              <View style={styles.earningHeader}>
                <View>
                  <Text style={[styles.earningOrderId, { color: theme.colors.gray[900] }]}>
                    {earning.orderId}
                  </Text>
                  <Text style={[styles.earningCustomer, { color: theme.colors.gray[600] }]}>
                    {earning.customerName}
                  </Text>
                </View>
                
                <View style={styles.earningTime}>
                  <Text style={[styles.earningDate, { color: theme.colors.gray[600] }]}>
                    {earning.time}
                  </Text>
                </View>
              </View>

              <View style={styles.earningFooter}>
                <View style={styles.earningAmounts}>
                  <Text style={[styles.earningAmount, { color: theme.colors.gray[900] }]}>
                    R{earning.amount.toFixed(2)}
                  </Text>
                  {earning.tip > 0 && (
                    <Text style={[styles.earningTip, { color: theme.colors.success[600] }]}>
                      +R{earning.tip.toFixed(2)} tip
                    </Text>
                  )}
                </View>
                
                <Text style={[styles.earningTotal, { color: theme.colors.primary[600] }]}>
                  R{(earning.amount + earning.tip).toFixed(2)}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Payout Info */}
        <View style={[styles.payoutCard, { backgroundColor: theme.colors.white }]}>
          <Text style={[styles.payoutTitle, { color: theme.colors.gray[900] }]}>
            Next Payout
          </Text>
          <Text style={[styles.payoutSubtitle, { color: theme.colors.gray[600] }]}>
            Your earnings will be paid out every Friday
          </Text>
          <TouchableOpacity
            style={[styles.payoutButton, { backgroundColor: theme.colors.primary[600] }]}
          >
            <Text style={[styles.payoutButtonText, { color: theme.colors.white }]}>
              View Payout Details
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  periodSelector: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  summaryCard: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  summaryAmount: {
    fontSize: 36,
    fontWeight: '700',
    marginBottom: 4,
  },
  summaryPeriod: {
    fontSize: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  seeAllText: {
    fontSize: 16,
    fontWeight: '600',
  },
  earningCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  earningHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  earningOrderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  earningCustomer: {
    fontSize: 14,
  },
  earningTime: {
    alignItems: 'flex-end',
  },
  earningDate: {
    fontSize: 14,
  },
  earningFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  earningAmounts: {
    flex: 1,
  },
  earningAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  earningTip: {
    fontSize: 12,
    marginTop: 2,
  },
  earningTotal: {
    fontSize: 18,
    fontWeight: '700',
  },
  payoutCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  payoutTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  payoutSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  payoutButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  payoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
})
