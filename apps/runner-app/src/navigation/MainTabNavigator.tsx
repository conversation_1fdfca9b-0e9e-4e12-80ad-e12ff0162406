import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { 
  Home, 
  Package, 
  DollarSign, 
  User,
  MapPin,
  Clock
} from 'lucide-react-native'

import type { MainTabParamList } from '../types/navigation'
import { useTheme } from '../providers/ThemeProvider'

// Import Stack Navigators (will be created next)
import { DashboardStackNavigator } from './DashboardStackNavigator'
import { OrdersStackNavigator } from './OrdersStackNavigator'
import { EarningsStackNavigator } from './EarningsStackNavigator'
import { ProfileStackNavigator } from './ProfileStackNavigator'

const Tab = createBottomTabNavigator<MainTabParamList>()

export function MainTabNavigator() {
  const { theme } = useTheme()

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarActiveTintColor: theme.colors.primary[600],
        tabBarInactiveTintColor: theme.colors.gray[500],
        tabBarStyle: {
          backgroundColor: theme.colors.white,
          borderTopColor: theme.colors.gray[200],
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: 8,
          height: 80,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarIcon: ({ focused, color, size }) => {
          let IconComponent
          
          switch (route.name) {
            case 'Dashboard':
              IconComponent = Home
              break
            case 'Orders':
              IconComponent = Package
              break
            case 'Earnings':
              IconComponent = DollarSign
              break
            case 'Profile':
              IconComponent = User
              break
            default:
              IconComponent = Home
          }

          return <IconComponent size={size} color={color} />
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardStackNavigator}
        options={{
          tabBarLabel: 'Dashboard',
          tabBarBadge: undefined, // Will be set dynamically for new orders
        }}
      />
      <Tab.Screen
        name="Orders"
        component={OrdersStackNavigator}
        options={{
          tabBarLabel: 'Orders',
        }}
      />
      <Tab.Screen
        name="Earnings"
        component={EarningsStackNavigator}
        options={{
          tabBarLabel: 'Earnings',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  )
}
