import React from 'react'
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { useNavigation } from '@react-navigation/native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Onboarding'>

export function OnboardingScreen({ navigation }: Props) {
  const { colors, typography } = useTheme()
  const { completeOnboarding } = useAuthStore()

  const handleGetStarted = () => {
    completeOnboarding()
    navigation.replace('Login')
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <View style={styles.content}>
        <Text style={[typography.display.medium, { color: colors.text.primary, textAlign: 'center' }]}>
          Welcome to HVPPYPlug+
        </Text>
        <Text style={[typography.body.large, { color: colors.text.secondary, textAlign: 'center', marginTop: 16 }]}>
          Your one-stop solution for all home and business services
        </Text>
        
        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.primary[500] }]}
          onPress={handleGetStarted}
        >
          <Text style={[typography.label.large, { color: colors.white }]}>
            Get Started
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  button: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
    marginTop: 32,
  },
})
