import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'

import type { OrdersStackParamList } from '../types/navigation'

// Import Screens
import { OrdersScreen } from '../screens/orders/OrdersScreen'
import { OrderDetailScreen } from '../screens/orders/OrderDetailScreen'
import { OrderHistoryScreen } from '../screens/orders/OrderHistoryScreen'
import { ActiveOrdersScreen } from '../screens/orders/ActiveOrdersScreen'

const Stack = createNativeStackNavigator<OrdersStackParamList>()

export function OrdersStackNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen
        name="OrdersMain"
        component={OrdersScreen}
      />
      <Stack.Screen
        name="OrderDetail"
        component={OrderDetailScreen}
        options={{
          headerShown: true,
          title: 'Order Details',
        }}
      />
      <Stack.Screen
        name="OrderHistory"
        component={OrderHistoryScreen}
        options={{
          headerShown: true,
          title: 'Order History',
        }}
      />
      <Stack.Screen
        name="ActiveOrders"
        component={ActiveOrdersScreen}
        options={{
          headerShown: true,
          title: 'Active Orders',
        }}
      />
    </Stack.Navigator>
  )
}
