"use client"

import React, { useState, useEffect, useMemo } from 'react'
import { View, ScrollView, Pressable } from 'react-native'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { Text, Button } from '@gluestack-ui/themed'
import { useBreakpointValue } from '@/hooks/useBreakpointValue'

// Types
export interface ContactFormProps {
  data?: any[]
  loading?: boolean
  onAction?: (action: string, data?: any) => void
  className?: string
  variant?: 'default' | 'compact' | 'detailed'
  validationEnabled?: boolean
  submissionEnabled?: boolean
  fileuploadEnabled?: boolean
  captchaEnabled?: boolean
}

// Styles
const containerStyles = tva({
  base: "flex-1 bg-background-0",
  variants: {
    variant: {
      default: "p-4",
      compact: "p-2", 
      detailed: "p-6",
    },
  },
})

const headerStyles = tva({
  base: "flex-row items-center justify-between mb-4",
})

const contentStyles = tva({
  base: "flex-1",
  variants: {
    variant: {
      compact: "space-y-2",
      detailed: "space-y-6",
    },
  },
})

/**
 * Contact form with validation and submission handling
 * 
 * Features:
 * - validation
 * - submission
 * - file upload
 * - captcha
 */
export function ContactForm({
  data = [],
  loading = false,
  onAction,
  className,
  variant = 'default',
  ...props
}: ContactFormProps) {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<Record<string, any>>({})

  // Responsive configuration
  const columns = useBreakpointValue({
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
  })

  // Filtered and processed data
  const processedData = useMemo(() => {
    let filtered = data

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(item => 
        JSON.stringify(item).toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(item => item[key] === value)
      }
    })

    return filtered
  }, [data, searchQuery, filters])

  // Handle actions
  const handleAction = (action: string, actionData?: any) => {
    onAction?.(action, actionData)
  }

  // Render header
  const renderHeader = () => (
    <View className={headerStyles()}>
      <View>
        <Text className="text-xl font-bold text-typography-900">
          Contact Form
        </Text>
        <Text className="text-sm text-typography-600">
          {processedData.length} items
        </Text>
      </View>
      
      <View className="flex-row items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onPress={() => handleAction('refresh')}
        >
          <Text>Refresh</Text>
        </Button>
        
        <Button
          size="sm"
          onPress={() => handleAction('create')}
        >
          <Text>Add New</Text>
        </Button>
      </View>
    </View>
  )

  // Render content
  const renderContent = () => {
    if (loading) {
      return (
        <View className="flex-1 items-center justify-center">
          <Text className="text-typography-600">Loading...</Text>
        </View>
      )
    }

    if (processedData.length === 0) {
      return (
        <View className="flex-1 items-center justify-center">
          <Text className="text-typography-600 mb-4">No items found</Text>
          <Button onPress={() => handleAction('create')}>
            <Text>Create First Item</Text>
          </Button>
        </View>
      )
    }

    return (
      <ScrollView className="flex-1">
        <View className="flex-row flex-wrap -mx-2">
          {processedData.map((item, index) => (
            <View
              key={item.id || index}
              className="bg-background-0 border border-outline-200 rounded-lg p-4 mx-2 mb-4"
              style={{ width: `${100 / columns}%` }}
            >
              <Text className="font-medium text-typography-900 mb-2">
                {item.title || item.name || `Item ${index + 1}`}
              </Text>
              <Text className="text-sm text-typography-600 mb-3">
                {item.description || 'No description available'}
              </Text>
              
              <View className="flex-row items-center justify-between">
                <Text className="text-xs text-typography-500">
                  {item.status || 'Active'}
                </Text>
                <Pressable
                  onPress={() => handleAction('view', item)}
                  className="px-3 py-1 bg-primary-100 rounded"
                >
                  <Text className="text-xs text-primary-700">View</Text>
                </Pressable>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    )
  }

  return (
    <View className={containerStyles({ variant, class: className })}>
      {renderHeader()}
      <View className={contentStyles({ variant })}>
        {renderContent()}
      </View>
    </View>
  )
}

// Export variants
export const ContactFormCompact = (props: Omit<ContactFormProps, 'variant'>) => (
  <ContactForm {...props} variant="compact" />
)

export const ContactFormDetailed = (props: Omit<ContactFormProps, 'variant'>) => (
  <ContactForm {...props} variant="detailed" />
)
