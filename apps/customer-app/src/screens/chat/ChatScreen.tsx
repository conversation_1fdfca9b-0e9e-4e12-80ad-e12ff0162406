import React, { useState, useEffect } from 'react'
import { View, Text, TouchableOpacity, Alert } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { ArrowLeftIcon, PhoneIcon, VideoIcon, MoreVerticalIcon } from 'lucide-react-native'

import { useTheme } from '../../hooks/useTheme'
import { useAuthStore } from '../../stores/authStore'
import { LoadingSpinner } from '../../components/LoadingSpinner'

// Import UI components from the library
// import { ChatInterface } from '@hvppyplug/ui-components-v2'

// Temporary placeholder component
const ChatInterface = ({ messages, onSendMessage, currentUserId }: any) => {
  return (
    <View className="flex-1 bg-white">
      <Text className="text-center p-4">Chat Interface Placeholder</Text>
    </View>
  )
}

import type { RootStackScreenProps } from '../../types/navigation'

type Props = RootStackScreenProps<'Chat'>

export function ChatScreen({ navigation, route }: Props) {
  const { orderId, providerId } = route.params
  const { colors, typography } = useTheme()
  const { user } = useAuthStore()

  const [messages, setMessages] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [providerInfo, setProviderInfo] = useState<any>(null)

  useEffect(() => {
    loadChatData()
  }, [orderId, providerId])

  const loadChatData = async () => {
    try {
      // Simulate loading chat data
      setIsLoading(true)

      // Mock provider info
      const mockProvider = {
        id: providerId,
        name: 'John\'s Home Services',
        avatar: null,
        isOnline: true,
        lastSeen: new Date().toISOString(),
      }

      // Mock messages
      const mockMessages = [
        {
          id: '1',
          text: 'Hi! I\'ve received your booking request. I can start the service at the scheduled time.',
          senderId: providerId,
          senderName: 'John\'s Home Services',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          type: 'text'
        },
        {
          id: '2',
          text: 'Great! I\'ll be ready. Do you need any special tools or should I prepare anything?',
          senderId: user?.id || 'customer',
          senderName: user?.name || 'You',
          timestamp: new Date(Date.now() - 3000000).toISOString(),
          type: 'text'
        },
        {
          id: '3',
          text: 'No need to prepare anything. I\'ll bring all the necessary tools. See you soon!',
          senderId: providerId,
          senderName: 'John\'s Home Services',
          timestamp: new Date(Date.now() - 1800000).toISOString(),
          type: 'text'
        }
      ]

      setProviderInfo(mockProvider)
      setMessages(mockMessages)
    } catch (error) {
      Alert.alert('Error', 'Failed to load chat')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendMessage = (message: string) => {
    const newMessage = {
      id: Date.now().toString(),
      text: message,
      senderId: user?.id || 'customer',
      senderName: user?.name || 'You',
      timestamp: new Date().toISOString(),
      type: 'text'
    }

    setMessages(prev => [...prev, newMessage])

    // In a real app, send to Appwrite real-time
    // AppwriteService.sendMessage(orderId, newMessage)
  }

  const handleCall = () => {
    Alert.alert('Call Provider', 'This feature will be available soon.')
  }

  const handleVideoCall = () => {
    Alert.alert('Video Call', 'This feature will be available soon.')
  }

  if (isLoading) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
        <LoadingSpinner message="Loading chat..." />
      </SafeAreaView>
    )
  }

  const renderHeader = () => (
    <View style={{
      backgroundColor: colors.background.primary,
      paddingTop: 50,
      paddingBottom: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <ArrowLeftIcon size={24} color={colors.text.primary} />
        </TouchableOpacity>

        <View style={{
          width: 40,
          height: 40,
          borderRadius: 20,
          backgroundColor: colors.background.tertiary,
          marginLeft: 12,
          marginRight: 12,
        }} />

        <View style={{ flex: 1 }}>
          <Text style={[
            typography.body.medium,
            { color: colors.text.primary, fontWeight: '600' }
          ]}>
            {providerInfo?.name}
          </Text>
          <Text style={[
            typography.body.small,
            { color: providerInfo?.isOnline ? colors.success[600] : colors.text.secondary }
          ]}>
            {providerInfo?.isOnline ? 'Online' : `Last seen ${new Date(providerInfo?.lastSeen).toLocaleTimeString()}`}
          </Text>
        </View>
      </View>

      <View style={{ flexDirection: 'row', gap: 12 }}>
        <TouchableOpacity
          style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 20,
            padding: 8,
          }}
          onPress={handleCall}
        >
          <PhoneIcon size={20} color={colors.text.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 20,
            padding: 8,
          }}
          onPress={handleVideoCall}
        >
          <VideoIcon size={20} color={colors.text.primary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            backgroundColor: colors.background.secondary,
            borderRadius: 20,
            padding: 8,
          }}
        >
          <MoreVerticalIcon size={20} color={colors.text.primary} />
        </TouchableOpacity>
      </View>
    </View>
  )

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.primary }}>
      {renderHeader()}

      <ChatInterface
        data={messages}
        loading={false}
        onAction={(action, data) => {
          if (action === 'send_message') {
            handleSendMessage(data.message)
          }
        }}
        variant="default"
        messagesEnabled={true}
        typingEnabled={true}
        filesharingEnabled={true}
        emojiEnabled={true}
        reactionsEnabled={true}
      />
    </SafeAreaView>
  )
}
