"use client"

import React, { useState } from 'react'
import { <PERSON>, ScrollView, Switch } from 'react-native'
import { Text, Button } from '@gluestack-ui/themed'
import { SettingsIcon, NotificationIcon, PlusIcon, BarChartIcon, MessageSquareIcon } from 'lucide-react-native'

// Import components from the UI package
import { NavigationLayout } from '../../../layouts/NavigationLayout'
import { ResponsiveGrid } from '../../../layouts/ResponsiveGrid'
import { DashboardWidget } from '../../../business/DashboardWidget'
import { SearchableList } from '../../../data/SearchableList'
import { OrderCard, type Order } from '../../../business/OrderCard'
import { type Vendor } from '../../../business/VendorCard'

// Types
export interface VendorStats {
  todayOrders: number
  todayRevenue: number
  avgRating: number
  activeMenuItems: number
  totalMenuItems: number
  ordersChange: number
  revenueChange: number
}

export interface QuickAction {
  id: string
  title: string
  icon: React.ComponentType<any>
  onPress: () => void
}

export interface VendorDashboardScreenProps {
  vendor: Vendor
  stats: VendorStats
  recentOrders: Order[]
  onNavigate: (route: string, params?: any) => void
  onToggleStoreStatus: (isOpen: boolean) => void
  onAcceptOrder: (orderId: string) => void
  onDeclineOrder: (orderId: string) => void
  loading?: boolean
}

/**
 * Vendor App Dashboard Screen
 * 
 * Features:
 * - Store status toggle
 * - Key metrics display
 * - Recent orders management
 * - Quick action buttons
 * - Real-time order notifications
 */
export function VendorDashboardScreen({
  vendor,
  stats,
  recentOrders,
  onNavigate,
  onToggleStoreStatus,
  onAcceptOrder,
  onDeclineOrder,
  loading = false,
}: VendorDashboardScreenProps) {
  const [storeOpen, setStoreOpen] = useState(vendor.isOpen)

  const handleToggleStore = (value: boolean) => {
    setStoreOpen(value)
    onToggleStoreStatus(value)
  }

  const quickActions: QuickAction[] = [
    {
      id: 'add-menu-item',
      title: 'Add Menu Item',
      icon: PlusIcon,
      onPress: () => onNavigate('/menu/add'),
    },
    {
      id: 'view-analytics',
      title: 'View Analytics',
      icon: BarChartIcon,
      onPress: () => onNavigate('/analytics'),
    },
    {
      id: 'customer-messages',
      title: 'Messages',
      icon: MessageSquareIcon,
      onPress: () => onNavigate('/messages'),
    },
  ]

  const renderStoreStatus = () => (
    <View className="p-4 bg-background-50 border-b border-outline-200">
      <View className="flex-row items-center justify-between">
        <View className="flex-1">
          <Text className="text-lg font-semibold text-typography-900">
            Store Status
          </Text>
          <Text className="text-sm text-typography-600 mt-1">
            {storeOpen ? 'Currently accepting orders' : 'Store is closed'}
          </Text>
        </View>
        <Switch
          value={storeOpen}
          onValueChange={handleToggleStore}
          trackColor={{ false: '#767577', true: '#81b0ff' }}
          thumbColor={storeOpen ? '#007AFF' : '#f4f3f4'}
        />
      </View>
    </View>
  )

  const renderKeyMetrics = () => (
    <View className="p-4">
      <Text className="text-lg font-semibold text-typography-900 mb-4">
        Today's Performance
      </Text>
      <ResponsiveGrid columns={{ xs: 2, sm: 4 }}>
        <DashboardWidget
          data={{
            title: "Orders",
            value: stats.todayOrders,
            trend: {
              value: stats.ordersChange,
              direction: stats.ordersChange >= 0 ? 'up' : 'down',
              label: 'vs yesterday'
            }
          }}
          onPress={() => onNavigate('/orders')}
          size="medium"
        />
        <DashboardWidget
          data={{
            title: "Revenue",
            value: `R${stats.todayRevenue.toFixed(2)}`,
            trend: {
              value: stats.revenueChange,
              direction: stats.revenueChange >= 0 ? 'up' : 'down',
              label: 'vs yesterday'
            }
          }}
          onPress={() => onNavigate('/earnings')}
          size="medium"
        />
        <DashboardWidget
          data={{
            title: "Rating",
            value: stats.avgRating.toFixed(1),
            subtitle: `${vendor.reviewCount} reviews`,
            status: stats.avgRating >= 4.5 ? 'success' : stats.avgRating >= 4.0 ? 'info' : 'warning'
          }}
          onPress={() => onNavigate('/reviews')}
          size="medium"
        />
        <DashboardWidget
          data={{
            title: "Menu Items",
            value: `${stats.activeMenuItems}/${stats.totalMenuItems}`,
            subtitle: 'active',
            status: stats.activeMenuItems === stats.totalMenuItems ? 'success' : 'warning'
          }}
          onPress={() => onNavigate('/menu')}
          size="medium"
        />
      </ResponsiveGrid>
    </View>
  )

  const renderRecentOrders = () => (
    <View className="p-4">
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-lg font-semibold text-typography-900">
          Recent Orders
        </Text>
        <Button
          variant="outline"
          size="sm"
          onPress={() => onNavigate('/orders')}
        >
          <Text>View All</Text>
        </Button>
      </View>

      {loading ? (
        <View className="p-4">
          <Text className="text-center text-typography-600">Loading orders...</Text>
        </View>
      ) : recentOrders.length === 0 ? (
        <View className="p-4 bg-background-50 rounded-lg">
          <Text className="text-center text-typography-600">
            No recent orders
          </Text>
          <Text className="text-center text-typography-500 text-sm mt-1">
            Orders will appear here when customers place them
          </Text>
        </View>
      ) : (
        <SearchableList
          data={recentOrders.slice(0, 5)}
          renderItem={(order) => (
            <OrderCard
              key={order.id}
              order={order}
              variant="vendor"
              onAccept={() => onAcceptOrder(order.id)}
              onDecline={() => onDeclineOrder(order.id)}
              onPress={() => onNavigate('/order', { orderId: order.id })}
              showActions={order.status === 'pending'}
            />
          )}
          emptyMessage="No orders found"
          showSearch={false}
        />
      )}
    </View>
  )

  const renderQuickActions = () => (
    <View className="p-4">
      <Text className="text-lg font-semibold text-typography-900 mb-4">
        Quick Actions
      </Text>
      <ResponsiveGrid columns={{ xs: 2, sm: 3 }}>
        {quickActions.map((action) => (
          <Button
            key={action.id}
            variant="outline"
            onPress={action.onPress}
            className="p-4 h-auto flex-col items-center justify-center"
          >
            <action.icon size={24} className="text-primary-600 mb-2" />
            <Text className="text-center text-sm font-medium">
              {action.title}
            </Text>
          </Button>
        ))}
      </ResponsiveGrid>
    </View>
  )

  return (
    <NavigationLayout
      variant="tabs"
      header={{
        title: vendor.name,
        actions: [
          {
            icon: NotificationIcon,
            onPress: () => onNavigate('/notifications'),
            label: 'Notifications',
          },
          {
            icon: SettingsIcon,
            onPress: () => onNavigate('/settings'),
            label: 'Settings',
          },
        ],
      }}
      tabs={{
        items: [
          { key: 'dashboard', label: 'Dashboard', icon: 'home' },
          { key: 'orders', label: 'Orders', icon: 'list' },
          { key: 'menu', label: 'Menu', icon: 'grid' },
          { key: 'analytics', label: 'Analytics', icon: 'bar-chart' },
          { key: 'profile', label: 'Profile', icon: 'user' },
        ],
        activeKey: 'dashboard',
        onTabPress: (key) => onNavigate(`/${key}`),
      }}
    >
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {renderStoreStatus()}
        {renderKeyMetrics()}
        {renderRecentOrders()}
        {renderQuickActions()}
      </ScrollView>
    </NavigationLayout>
  )
}

// Export with default props for easier usage
export const VendorDashboard = (props: VendorDashboardScreenProps) => (
  <VendorDashboardScreen {...props} />
)
